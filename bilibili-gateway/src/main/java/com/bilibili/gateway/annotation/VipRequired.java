package com.bilibili.gateway.annotation;

import java.lang.annotation.*;

/**
 * VIP权限注解
 * 用于标记需要VIP权限的接口
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface VipRequired {

    /**
     * 所需VIP等级
     * 0: 不需要VIP
     * 1: 月会员
     * 2: 年会员
     */
    int level() default 1;

    /**
     * 所需特权
     */
    String privilege() default "";

    /**
     * 错误提示信息
     */
    String message() default "该功能需要开通大会员";
}
