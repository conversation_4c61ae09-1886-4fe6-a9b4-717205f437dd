package com.bilibili.gateway.config;

// import com.bilibili.gateway.filter.VipAuthFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import reactor.core.publisher.Mono;

/**
 * 网关配置类
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Configuration
// @RequiredArgsConstructor  // 暂时注释掉，因为VipAuthFilter还未实现
public class GatewayConfig {

    // private final VipAuthFilter vipAuthFilter;  // 暂时注释掉

    /**
     * 限流Key解析器 - 基于用户ID限流
     */
    @Bean
    @Primary
    public KeyResolver userKeyResolver() {
        return exchange -> {
            // 优先使用用户ID进行限流
            String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
            if (userId != null) {
                return Mono.just("user:" + userId);
            }
            
            // 如果没有用户ID，使用IP地址
            String clientIp = getClientIp(exchange);
            return Mono.just("ip:" + clientIp);
        };
    }

    /**
     * IP限流Key解析器
     */
    @Bean
    public KeyResolver ipKeyResolver() {
        return exchange -> Mono.just(getClientIp(exchange));
    }

    /**
     * 服务限流Key解析器
     */
    @Bean
    public KeyResolver serviceKeyResolver() {
        return exchange -> {
            String path = exchange.getRequest().getURI().getPath();
            if (path.startsWith("/api/v1/auth")) {
                return Mono.just("service:auth");
            } else if (path.startsWith("/api/v1/user")) {
                return Mono.just("service:user");
            } else if (path.startsWith("/api/v1/ai")) {
                return Mono.just("service:ai");
            }
            return Mono.just("service:unknown");
        };
    }

    /**
     * Redis限流器配置
     */
    @Bean
    public RedisRateLimiter redisRateLimiter() {
        return new RedisRateLimiter(100, 200, 1);
    }

    /**
     * 动态路由配置（如果需要）
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
                // 健康检查路由
                .route("health-check", r -> r
                        .path("/health", "/actuator/health")
                        .uri("http://localhost:8080"))
                
                // 文档路由 - 确保Swagger文档能正常访问
                .route("docs", r -> r
                        .path("/doc.html", "/swagger-ui/**", "/v3/api-docs/**", "/webjars/**")
                        .filters(f -> f
                                .addRequestHeader("X-Forwarded-Proto", "http")
                                .addResponseHeader("Cache-Control", "no-cache"))
                        .uri("http://localhost:8080"))
                
                .build();
    }

    /**
     * 获取客户端真实IP
     */
    private String getClientIp(org.springframework.web.server.ServerWebExchange exchange) {
        String xForwardedFor = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return exchange.getRequest().getRemoteAddress() != null ? 
                exchange.getRequest().getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }
}
