package com.bilibili.gateway.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.cloud.gateway.route.RouteDefinitionLocator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 网关Swagger配置
 * 用于聚合所有微服务的API文档
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Configuration
public class SwaggerConfig {

    /**
     * 认证服务API文档分组
     */
    @Bean
    public GroupedOpenApi authApi() {
        return GroupedOpenApi.builder()
                .group("auth-service")
                .pathsToMatch("/api/v1/auth/**")
                .build();
    }

    /**
     * 用户服务API文档分组
     */
    @Bean
    public GroupedOpenApi userApi() {
        return GroupedOpenApi.builder()
                .group("user-service")
                .pathsToMatch("/api/v1/user/**")
                .build();
    }

    /**
     * 网关服务API文档分组
     */
    @Bean
    public GroupedOpenApi gatewayApi() {
        return GroupedOpenApi.builder()
                .group("gateway-service")
                .pathsToMatch("/actuator/**", "/health/**")
                .build();
    }
}
