package com.bilibili.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * JWT配置属性
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.jwt")
public class JwtProperties {

    /**
     * JWT密钥
     */
    private String secret = "bilibiliAuthSecretKey2024SunsetTeamVeryLongSecretKeyForJWTTokenHS512";

    /**
     * 访问令牌过期时间（毫秒）
     */
    private Long accessTokenExpiration = 86400000L; // 24小时

    /**
     * 刷新令牌过期时间（毫秒）
     */
    private Long refreshTokenExpiration = 604800000L; // 7天

    /**
     * 发行者
     */
    private String issuer = "bilibili-auth";

    /**
     * 受众
     */
    private String audience = "bilibili-users";
}
