package com.bilibili.gateway.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * VIP权限验证过滤器
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Component
public class VipAuthFilter extends AbstractGatewayFilterFactory<VipAuthFilter.Config> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    public VipAuthFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            String path = request.getURI().getPath();

            log.debug("VIP权限验证: path={}, requiredLevel={}", path, config.getRequiredLevel());

            // 检查是否需要VIP权限
            if (config.getRequiredLevel() <= 0) {
                return chain.filter(exchange);
            }

            // 从请求头获取VIP信息
            List<String> vipHeaders = request.getHeaders().get("X-Vip-Info");
            if (vipHeaders == null || vipHeaders.isEmpty()) {
                log.warn("VIP权限验证失败: 缺少VIP信息头");
                return unauthorizedResponse(exchange, "缺少VIP信息");
            }

            try {
                // 解析VIP信息
                String vipInfoJson = vipHeaders.get(0);
                Map<String, Object> vipInfo = objectMapper.readValue(vipInfoJson, Map.class);

                Boolean isVip = (Boolean) vipInfo.get("isVip");
                Integer vipType = (Integer) vipInfo.get("vipType");
                Integer vipStatus = (Integer) vipInfo.get("vipStatus");
                Long vipEndTime = vipInfo.get("vipEndTime") != null ? 
                    ((Number) vipInfo.get("vipEndTime")).longValue() : null;

                // 验证VIP状态
                if (!isVip || vipStatus != 1) {
                    log.warn("VIP权限验证失败: 用户非VIP或状态无效");
                    return unauthorizedResponse(exchange, "该功能需要开通大会员");
                }

                // 验证VIP等级
                if (vipType < config.getRequiredLevel()) {
                    String message = config.getRequiredLevel() == 2 ? 
                        "该功能需要年会员权限" : "该功能需要大会员权限";
                    log.warn("VIP权限验证失败: VIP等级不足, required={}, actual={}", 
                        config.getRequiredLevel(), vipType);
                    return unauthorizedResponse(exchange, message);
                }

                // 验证VIP是否过期
                if (vipEndTime != null && vipEndTime < System.currentTimeMillis() / 1000) {
                    log.warn("VIP权限验证失败: VIP已过期");
                    return unauthorizedResponse(exchange, "大会员已过期，请续费");
                }

                // 验证特定权限
                if (config.getRequiredPrivilege() != null && !config.getRequiredPrivilege().isEmpty()) {
                    if (!hasPrivilege(vipInfo, config.getRequiredPrivilege())) {
                        log.warn("VIP权限验证失败: 缺少特定权限 {}", config.getRequiredPrivilege());
                        return unauthorizedResponse(exchange, "您的会员等级不支持该功能");
                    }
                }

                log.debug("VIP权限验证通过: vipType={}", vipType);
                return chain.filter(exchange);

            } catch (Exception e) {
                log.error("VIP权限验证异常: {}", e.getMessage(), e);
                return unauthorizedResponse(exchange, "VIP信息解析失败");
            }
        };
    }

    private boolean hasPrivilege(Map<String, Object> vipInfo, String privilege) {
        Boolean isVip = (Boolean) vipInfo.get("isVip");
        Integer vipType = (Integer) vipInfo.get("vipType");

        if (!isVip) {
            return false;
        }

        switch (privilege) {
            case "ad_free":
            case "hd_quality":
            case "offline_download":
                return true;
            case "exclusive_content":
                return vipType != null && vipType == 2; // 年会员专享
            default:
                return false;
        }
    }

    private Mono<Void> unauthorizedResponse(org.springframework.web.server.ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.FORBIDDEN);
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);

        Map<String, Object> result = new HashMap<>();
        result.put("code", 403);
        result.put("message", message);
        result.put("data", null);
        result.put("timestamp", System.currentTimeMillis());

        try {
            String body = objectMapper.writeValueAsString(result);
            DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (Exception e) {
            log.error("写入响应失败", e);
            return response.setComplete();
        }
    }

    public static class Config {
        private int requiredLevel = 1;
        private String requiredPrivilege;
        private String message = "该功能需要开通大会员";

        public int getRequiredLevel() { return requiredLevel; }
        public void setRequiredLevel(int requiredLevel) { this.requiredLevel = requiredLevel; }

        public String getRequiredPrivilege() { return requiredPrivilege; }
        public void setRequiredPrivilege(String requiredPrivilege) { this.requiredPrivilege = requiredPrivilege; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
