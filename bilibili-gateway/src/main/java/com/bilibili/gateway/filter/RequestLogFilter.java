package com.bilibili.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 请求日志过滤器
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Component
public class RequestLogFilter implements GlobalFilter, Ordered {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String startTime = LocalDateTime.now().format(FORMATTER);
        
        // 记录请求开始
        String requestId = generateRequestId();
        String clientIp = getClientIp(exchange);
        String userAgent = request.getHeaders().getFirst("User-Agent");
        String userId = request.getHeaders().getFirst("X-User-Id");
        
        log.info("🚀 [{}] 请求开始 - {} {} | IP: {} | User: {} | UA: {}", 
                requestId, request.getMethod(), request.getURI(), 
                clientIp, userId != null ? userId : "anonymous", 
                userAgent != null ? userAgent.substring(0, Math.min(userAgent.length(), 50)) : "unknown");

        // 添加请求ID到请求头
        ServerHttpRequest newRequest = request.mutate()
                .header("X-Request-Id", requestId)
                .header("X-Request-Time", startTime)
                .build();

        ServerWebExchange newExchange = exchange.mutate().request(newRequest).build();
        
        long startTimeMillis = System.currentTimeMillis();
        
        return chain.filter(newExchange).doFinally(signalType -> {
            long duration = System.currentTimeMillis() - startTimeMillis;
            int statusCode = exchange.getResponse().getStatusCode() != null ? 
                    exchange.getResponse().getStatusCode().value() : 0;
            
            String logLevel = getLogLevel(statusCode, duration);
            String statusEmoji = getStatusEmoji(statusCode);
            
            if ("ERROR".equals(logLevel)) {
                log.error("❌ [{}] 请求完成 - {} {} | 状态: {}{} | 耗时: {}ms | IP: {}", 
                        requestId, request.getMethod(), request.getURI(), 
                        statusEmoji, statusCode, duration, clientIp);
            } else if ("WARN".equals(logLevel)) {
                log.warn("⚠️ [{}] 请求完成 - {} {} | 状态: {}{} | 耗时: {}ms | IP: {}", 
                        requestId, request.getMethod(), request.getURI(), 
                        statusEmoji, statusCode, duration, clientIp);
            } else {
                log.info("✅ [{}] 请求完成 - {} {} | 状态: {}{} | 耗时: {}ms | IP: {}", 
                        requestId, request.getMethod(), request.getURI(), 
                        statusEmoji, statusCode, duration, clientIp);
            }
        });
    }

    private String generateRequestId() {
        return String.valueOf(System.currentTimeMillis()) + 
               String.valueOf((int)(Math.random() * 1000));
    }

    private String getClientIp(ServerWebExchange exchange) {
        String xForwardedFor = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return exchange.getRequest().getRemoteAddress() != null ? 
                exchange.getRequest().getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    private String getLogLevel(int statusCode, long duration) {
        if (statusCode >= 500) {
            return "ERROR";
        } else if (statusCode >= 400 || duration > 5000) {
            return "WARN";
        } else {
            return "INFO";
        }
    }

    private String getStatusEmoji(int statusCode) {
        if (statusCode >= 200 && statusCode < 300) {
            return "✅";
        } else if (statusCode >= 300 && statusCode < 400) {
            return "🔄";
        } else if (statusCode >= 400 && statusCode < 500) {
            return "❌";
        } else if (statusCode >= 500) {
            return "💥";
        } else {
            return "❓";
        }
    }

    @Override
    public int getOrder() {
        return -200; // 最高优先级，第一个执行
    }
}
