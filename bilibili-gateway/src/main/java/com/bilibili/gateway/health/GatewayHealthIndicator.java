package com.bilibili.gateway.health;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网关健康检查指示器
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Component
@RequiredArgsConstructor
public class GatewayHealthIndicator implements HealthIndicator {

    private final DiscoveryClient discoveryClient;

    @Override
    public Health health() {
        try {
            Map<String, Object> details = new HashMap<>();
            
            // 检查服务注册中心连接
            List<String> services = discoveryClient.getServices();
            details.put("discoveryClient", "UP");
            details.put("registeredServices", services.size());
            details.put("services", services);
            
            // 检查关键服务状态
            Map<String, String> serviceStatus = new HashMap<>();
            checkServiceHealth("auth-service", serviceStatus);
            checkServiceHealth("user-service", serviceStatus);
            checkServiceHealth("aiassistant-service", serviceStatus);
            
            details.put("serviceStatus", serviceStatus);
            
            // 判断整体健康状态
            boolean allServicesUp = serviceStatus.values().stream()
                    .allMatch("UP"::equals);
            
            if (allServicesUp) {
                details.put("status", "所有服务正常");
                return Health.up().withDetails(details).build();
            } else {
                details.put("status", "部分服务异常");
                return Health.down().withDetails(details).build();
            }
            
        } catch (Exception e) {
            Map<String, Object> details = new HashMap<>();
            details.put("error", e.getMessage());
            details.put("status", "健康检查异常");
            return Health.down().withDetails(details).build();
        }
    }

    private void checkServiceHealth(String serviceName, Map<String, String> serviceStatus) {
        try {
            List<org.springframework.cloud.client.ServiceInstance> instances = 
                    discoveryClient.getInstances(serviceName);
            
            if (instances != null && !instances.isEmpty()) {
                serviceStatus.put(serviceName, "UP (" + instances.size() + " instances)");
            } else {
                serviceStatus.put(serviceName, "DOWN (no instances)");
            }
        } catch (Exception e) {
            serviceStatus.put(serviceName, "ERROR: " + e.getMessage());
        }
    }
}
