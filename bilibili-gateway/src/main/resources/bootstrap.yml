# Gateway服务Bootstrap配置
# 此配置在application.yml之前加载，确保Nacos配置正确导入

spring:
  application:
    name: bilibili-gateway
  profiles:
    active: prod
  cloud:
    nacos:
      # Nacos Server地址
      server-addr: ***********:8848
      # Nacos配置
      config:
        # 配置文件格式
        file-extension: yml
        # 配置分组
        group: DEFAULT_GROUP
        # 命名空间
        namespace: public
        # 配置刷新
        refresh-enabled: true
        # 配置导入
        import:
          - optional:nacos:bilibili-gateway-service.yml
          - optional:nacos:shared-spring.yaml
          - optional:nacos:shared-redis.yaml
          - optional:nacos:shared-security.yaml
          - optional:nacos:shared-monitoring.yaml
          - optional:nacos:shared-swagger.yaml
      # Nacos服务发现
      discovery:
        # 服务发现地址
        server-addr: ***********:8848
        # 命名空间
        namespace: public
        # 分组
        group: DEFAULT_GROUP
        # 集群名称
        cluster-name: DEFAULT
        # 服务名称
        service: ${spring.application.name}
        # 是否注册到Nacos
        register-enabled: true
        # 心跳间隔（毫秒）
        heart-beat-interval: 5000
        # 心跳超时时间（毫秒）
        heart-beat-timeout: 15000
        # IP删除超时时间（毫秒）
        ip-delete-timeout: 30000

# 基础配置
server:
  port: 8080
