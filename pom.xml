<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 项目基本信息 -->
    <groupId>com.bilibili</groupId>
    <artifactId>bilibili-parent</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>Bilibili Microservices Parent</name>
    <description>Bilibili微服务架构父工程，统一管理依赖版本和构建配置</description>
    <url>https://github.com/bilibili/bilibili-microservices</url>

    <!-- 开发者信息 -->
    <developers>
        <developer>
            <name>Bilibili Team</name>
            <email><EMAIL></email>
            <organization>Bilibili</organization>
        </developer>
    </developers>

    <!-- 子模块 -->
    <modules>
        <module>bilibili-common</module>
        <module>bilibili-api</module>
        <module>bilibili-auth</module>
        <module>bilibili-user</module>
        <module>bilibili-gateway</module>
        <module>bilibili-aiassistant</module>
    </modules>

    <!-- 继承 Spring Boot 父工程 -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.0</version>
    </parent>


    <properties>
        <!-- ==================== 基础配置 ==================== -->
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.test.skip>false</maven.test.skip>

        <!-- ==================== Spring生态版本 ==================== -->
        <spring-boot.version>3.2.0</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        <spring-cloud-alibaba.version>2022.0.0.0</spring-cloud-alibaba.version>

        <!-- ==================== 数据库相关 ==================== -->
        <mysql.version>8.0.33</mysql.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <mybatis-spring.version>3.0.3</mybatis-spring.version>
        <druid.version>1.2.20</druid.version>

        <!-- ==================== 安全认证 ==================== -->
        <jwt.version>0.12.3</jwt.version>

        <!-- ==================== 工具类库 ==================== -->
        <hutool.version>5.8.25</hutool.version>
        <fastjson2.version>2.0.43</fastjson2.version>
        <lombok.version>1.18.30</lombok.version>

        <!-- ==================== API文档 ==================== -->
        <springdoc.version>2.5.0</springdoc.version>
        <knife4j.version>4.4.0</knife4j.version>
        <swagger.version>1.6.9</swagger.version>
        <swagger-v3.version>2.2.21</swagger-v3.version>

        <!-- ==================== 缓存相关 ==================== -->
        <redisson.version>3.24.3</redisson.version>
        <caffeine.version>3.1.8</caffeine.version>

        <!-- ==================== 搜索引擎 ==================== -->
        <elasticsearch.version>8.11.0</elasticsearch.version>

        <!-- ==================== 云服务SDK ==================== -->
        <!-- 阿里云SDK -->
        <ali.sdk.core.version>4.6.0</ali.sdk.core.version>
        <ali.sdk.kms.version>2.10.1</ali.sdk.kms.version>
        <ali.sdk.oss.version>3.10.2</ali.sdk.oss.version>
        <ali.sms.version>4.1.2</ali.sms.version>
        <ali.tea.version>0.2.8</ali.tea.version>

        <!-- 腾讯云SDK -->
        <tencent.cloud.version>3.1.515</tencent.cloud.version>
        <tencent.sdk.cos.version>5.6.89</tencent.sdk.cos.version>

        <!-- ==================== 其他组件 ==================== -->
        <jakarta-annotation.version>2.1.1</jakarta-annotation.version>
        <xxl-job.version>2.4.0</xxl-job.version>
        <seata.version>1.7.0</seata.version>

        <!-- ==================== 测试相关 ==================== -->
        <testcontainers.version>1.19.3</testcontainers.version>

        <!-- ==================== Maven插件版本 ==================== -->
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven-failsafe-plugin.version>3.2.2</maven-failsafe-plugin.version>
        <jacoco-maven-plugin.version>0.8.10</jacoco-maven-plugin.version>
    </properties>

    <!-- ==================== 依赖版本管理 ==================== -->
    <dependencyManagement>
        <dependencies>
            <!-- ==================== Spring生态BOM导入 ==================== -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- ==================== 数据库相关 ==================== -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- ==================== Jakarta注解 ==================== -->
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta-annotation.version}</version>
            </dependency>

            <!-- ==================== JWT认证 ==================== -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jwt.version}</version>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jwt.version}</version>
                <scope>runtime</scope>
            </dependency>

            <!-- ==================== 工具类库 ==================== -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- ==================== API文档 ==================== -->
            <!-- SpringDoc for WebMVC -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- SpringDoc for WebFlux (Gateway) -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- Knife4j增强文档 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- Swagger注解 -->
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger-v3.version}</version>
            </dependency>

            <!-- ==================== 云服务SDK ==================== -->
            <!-- 阿里云SDK -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${ali.sms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>tea-openapi</artifactId>
                <version>${ali.tea.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${ali.sdk.core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${ali.sdk.oss.version}</version>
            </dependency>

            <!-- 腾讯云SDK -->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencent.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${tencent.sdk.cos.version}</version>
            </dependency>

            <!-- ==================== 缓存相关 ==================== -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <!-- ==================== 其他组件 ==================== -->
            <!-- 任务调度 -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>

            <!-- ==================== 测试相关 ==================== -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>mysql</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${testcontainers.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- ==================== 内部模块版本管理 ==================== -->
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>bilibili-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>bilibili-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>bilibili-auth</artifactId>
                <version>${project.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <!-- ==================== 所有子模块共同依赖 ==================== -->
    <dependencies>
        <!-- Lombok - 所有模块都需要 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 单元测试 - 所有模块都需要 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Bootstrap配置文件支持 - 微服务必需 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- Jakarta注解 - Spring Boot 3必需 -->
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>
    </dependencies>

    <!-- ==================== 构建配置 ==================== -->
    <build>
        <pluginManagement>
            <plugins>
                <!-- Maven编译插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <fork>true</fork>
                        <parameters>true</parameters>
                        <compilerArgs>
                            <arg>-Xlint:unchecked</arg>
                            <arg>-Xlint:deprecation</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>

                <!-- Spring Boot Maven插件 -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                        <layers>
                            <enabled>true</enabled>
                        </layers>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                                <goal>build-info</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- Maven单元测试插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <skipTests>${maven.test.skip}</skipTests>
                        <includes>
                            <include>**/*Test.java</include>
                            <include>**/*Tests.java</include>
                        </includes>
                    </configuration>
                </plugin>

                <!-- Maven集成测试插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                    <configuration>
                        <includes>
                            <include>**/*IT.java</include>
                            <include>**/*IntegrationTest.java</include>
                        </includes>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>integration-test</goal>
                                <goal>verify</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- JaCoCo代码覆盖率插件 -->
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
