package com.bilibili.common.utils;

import com.bilibili.common.constants.CommonConstants;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * 日期时间工具类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public class DateUtils {

    /**
     * 标准日期时间格式化器
     */
    public static final DateTimeFormatter STANDARD_DATETIME_FORMATTER = 
            DateTimeFormatter.ofPattern(CommonConstants.DateTime.STANDARD_DATETIME_FORMAT);

    /**
     * 标准日期格式化器
     */
    public static final DateTimeFormatter STANDARD_DATE_FORMATTER = 
            DateTimeFormatter.ofPattern(CommonConstants.DateTime.STANDARD_DATE_FORMAT);

    /**
     * 标准时间格式化器
     */
    public static final DateTimeFormatter STANDARD_TIME_FORMATTER = 
            DateTimeFormatter.ofPattern(CommonConstants.DateTime.STANDARD_TIME_FORMAT);

    /**
     * ISO日期时间格式化器
     */
    public static final DateTimeFormatter ISO_DATETIME_FORMATTER = 
            DateTimeFormatter.ofPattern(CommonConstants.DateTime.ISO_DATETIME_FORMAT);

    /**
     * 获取当前时间
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }

    /**
     * 获取当前日期
     */
    public static LocalDate today() {
        return LocalDate.now();
    }

    /**
     * 获取当前时间戳（秒）
     */
    public static long currentTimestamp() {
        return Instant.now().getEpochSecond();
    }

    /**
     * 获取当前时间戳（毫秒）
     */
    public static long currentTimestampMillis() {
        return Instant.now().toEpochMilli();
    }

    /**
     * 格式化日期时间
     */
    public static String format(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(STANDARD_DATETIME_FORMATTER) : null;
    }

    /**
     * 格式化日期时间（自定义格式）
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        return dateTime != null ? dateTime.format(DateTimeFormatter.ofPattern(pattern)) : null;
    }

    /**
     * 格式化日期
     */
    public static String format(LocalDate date) {
        return date != null ? date.format(STANDARD_DATE_FORMATTER) : null;
    }

    /**
     * 格式化日期（自定义格式）
     */
    public static String format(LocalDate date, String pattern) {
        return date != null ? date.format(DateTimeFormatter.ofPattern(pattern)) : null;
    }

    /**
     * 解析日期时间字符串
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        return StringUtils.isNotEmpty(dateTimeStr) ? 
                LocalDateTime.parse(dateTimeStr, STANDARD_DATETIME_FORMATTER) : null;
    }

    /**
     * 解析日期时间字符串（自定义格式）
     */
    public static LocalDateTime parseDateTime(String dateTimeStr, String pattern) {
        return StringUtils.isNotEmpty(dateTimeStr) ? 
                LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(pattern)) : null;
    }

    /**
     * 解析日期字符串
     */
    public static LocalDate parseDate(String dateStr) {
        return StringUtils.isNotEmpty(dateStr) ? 
                LocalDate.parse(dateStr, STANDARD_DATE_FORMATTER) : null;
    }

    /**
     * 解析日期字符串（自定义格式）
     */
    public static LocalDate parseDate(String dateStr, String pattern) {
        return StringUtils.isNotEmpty(dateStr) ? 
                LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern)) : null;
    }

    /**
     * 时间戳转LocalDateTime
     */
    public static LocalDateTime fromTimestamp(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
    }

    /**
     * 时间戳转LocalDateTime（毫秒）
     */
    public static LocalDateTime fromTimestampMillis(long timestampMillis) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestampMillis), ZoneId.systemDefault());
    }

    /**
     * LocalDateTime转时间戳
     */
    public static long toTimestamp(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(ZoneId.systemDefault()).toEpochSecond() : 0;
    }

    /**
     * LocalDateTime转时间戳（毫秒）
     */
    public static long toTimestampMillis(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : 0;
    }

    /**
     * 计算两个日期之间的天数
     */
    public static long daysBetween(LocalDate startDate, LocalDate endDate) {
        return startDate != null && endDate != null ? 
                ChronoUnit.DAYS.between(startDate, endDate) : 0;
    }

    /**
     * 计算两个时间之间的小时数
     */
    public static long hoursBetween(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return startDateTime != null && endDateTime != null ? 
                ChronoUnit.HOURS.between(startDateTime, endDateTime) : 0;
    }

    /**
     * 计算两个时间之间的分钟数
     */
    public static long minutesBetween(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return startDateTime != null && endDateTime != null ? 
                ChronoUnit.MINUTES.between(startDateTime, endDateTime) : 0;
    }

    /**
     * 计算两个时间之间的秒数
     */
    public static long secondsBetween(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return startDateTime != null && endDateTime != null ? 
                ChronoUnit.SECONDS.between(startDateTime, endDateTime) : 0;
    }

    /**
     * 判断是否为今天
     */
    public static boolean isToday(LocalDate date) {
        return date != null && date.equals(LocalDate.now());
    }

    /**
     * 判断是否为今天
     */
    public static boolean isToday(LocalDateTime dateTime) {
        return dateTime != null && dateTime.toLocalDate().equals(LocalDate.now());
    }

    /**
     * 判断是否为昨天
     */
    public static boolean isYesterday(LocalDate date) {
        return date != null && date.equals(LocalDate.now().minusDays(1));
    }

    /**
     * 判断是否为本周
     */
    public static boolean isThisWeek(LocalDate date) {
        if (date == null) {
            return false;
        }
        LocalDate now = LocalDate.now();
        LocalDate startOfWeek = now.with(DayOfWeek.MONDAY);
        LocalDate endOfWeek = now.with(DayOfWeek.SUNDAY);
        return !date.isBefore(startOfWeek) && !date.isAfter(endOfWeek);
    }

    /**
     * 判断是否为本月
     */
    public static boolean isThisMonth(LocalDate date) {
        if (date == null) {
            return false;
        }
        LocalDate now = LocalDate.now();
        return date.getYear() == now.getYear() && date.getMonth() == now.getMonth();
    }

    /**
     * 判断是否为本年
     */
    public static boolean isThisYear(LocalDate date) {
        if (date == null) {
            return false;
        }
        return date.getYear() == LocalDate.now().getYear();
    }

    /**
     * 获取月初日期
     */
    public static LocalDate getFirstDayOfMonth(LocalDate date) {
        return date != null ? date.withDayOfMonth(1) : null;
    }

    /**
     * 获取月末日期
     */
    public static LocalDate getLastDayOfMonth(LocalDate date) {
        return date != null ? date.withDayOfMonth(date.lengthOfMonth()) : null;
    }

    /**
     * 获取年初日期
     */
    public static LocalDate getFirstDayOfYear(LocalDate date) {
        return date != null ? date.withDayOfYear(1) : null;
    }

    /**
     * 获取年末日期
     */
    public static LocalDate getLastDayOfYear(LocalDate date) {
        return date != null ? date.withDayOfYear(date.lengthOfYear()) : null;
    }

    /**
     * 获取友好的时间描述
     */
    public static String getFriendlyTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }

        LocalDateTime now = LocalDateTime.now();
        long seconds = ChronoUnit.SECONDS.between(dateTime, now);

        if (seconds < 60) {
            return "刚刚";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分钟前";
        } else if (seconds < 86400) {
            return (seconds / 3600) + "小时前";
        } else if (seconds < 2592000) {
            return (seconds / 86400) + "天前";
        } else if (seconds < 31536000) {
            return (seconds / 2592000) + "个月前";
        } else {
            return (seconds / 31536000) + "年前";
        }
    }
}
