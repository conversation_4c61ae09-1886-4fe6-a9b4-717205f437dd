package com.bilibili.common.cache;

/**
 * 缓存常量定义
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
public class CacheConstants {

    /**
     * 缓存名称
     */
    public static final String USER_CACHE = "user";
    public static final String USER_STATS_CACHE = "user_stats";
    public static final String USER_PROFILE_CACHE = "user_profile";
    public static final String USER_FOLLOWING_CACHE = "user_following";
    public static final String USER_FOLLOWERS_CACHE = "user_followers";

    /**
     * 缓存键前缀
     */
    public static final String USER_INFO_KEY = "user:info:";
    public static final String USER_STATS_KEY = "user:stats:";
    public static final String USER_PROFILE_KEY = "user:profile:";
    public static final String USER_FOLLOWING_KEY = "user:following:";
    public static final String USER_FOLLOWERS_KEY = "user:followers:";

    /**
     * 缓存过期时间（秒）
     */
    public static final int USER_INFO_TTL = 3600; // 1小时
    public static final int USER_STATS_TTL = 1800; // 30分钟
    public static final int USER_PROFILE_TTL = 3600; // 1小时
    public static final int USER_RELATIONSHIP_TTL = 7200; // 2小时

    /**
     * 分布式锁键前缀
     */
    public static final String LOCK_USER_UPDATE = "lock:user:update:";
    public static final String LOCK_USER_FOLLOW = "lock:user:follow:";

    /**
     * 分布式锁过期时间（秒）
     */
    public static final int LOCK_TTL = 30;

    private CacheConstants() {
        // 工具类，禁止实例化
    }
}
