package com.bilibili.common.validation;

import com.bilibili.common.utils.StringUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 手机号验证器
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public class PhoneValidator implements ConstraintValidator<Phone, String> {

    private boolean allowEmpty;

    @Override
    public void initialize(Phone constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果允许为空且值为空，则验证通过
        if (allowEmpty && StringUtils.isEmpty(value)) {
            return true;
        }

        // 如果不允许为空且值为空，则验证失败
        if (!allowEmpty && StringUtils.isEmpty(value)) {
            return false;
        }

        // 验证手机号格式
        return StringUtils.isValidPhone(value);
    }
}
