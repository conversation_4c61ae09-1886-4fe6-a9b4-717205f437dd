package com.bilibili.common.autoconfigure;

import com.bilibili.common.config.UserContextWebConfig;
import com.bilibili.common.interceptor.UserContextInterceptor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

/**
 * 用户上下文自动配置类
 * 自动注册用户上下文相关的Bean
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@Import(UserContextWebConfig.class)
public class UserContextAutoConfiguration {

    @Bean
    public UserContextInterceptor userContextInterceptor() {
        return new UserContextInterceptor();
    }
}
