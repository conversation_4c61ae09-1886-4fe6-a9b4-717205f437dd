package com.bilibili.common.context;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户上下文信息
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@Accessors(chain = true)
public class UserContext {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户角色
     */
    private String roles;

    /**
     * 请求来源
     */
    private String requestSource;

    /**
     * 请求IP
     */
    private String requestIp;

    /**
     * 请求URI
     */
    private String requestUri;
}
