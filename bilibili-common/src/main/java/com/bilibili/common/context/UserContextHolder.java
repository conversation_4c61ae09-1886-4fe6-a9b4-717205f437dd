package com.bilibili.common.context;

import lombok.extern.slf4j.Slf4j;

/**
 * 用户上下文管理器
 * 基于ThreadLocal实现，用于在请求处理过程中传递用户信息
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
public class UserContextHolder {

    private static final ThreadLocal<UserContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 设置用户上下文
     */
    public static void setContext(UserContext userContext) {
        CONTEXT_HOLDER.set(userContext);
        log.debug("设置用户上下文: userId={}, username={}",
            userContext.getUserId(), userContext.getUsername());
    }

    /**
     * 获取用户上下文
     */
    public static UserContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        UserContext context = getContext();
        return context != null ? context.getUserId() : null;
    }

    /**
     * 获取当前用户名
     */
    public static String getCurrentUsername() {
        UserContext context = getContext();
        return context != null ? context.getUsername() : null;
    }

    /**
     * 获取当前用户角色
     */
    public static String getCurrentUserRoles() {
        UserContext context = getContext();
        return context != null ? context.getRoles() : null;
    }

    /**
     * 检查是否有用户上下文
     */
    public static boolean hasContext() {
        return getContext() != null;
    }

    /**
     * 检查当前用户是否已认证
     */
    public static boolean isAuthenticated() {
        UserContext context = getContext();
        return context != null && context.getUserId() != null;
    }

    /**
     * 清除用户上下文
     */
    public static void clearContext() {
        UserContext context = CONTEXT_HOLDER.get();
        if (context != null) {
            log.debug("清除用户上下文: userId={}, username={}",
                context.getUserId(), context.getUsername());
        }
        CONTEXT_HOLDER.remove();
    }
}
