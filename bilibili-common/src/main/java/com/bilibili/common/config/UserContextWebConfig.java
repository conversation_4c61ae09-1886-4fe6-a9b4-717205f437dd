package com.bilibili.common.config;

import com.bilibili.common.interceptor.UserContextInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 注册用户上下文拦截器
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Configuration
@RequiredArgsConstructor
public class UserContextWebConfig implements WebMvcConfigurer {

    private final UserContextInterceptor userContextInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(userContextInterceptor)
                .addPathPatterns("/**")  // 拦截所有请求
                .excludePathPatterns(
                    "/health/**",           // 健康检查
                    "/actuator/**",         // Spring Boot Actuator
                    "/error",               // 错误页面
                    "/favicon.ico",         // 图标
                    "/v3/api-docs/**",      // API文档
                    "/swagger-ui/**",       // Swagger UI
                    "/doc.html",            // Knife4j文档
                    "/swagger-resources/**",
                    "/webjars/**",
                    "/static/**",           // 静态资源
                    "/public/**",
                    "/css/**",
                    "/js/**",
                    "/images/**"
                );
    }
}
