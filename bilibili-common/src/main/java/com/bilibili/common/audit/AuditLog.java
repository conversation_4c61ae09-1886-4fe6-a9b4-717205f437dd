package com.bilibili.common.audit;

import java.lang.annotation.*;

/**
 * 审计日志注解
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuditLog {

    /**
     * 操作描述
     */
    String value() default "";

    /**
     * 操作类型
     */
    OperationType type() default OperationType.OTHER;

    /**
     * 是否记录请求参数
     */
    boolean logParams() default true;

    /**
     * 是否记录返回结果
     */
    boolean logResult() default false;

    /**
     * 是否记录异常信息
     */
    boolean logException() default true;

    /**
     * 操作类型枚举
     */
    enum OperationType {
        CREATE("创建"),
        UPDATE("更新"),
        DELETE("删除"),
        QUERY("查询"),
        LOGIN("登录"),
        LOGOUT("登出"),
        FOLLOW("关注"),
        UNFOLLOW("取消关注"),
        OTHER("其他");

        private final String description;

        OperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
