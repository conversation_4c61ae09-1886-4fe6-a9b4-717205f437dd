package com.bilibili.common.exception;

/**
 * 错误码枚举
 */
public enum ErrorCode {
    SUCCESS(200, "成功"),
    PARAM_ERROR(400, "请求参数错误"),
    NOT_LOGIN(401, "用户未登录"),
    NO_PERMISSION(403, "权限不足"),
    NOT_FOUND(404, "资源不存在"),
    TOO_MANY_REQUESTS(429, "请求频率过高"),
    SERVER_ERROR(500, "服务器内部错误");

    private final int code;
    private final String message;

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}