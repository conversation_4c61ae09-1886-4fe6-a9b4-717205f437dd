package com.bilibili.common.exception;

/**
 * 业务异常
 */
public class ServiceException extends RuntimeException {
    private final int code;

    public ServiceException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
    }

    public ServiceException(String message) {
        super(message);
        this.code = 500; // 默认错误码
    }

    public ServiceException(int code, String message) {
        super(message);
        this.code = code;
    }

    public ServiceException(ErrorCode errorCode, String customMessage) {
        super(customMessage);
        this.code = errorCode.getCode();
    }

    public int getCode() {
        return code;
    }
}