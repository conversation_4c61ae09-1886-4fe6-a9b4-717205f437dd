package com.bilibili.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分页结果封装类
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分页结果")
public class PageResult<T> {

    @Schema(description = "数据列表")
    private List<T> records;

    @Schema(description = "总记录数")
    private Long total;

    @Schema(description = "当前页码")
    private Integer page;

    @Schema(description = "每页大小")
    private Integer size;

    @Schema(description = "总页数")
    private Integer pages;

    @Schema(description = "是否有下一页")
    private Boolean hasNext;

    @Schema(description = "是否有上一页")
    private Boolean hasPrevious;

    /**
     * 构建分页结果
     */
    public static <T> PageResult<T> of(List<T> records, Long total, Integer page, Integer size) {
        int pages = (int) Math.ceil((double) total / size);
        
        return PageResult.<T>builder()
                .records(records)
                .total(total)
                .page(page)
                .size(size)
                .pages(pages)
                .hasNext(page < pages)
                .hasPrevious(page > 1)
                .build();
    }

    /**
     * 空分页结果
     */
    public static <T> PageResult<T> empty(Integer page, Integer size) {
        return PageResult.<T>builder()
                .records(List.of())
                .total(0L)
                .page(page)
                .size(size)
                .pages(0)
                .hasNext(false)
                .hasPrevious(false)
                .build();
    }
}
