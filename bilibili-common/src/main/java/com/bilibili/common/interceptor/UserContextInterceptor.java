package com.bilibili.common.interceptor;

import com.bilibili.common.context.UserContext;
import com.bilibili.common.context.UserContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 用户上下文拦截器
 * 拦截进入微服务的请求，从网关传递的请求头中提取用户信息并设置到UserContext
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Component
public class UserContextInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // 从请求头中获取网关传递的用户信息
            String userId = request.getHeader("X-User-Id");
            String username = request.getHeader("X-Username");
            String roles = request.getHeader("X-User-Roles");
            String requestSource = request.getHeader("X-Request-Source");

            // 获取请求信息
            String requestIp = getClientIpAddress(request);
            String requestUri = request.getRequestURI();

            // 创建用户上下文
            UserContext userContext = new UserContext()
                .setUserId(StringUtils.hasText(userId) ? Long.valueOf(userId) : null)
                .setUsername(username)
                .setRoles(roles)
                .setRequestSource(requestSource)
                .setRequestIp(requestIp)
                .setRequestUri(requestUri);

            // 设置到ThreadLocal
            UserContextHolder.setContext(userContext);

            // 如果有用户信息，记录日志
            if (userContext.getUserId() != null) {
                log.debug("拦截器设置用户上下文: userId={}, username={}, uri={}, ip={}",
                    userContext.getUserId(), userContext.getUsername(), requestUri, requestIp);
            }

        } catch (Exception e) {
            log.warn("设置用户上下文失败", e);
            // 即使失败也要继续请求处理
        }

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 请求处理完成后清除ThreadLocal，避免内存泄漏
        try {
            UserContextHolder.clearContext();
            log.debug("清除用户上下文: {}", request.getRequestURI());
        } catch (Exception e) {
            log.warn("清除用户上下文失败", e);
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果通过了多级反向代理，X-Forwarded-For会包含多个IP，第一个为真实IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }
}
