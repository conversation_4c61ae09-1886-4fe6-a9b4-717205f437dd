package com.bilibili.common.page;

import com.bilibili.common.constants.CommonConstants;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * 分页请求参数
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Accessors(chain = true)
public class PageRequest {

    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum = CommonConstants.Page.DEFAULT_PAGE_NUM;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = CommonConstants.Page.MAX_PAGE_SIZE, message = "每页大小不能超过" + CommonConstants.Page.MAX_PAGE_SIZE)
    private Integer pageSize = CommonConstants.Page.DEFAULT_PAGE_SIZE;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 排序方向：asc/desc
     */
    private String sortOrder = "desc";

    /**
     * 搜索关键词
     */
    private String keyword;

    /**
     * 获取偏移量
     */
    public int getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 获取限制数量
     */
    public int getLimit() {
        return pageSize;
    }

    /**
     * 是否升序排序
     */
    public boolean isAscending() {
        return "asc".equalsIgnoreCase(sortOrder);
    }

    /**
     * 是否降序排序
     */
    public boolean isDescending() {
        return "desc".equalsIgnoreCase(sortOrder);
    }

    /**
     * 设置排序为升序
     */
    public PageRequest asc() {
        this.sortOrder = "asc";
        return this;
    }

    /**
     * 设置排序为降序
     */
    public PageRequest desc() {
        this.sortOrder = "desc";
        return this;
    }

    /**
     * 设置排序字段
     */
    public PageRequest sortBy(String sortBy) {
        this.sortBy = sortBy;
        return this;
    }

    /**
     * 设置搜索关键词
     */
    public PageRequest keyword(String keyword) {
        this.keyword = keyword;
        return this;
    }

    /**
     * 创建分页请求
     */
    public static PageRequest of(int pageNum, int pageSize) {
        return new PageRequest()
                .setPageNum(pageNum)
                .setPageSize(pageSize);
    }

    /**
     * 创建分页请求（带排序）
     */
    public static PageRequest of(int pageNum, int pageSize, String sortBy, String sortOrder) {
        return new PageRequest()
                .setPageNum(pageNum)
                .setPageSize(pageSize)
                .setSortBy(sortBy)
                .setSortOrder(sortOrder);
    }

    /**
     * 创建默认分页请求
     */
    public static PageRequest defaultRequest() {
        return new PageRequest();
    }
}
