package com.bilibili.common.page;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页结果
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Accessors(chain = true)
public class PageResult<T> {

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否为第一页
     */
    private Boolean isFirst;

    /**
     * 是否为最后一页
     */
    private Boolean isLast;

    /**
     * 构造方法
     */
    public PageResult() {
        this.records = Collections.emptyList();
    }

    /**
     * 构造方法
     */
    public PageResult(List<T> records, long total, int pageNum, int pageSize) {
        this.records = records != null ? records : Collections.emptyList();
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.pages = (int) Math.ceil((double) total / pageSize);
        this.hasPrevious = pageNum > 1;
        this.hasNext = pageNum < pages;
        this.isFirst = pageNum == 1;
        this.isLast = pageNum >= pages;
    }

    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(Collections.emptyList(), 0, 1, 10);
    }

    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty(int pageNum, int pageSize) {
        return new PageResult<>(Collections.emptyList(), 0, pageNum, pageSize);
    }

    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> records, long total, int pageNum, int pageSize) {
        return new PageResult<>(records, total, pageNum, pageSize);
    }

    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> records, long total, PageRequest pageRequest) {
        return new PageResult<>(records, total, pageRequest.getPageNum(), pageRequest.getPageSize());
    }

    /**
     * 转换数据类型
     */
    public <R> PageResult<R> map(Function<T, R> mapper) {
        List<R> mappedRecords = this.records.stream()
                .map(mapper)
                .collect(Collectors.toList());
        
        PageResult<R> result = new PageResult<>();
        result.setPageNum(this.pageNum);
        result.setPageSize(this.pageSize);
        result.setTotal(this.total);
        result.setPages(this.pages);
        result.setRecords(mappedRecords);
        result.setHasPrevious(this.hasPrevious);
        result.setHasNext(this.hasNext);
        result.setIsFirst(this.isFirst);
        result.setIsLast(this.isLast);
        
        return result;
    }

    /**
     * 获取记录数量
     */
    public int getSize() {
        return records != null ? records.size() : 0;
    }

    /**
     * 是否为空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 是否不为空
     */
    public boolean isNotEmpty() {
        return !isEmpty();
    }

    /**
     * 获取开始记录号
     */
    public long getStartRow() {
        return pageNum > 0 ? (long) (pageNum - 1) * pageSize + 1 : 0;
    }

    /**
     * 获取结束记录号
     */
    public long getEndRow() {
        return pageNum > 0 ? Math.min(pageNum * pageSize, total) : 0;
    }

    /**
     * 计算总页数
     */
    private void calculatePages() {
        if (pageSize > 0) {
            this.pages = (int) Math.ceil((double) total / pageSize);
        } else {
            this.pages = 0;
        }
    }

    /**
     * 计算分页状态
     */
    private void calculatePageStatus() {
        this.hasPrevious = pageNum > 1;
        this.hasNext = pageNum < pages;
        this.isFirst = pageNum == 1;
        this.isLast = pageNum >= pages;
    }

    /**
     * 设置总记录数并重新计算分页信息
     */
    public PageResult<T> setTotal(Long total) {
        this.total = total;
        calculatePages();
        calculatePageStatus();
        return this;
    }

    /**
     * 设置页码并重新计算分页信息
     */
    public PageResult<T> setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
        calculatePageStatus();
        return this;
    }

    /**
     * 设置页大小并重新计算分页信息
     */
    public PageResult<T> setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
        calculatePages();
        calculatePageStatus();
        return this;
    }
}
