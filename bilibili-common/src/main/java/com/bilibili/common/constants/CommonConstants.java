package com.bilibili.common.constants;

/**
 * 通用常量类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public class CommonConstants {

    /**
     * 系统相关常量
     */
    public static class System {
        /** 系统名称 */
        public static final String SYSTEM_NAME = "Bilibili";
        /** 默认编码 */
        public static final String DEFAULT_CHARSET = "UTF-8";
        /** 默认时区 */
        public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";
        /** 默认语言 */
        public static final String DEFAULT_LOCALE = "zh_CN";
    }

    /**
     * HTTP相关常量
     */
    public static class Http {
        /** 成功状态码 */
        public static final int SUCCESS_CODE = 200;
        /** 参数错误状态码 */
        public static final int BAD_REQUEST_CODE = 400;
        /** 未授权状态码 */
        public static final int UNAUTHORIZED_CODE = 401;
        /** 禁止访问状态码 */
        public static final int FORBIDDEN_CODE = 403;
        /** 资源不存在状态码 */
        public static final int NOT_FOUND_CODE = 404;
        /** 服务器错误状态码 */
        public static final int SERVER_ERROR_CODE = 500;
        
        /** 请求头 - 用户ID */
        public static final String HEADER_USER_ID = "X-User-Id";
        /** 请求头 - 用户名 */
        public static final String HEADER_USERNAME = "X-Username";
        /** 请求头 - 用户角色 */
        public static final String HEADER_USER_ROLES = "X-User-Roles";
        /** 请求头 - 请求来源 */
        public static final String HEADER_REQUEST_SOURCE = "X-Request-Source";
        /** 请求头 - 授权 */
        public static final String HEADER_AUTHORIZATION = "Authorization";
        /** Bearer前缀 */
        public static final String BEARER_PREFIX = "Bearer ";
    }

    /**
     * 缓存相关常量
     */
    public static class Cache {
        /** 默认缓存时间（秒） */
        public static final int DEFAULT_TTL = 3600;
        /** 短期缓存时间（秒） */
        public static final int SHORT_TTL = 300;
        /** 长期缓存时间（秒） */
        public static final int LONG_TTL = 86400;
        
        /** 用户信息缓存前缀 */
        public static final String USER_INFO_PREFIX = "user:info:";
        /** 用户权限缓存前缀 */
        public static final String USER_PERMISSION_PREFIX = "user:permission:";
        /** 验证码缓存前缀 */
        public static final String VERIFICATION_CODE_PREFIX = "verification:code:";
        /** 令牌黑名单前缀 */
        public static final String TOKEN_BLACKLIST_PREFIX = "blacklist:token:";
    }

    /**
     * 分页相关常量
     */
    public static class Page {
        /** 默认页码 */
        public static final int DEFAULT_PAGE_NUM = 1;
        /** 默认页大小 */
        public static final int DEFAULT_PAGE_SIZE = 10;
        /** 最大页大小 */
        public static final int MAX_PAGE_SIZE = 100;
        /** 页码参数名 */
        public static final String PAGE_NUM_PARAM = "pageNum";
        /** 页大小参数名 */
        public static final String PAGE_SIZE_PARAM = "pageSize";
    }

    /**
     * 日期时间相关常量
     */
    public static class DateTime {
        /** 标准日期时间格式 */
        public static final String STANDARD_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
        /** 标准日期格式 */
        public static final String STANDARD_DATE_FORMAT = "yyyy-MM-dd";
        /** 标准时间格式 */
        public static final String STANDARD_TIME_FORMAT = "HH:mm:ss";
        /** ISO日期时间格式 */
        public static final String ISO_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    }

    /**
     * 正则表达式常量
     */
    public static class Regex {
        /** 手机号正则 */
        public static final String PHONE_REGEX = "^1[3-9]\\d{9}$";
        /** 邮箱正则 */
        public static final String EMAIL_REGEX = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        /** 用户名正则（4-20位字母数字下划线） */
        public static final String USERNAME_REGEX = "^[a-zA-Z0-9_]{4,20}$";
        /** 密码正则（8-20位，包含字母和数字） */
        public static final String PASSWORD_REGEX = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,20}$";
        /** 身份证号正则 */
        public static final String ID_CARD_REGEX = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
    }

    /**
     * 文件相关常量
     */
    public static class File {
        /** 默认上传路径 */
        public static final String DEFAULT_UPLOAD_PATH = "/uploads";
        /** 图片文件扩展名 */
        public static final String[] IMAGE_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"};
        /** 视频文件扩展名 */
        public static final String[] VIDEO_EXTENSIONS = {".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv"};
        /** 音频文件扩展名 */
        public static final String[] AUDIO_EXTENSIONS = {".mp3", ".wav", ".flac", ".aac", ".ogg"};
        /** 最大文件大小（字节） */
        public static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    }

    /**
     * 消息队列相关常量
     */
    public static class MQ {
        /** 用户相关交换机 */
        public static final String USER_EXCHANGE = "bilibili.user.exchange";
        /** 视频相关交换机 */
        public static final String VIDEO_EXCHANGE = "bilibili.video.exchange";
        /** 通知相关交换机 */
        public static final String NOTIFICATION_EXCHANGE = "bilibili.notification.exchange";
        
        /** 用户注册队列 */
        public static final String USER_REGISTER_QUEUE = "bilibili.user.register.queue";
        /** 用户登录队列 */
        public static final String USER_LOGIN_QUEUE = "bilibili.user.login.queue";
        /** 视频上传队列 */
        public static final String VIDEO_UPLOAD_QUEUE = "bilibili.video.upload.queue";
        /** 邮件发送队列 */
        public static final String EMAIL_SEND_QUEUE = "bilibili.email.send.queue";
        /** 短信发送队列 */
        public static final String SMS_SEND_QUEUE = "bilibili.sms.send.queue";
    }

    /**
     * 业务相关常量
     */
    public static class Business {
        /** 默认头像 */
        public static final String DEFAULT_AVATAR = "/static/images/default-avatar.png";
        /** 默认昵称前缀 */
        public static final String DEFAULT_NICKNAME_PREFIX = "用户";
        /** 验证码长度 */
        public static final int VERIFICATION_CODE_LENGTH = 6;
        /** 验证码有效期（分钟） */
        public static final int VERIFICATION_CODE_EXPIRE_MINUTES = 5;
        /** 最大重试次数 */
        public static final int MAX_RETRY_COUNT = 3;
    }
}
