package com.bilibili.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 性别枚举
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Getter
@AllArgsConstructor
public enum Gender {

    /**
     * 未知
     */
    UNKNOWN(0, "未知"),

    /**
     * 男
     */
    MALE(1, "男"),

    /**
     * 女
     */
    FEMALE(2, "女");

    private final Integer code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static Gender fromCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        for (Gender gender : values()) {
            if (gender.getCode().equals(code)) {
                return gender;
            }
        }
        return UNKNOWN;
    }

    /**
     * 是否为男性
     */
    public boolean isMale() {
        return this == MALE;
    }

    /**
     * 是否为女性
     */
    public boolean isFemale() {
        return this == FEMALE;
    }

    /**
     * 是否未知
     */
    public boolean isUnknown() {
        return this == UNKNOWN;
    }
}
