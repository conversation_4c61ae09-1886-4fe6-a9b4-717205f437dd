package com.bilibili.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Getter
@AllArgsConstructor
public enum UserStatus {

    /**
     * 正常
     */
    NORMAL(0, "正常"),

    /**
     * 封禁
     */
    BANNED(1, "封禁"),

    /**
     * 注销
     */
    DELETED(2, "注销");

    private final Integer code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static UserStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 是否为正常状态
     */
    public boolean isNormal() {
        return this == NORMAL;
    }

    /**
     * 是否为封禁状态
     */
    public boolean isBanned() {
        return this == BANNED;
    }

    /**
     * 是否为注销状态
     */
    public boolean isDeleted() {
        return this == DELETED;
    }
}
