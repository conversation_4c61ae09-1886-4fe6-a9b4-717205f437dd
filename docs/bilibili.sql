/*
 Navicat Premium Dump SQL

 Source Server         : bilibili
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : ***********:3306
 Source Schema         : bilibili

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 29/07/2025 16:01:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ai_cache
-- ----------------------------
DROP TABLE IF EXISTS `ai_cache`;
CREATE TABLE `ai_cache`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '缓存ID',
  `cache_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '缓存键',
  `cache_type` tinyint NOT NULL COMMENT '缓存类型 1问答 2分析 3推荐',
  `cache_value` json NOT NULL COMMENT '缓存值',
  `hit_count` int NOT NULL DEFAULT 0 COMMENT '命中次数',
  `expire_at` timestamp NOT NULL COMMENT '过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_cache_key`(`cache_key` ASC) USING BTREE,
  INDEX `idx_cache_type`(`cache_type` ASC) USING BTREE,
  INDEX `idx_expire_at`(`expire_at` ASC) USING BTREE,
  INDEX `idx_hit_count`(`hit_count` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI缓存表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_content_analysis
-- ----------------------------
DROP TABLE IF EXISTS `ai_content_analysis`;
CREATE TABLE `ai_content_analysis`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分析ID',
  `content_type` tinyint NOT NULL COMMENT '内容类型 1视频 2用户 3评论',
  `content_id` bigint UNSIGNED NOT NULL COMMENT '内容ID',
  `analysis_type` tinyint NOT NULL COMMENT '分析类型 1摘要 2标签 3情感 4质量评分',
  `analysis_result` json NOT NULL COMMENT '分析结果',
  `confidence_score` decimal(5, 4) NOT NULL DEFAULT 0.0000 COMMENT '置信度',
  `model_used` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '使用的模型',
  `analysis_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0' COMMENT '分析版本',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_content_analysis`(`content_type` ASC, `content_id` ASC, `analysis_type` ASC) USING BTREE,
  INDEX `idx_analysis_type`(`analysis_type` ASC) USING BTREE,
  INDEX `idx_confidence_score`(`confidence_score` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI内容分析结果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_conversations
-- ----------------------------
DROP TABLE IF EXISTS `ai_conversations`;
CREATE TABLE `ai_conversations`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '对话ID',
  `session_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `conversation_type` tinyint NOT NULL DEFAULT 1 COMMENT '对话类型 1问答 2分析 3推荐',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对话标题',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1进行中 2已结束 3已删除',
  `message_count` int NOT NULL DEFAULT 0 COMMENT '消息数量',
  `total_tokens` int NOT NULL DEFAULT 0 COMMENT '总Token消耗',
  `total_cost` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '总成本(美元)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_session_id`(`session_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_type`(`conversation_type` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `ai_conversations_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI对话会话表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_messages
-- ----------------------------
DROP TABLE IF EXISTS `ai_messages`;
CREATE TABLE `ai_messages`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `conversation_id` bigint UNSIGNED NOT NULL COMMENT '对话ID',
  `message_type` tinyint NOT NULL COMMENT '消息类型 1用户 2助手 3系统',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `metadata` json NULL COMMENT '消息元数据',
  `model_used` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '使用的模型',
  `prompt_tokens` int NOT NULL DEFAULT 0 COMMENT '输入Token数',
  `completion_tokens` int NOT NULL DEFAULT 0 COMMENT '输出Token数',
  `total_tokens` int NOT NULL DEFAULT 0 COMMENT '总Token数',
  `cost` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '成本(美元)',
  `response_time` int NOT NULL DEFAULT 0 COMMENT '响应时间(毫秒)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_conversation_id`(`conversation_id` ASC) USING BTREE,
  INDEX `idx_message_type`(`message_type` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `ai_messages_ibfk_1` FOREIGN KEY (`conversation_id`) REFERENCES `ai_conversations` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI对话消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_model_usage_stats
-- ----------------------------
DROP TABLE IF EXISTS `ai_model_usage_stats`;
CREATE TABLE `ai_model_usage_stats`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `model_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型名称',
  `usage_type` tinyint NOT NULL COMMENT '使用类型 1聊天 2嵌入 3分析',
  `request_count` int NOT NULL DEFAULT 0 COMMENT '请求次数',
  `total_tokens` bigint NOT NULL DEFAULT 0 COMMENT '总Token数',
  `total_cost` decimal(12, 4) NOT NULL DEFAULT 0.0000 COMMENT '总成本',
  `avg_response_time` int NOT NULL DEFAULT 0 COMMENT '平均响应时间(毫秒)',
  `success_count` int NOT NULL DEFAULT 0 COMMENT '成功次数',
  `error_count` int NOT NULL DEFAULT 0 COMMENT '错误次数',
  `date_hour` datetime NOT NULL COMMENT '统计时间(小时)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_model_type_hour`(`model_name` ASC, `usage_type` ASC, `date_hour` ASC) USING BTREE,
  INDEX `idx_model_name`(`model_name` ASC) USING BTREE,
  INDEX `idx_usage_type`(`usage_type` ASC) USING BTREE,
  INDEX `idx_date_hour`(`date_hour` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI模型使用统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_recommendations
-- ----------------------------
DROP TABLE IF EXISTS `ai_recommendations`;
CREATE TABLE `ai_recommendations`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '推荐ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `recommendation_type` tinyint NOT NULL COMMENT '推荐类型 1视频 2UP主 3标签',
  `target_id` bigint UNSIGNED NOT NULL COMMENT '推荐目标ID',
  `score` decimal(8, 6) NOT NULL COMMENT '推荐分数',
  `reason` json NULL COMMENT '推荐理由',
  `algorithm_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0' COMMENT '算法版本',
  `is_clicked` tinyint NOT NULL DEFAULT 0 COMMENT '是否点击',
  `is_liked` tinyint NOT NULL DEFAULT 0 COMMENT '是否喜欢',
  `feedback_score` tinyint NULL DEFAULT NULL COMMENT '用户反馈分数 1-5',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `clicked_at` timestamp NULL DEFAULT NULL COMMENT '点击时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_recommendation_type`(`recommendation_type` ASC) USING BTREE,
  INDEX `idx_target_id`(`target_id` ASC) USING BTREE,
  INDEX `idx_score`(`score` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `ai_recommendations_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI推荐记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_task_queue
-- ----------------------------
DROP TABLE IF EXISTS `ai_task_queue`;
CREATE TABLE `ai_task_queue`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_type` tinyint NOT NULL COMMENT '任务类型 1向量化 2分析 3推荐计算',
  `task_data` json NOT NULL COMMENT '任务数据',
  `priority` tinyint NOT NULL DEFAULT 5 COMMENT '优先级 1-10',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态 0待处理 1处理中 2已完成 3失败',
  `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
  `max_retry` int NOT NULL DEFAULT 3 COMMENT '最大重试次数',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_type`(`task_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_priority`(`priority` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI任务队列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_vector_embeddings
-- ----------------------------
DROP TABLE IF EXISTS `ai_vector_embeddings`;
CREATE TABLE `ai_vector_embeddings`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '向量ID',
  `content_type` tinyint NOT NULL COMMENT '内容类型 1视频 2用户 3评论 4文章',
  `content_id` bigint UNSIGNED NOT NULL COMMENT '内容ID',
  `content_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容哈希',
  `text_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文本内容',
  `embedding_vector` json NOT NULL COMMENT '向量数据',
  `embedding_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '向量化模型',
  `chunk_index` int NOT NULL DEFAULT 0 COMMENT '分块索引',
  `chunk_size` int NOT NULL DEFAULT 0 COMMENT '分块大小',
  `metadata` json NULL COMMENT '元数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_content_hash_chunk`(`content_hash` ASC, `chunk_index` ASC) USING BTREE,
  INDEX `idx_content_type_id`(`content_type` ASC, `content_id` ASC) USING BTREE,
  INDEX `idx_embedding_model`(`embedding_model` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI向量嵌入表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for article_categories
-- ----------------------------
DROP TABLE IF EXISTS `article_categories`;
CREATE TABLE `article_categories`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int NOT NULL DEFAULT 0 COMMENT '父分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '专栏分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for articles
-- ----------------------------
DROP TABLE IF EXISTS `articles`;
CREATE TABLE `articles`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `cvid` bigint UNSIGNED NOT NULL COMMENT '专栏ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '作者UID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文章标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文章内容',
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '文章摘要',
  `banner_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面图',
  `template_id` int NOT NULL DEFAULT 1 COMMENT '模板ID',
  `category_id` int NOT NULL DEFAULT 0 COMMENT '分类ID',
  `tags` json NULL COMMENT '标签',
  `words` int NOT NULL DEFAULT 0 COMMENT '字数',
  `read_time` int NOT NULL DEFAULT 0 COMMENT '阅读时长(秒)',
  `original` tinyint NOT NULL DEFAULT 1 COMMENT '原创标识 0转载 1原创',
  `image_urls` json NULL COMMENT '文章图片',
  `list_id` bigint NOT NULL DEFAULT 0 COMMENT '文集ID',
  `is_pay` tinyint NOT NULL DEFAULT 0 COMMENT '是否付费 0否 1是',
  `pay_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '付费价格',
  `state` tinyint NOT NULL DEFAULT 0 COMMENT '状态 0草稿 1已发布 2审核中 3已删除',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '审核原因',
  `view_count` bigint NOT NULL DEFAULT 0 COMMENT '阅读数',
  `like_count` bigint NOT NULL DEFAULT 0 COMMENT '点赞数',
  `reply_count` bigint NOT NULL DEFAULT 0 COMMENT '评论数',
  `share_count` bigint NOT NULL DEFAULT 0 COMMENT '分享数',
  `coin_count` bigint NOT NULL DEFAULT 0 COMMENT '投币数',
  `favorite_count` bigint NOT NULL DEFAULT 0 COMMENT '收藏数',
  `publish_time` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `cvid`(`cvid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_state`(`state` ASC) USING BTREE,
  INDEX `idx_publish_time`(`publish_time` ASC) USING BTREE,
  INDEX `idx_view_count`(`view_count` ASC) USING BTREE,
  INDEX `idx_like_count`(`like_count` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `articles_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '专栏文章表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for audit_rules
-- ----------------------------
DROP TABLE IF EXISTS `audit_rules`;
CREATE TABLE `audit_rules`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `rule_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则名称',
  `rule_type` tinyint NOT NULL DEFAULT 1 COMMENT '规则类型 1关键词 2图像识别 3文本分析 4行为分析',
  `content_type` tinyint NOT NULL DEFAULT 1 COMMENT '适用内容类型',
  `rule_config` json NOT NULL COMMENT '规则配置',
  `threshold` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '阈值',
  `action` tinyint NOT NULL DEFAULT 1 COMMENT '触发动作 1标记 2隐藏 3删除 4人工审核',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `priority` int NOT NULL DEFAULT 0 COMMENT '优先级',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_rule_type`(`rule_type` ASC) USING BTREE,
  INDEX `idx_content_type`(`content_type` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE,
  INDEX `idx_priority`(`priority` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审核规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for balance_records
-- ----------------------------
DROP TABLE IF EXISTS `balance_records`;
CREATE TABLE `balance_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `change_type` tinyint NOT NULL DEFAULT 1 COMMENT '变动类型 1充值 2消费 3退款 4奖励 5系统调整 6转账 7提现 8冻结 9解冻',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变动金额',
  `currency_type` tinyint NOT NULL DEFAULT 1 COMMENT '货币类型 1人民币 2B币',
  `balance_before` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变动前余额',
  `balance_after` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变动后余额',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联订单号',
  `business_type` tinyint NOT NULL DEFAULT 1 COMMENT '业务类型 1充值 2购买会员 3购买商品 4打赏 5礼物 6其他',
  `business_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '业务ID',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `operator_uid` bigint UNSIGNED NULL DEFAULT NULL COMMENT '操作者UID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_change_type`(`change_type` ASC) USING BTREE,
  INDEX `idx_currency_type`(`currency_type` ASC) USING BTREE,
  INDEX `idx_business_type`(`business_type` ASC) USING BTREE,
  INDEX `idx_business_id`(`business_id` ASC) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `operator_uid`(`operator_uid` ASC) USING BTREE,
  CONSTRAINT `balance_records_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `balance_records_ibfk_2` FOREIGN KEY (`operator_uid`) REFERENCES `users` (`uid`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '余额变动记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bangumi_episodes
-- ----------------------------
DROP TABLE IF EXISTS `bangumi_episodes`;
CREATE TABLE `bangumi_episodes`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `ep_id` bigint UNSIGNED NOT NULL COMMENT '集数ID',
  `season_id` bigint UNSIGNED NOT NULL COMMENT '季度ID',
  `aid` bigint UNSIGNED NOT NULL COMMENT '视频AID',
  `cid` bigint UNSIGNED NOT NULL COMMENT '视频CID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `long_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '长标题',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面',
  `duration` int NOT NULL DEFAULT 0 COMMENT '时长(秒)',
  `pub_time` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `badge` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '徽章',
  `status` tinyint NOT NULL DEFAULT 2 COMMENT '状态 0下架 1待发布 2已发布',
  `episode_index` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '集数索引',
  `view_count` bigint NOT NULL DEFAULT 0 COMMENT '播放数',
  `danmaku_count` bigint NOT NULL DEFAULT 0 COMMENT '弹幕数',
  `like_count` bigint NOT NULL DEFAULT 0 COMMENT '点赞数',
  `coin_count` bigint NOT NULL DEFAULT 0 COMMENT '投币数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ep_id`(`ep_id` ASC) USING BTREE,
  INDEX `idx_season_id`(`season_id` ASC) USING BTREE,
  INDEX `idx_aid`(`aid` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_episode_index`(`episode_index` ASC) USING BTREE,
  INDEX `idx_pub_time`(`pub_time` ASC) USING BTREE,
  INDEX `idx_view_count`(`view_count` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `bangumi_episodes_ibfk_1` FOREIGN KEY (`season_id`) REFERENCES `bangumi_seasons` (`season_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '番剧集数表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bangumi_seasons
-- ----------------------------
DROP TABLE IF EXISTS `bangumi_seasons`;
CREATE TABLE `bangumi_seasons`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `season_id` bigint UNSIGNED NOT NULL COMMENT '季度ID',
  `media_id` bigint UNSIGNED NOT NULL COMMENT '媒体ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面',
  `evaluate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '简介',
  `areas` json NULL COMMENT '地区',
  `styles` json NULL COMMENT '风格',
  `total_ep` int NOT NULL DEFAULT 0 COMMENT '总集数',
  `season_type` tinyint NOT NULL DEFAULT 1 COMMENT '季度类型 1番剧 2电影 3纪录片 4国创 5电视剧',
  `badge` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '徽章',
  `is_finish` tinyint NOT NULL DEFAULT 0 COMMENT '是否完结 0否 1是',
  `is_started` tinyint NOT NULL DEFAULT 0 COMMENT '是否开播 0否 1是',
  `weekday` tinyint NOT NULL DEFAULT 0 COMMENT '周几更新',
  `rating_score` decimal(3, 1) NOT NULL DEFAULT 0.0 COMMENT '评分',
  `rating_count` int NOT NULL DEFAULT 0 COMMENT '评分人数',
  `follow_count` bigint NOT NULL DEFAULT 0 COMMENT '追番人数',
  `view_count` bigint NOT NULL DEFAULT 0 COMMENT '播放数',
  `danmaku_count` bigint NOT NULL DEFAULT 0 COMMENT '弹幕数',
  `reply_count` bigint NOT NULL DEFAULT 0 COMMENT '评论数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 0下架 1上架 2审核中',
  `publish_time` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `season_id`(`season_id` ASC) USING BTREE,
  INDEX `idx_season_type`(`season_type` ASC) USING BTREE,
  INDEX `idx_is_finish`(`is_finish` ASC) USING BTREE,
  INDEX `idx_weekday`(`weekday` ASC) USING BTREE,
  INDEX `idx_rating_score`(`rating_score` ASC) USING BTREE,
  INDEX `idx_follow_count`(`follow_count` ASC) USING BTREE,
  INDEX `idx_view_count`(`view_count` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '番剧季度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comment_audits
-- ----------------------------
DROP TABLE IF EXISTS `comment_audits`;
CREATE TABLE `comment_audits`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '审核ID',
  `rpid` bigint UNSIGNED NOT NULL COMMENT '评论ID',
  `oid` bigint UNSIGNED NOT NULL COMMENT '对象ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '对象类型',
  `audit_type` tinyint NOT NULL DEFAULT 1 COMMENT '审核类型 1自动审核 2人工审核 3用户举报 4系统检测',
  `audit_result` tinyint NOT NULL DEFAULT 0 COMMENT '审核结果 0通过 1不通过 2需人工复审',
  `audit_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '审核原因',
  `risk_level` tinyint NOT NULL DEFAULT 0 COMMENT '风险等级 0低风险 1中风险 2高风险',
  `keywords` json NULL COMMENT '命中关键词',
  `audit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',
  `auditor_uid` bigint UNSIGNED NULL DEFAULT NULL COMMENT '审核员ID',
  `auditor_type` tinyint NOT NULL DEFAULT 1 COMMENT '审核员类型 1系统 2人工',
  `action_taken` tinyint NOT NULL DEFAULT 0 COMMENT '采取的行动 0无 1删除评论 2折叠评论 3限制用户 4封禁用户',
  `action_duration` int NOT NULL DEFAULT 0 COMMENT '行动持续时间(分钟)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_rpid`(`rpid` ASC) USING BTREE,
  INDEX `idx_oid_type`(`oid` ASC, `type` ASC) USING BTREE,
  INDEX `idx_audit_type`(`audit_type` ASC) USING BTREE,
  INDEX `idx_audit_result`(`audit_result` ASC) USING BTREE,
  INDEX `idx_risk_level`(`risk_level` ASC) USING BTREE,
  INDEX `idx_audit_time`(`audit_time` ASC) USING BTREE,
  INDEX `idx_auditor_uid`(`auditor_uid` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `comment_audits_ibfk_1` FOREIGN KEY (`rpid`) REFERENCES `comments` (`rpid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `comment_audits_ibfk_2` FOREIGN KEY (`auditor_uid`) REFERENCES `users` (`uid`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comment_configs
-- ----------------------------
DROP TABLE IF EXISTS `comment_configs`;
CREATE TABLE `comment_configs`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `oid` bigint UNSIGNED NOT NULL COMMENT '对象ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '对象类型',
  `config_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '配置值',
  `config_type` tinyint NOT NULL DEFAULT 1 COMMENT '配置类型 1字符串 2数字 3布尔 4JSON',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_oid_type_key`(`oid` ASC, `type` ASC, `config_key` ASC) USING BTREE,
  INDEX `idx_oid_type`(`oid` ASC, `type` ASC) USING BTREE,
  INDEX `idx_config_key`(`config_key` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comment_emote_packages
-- ----------------------------
DROP TABLE IF EXISTS `comment_emote_packages`;
CREATE TABLE `comment_emote_packages`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '套装ID',
  `package_id` int UNSIGNED NOT NULL COMMENT '表情包套装ID',
  `text` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '套装名称',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '套装图片URL',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '套装类型 1普通 2VIP专属 3大会员专属 4付费套装',
  `unlock_need` tinyint NOT NULL DEFAULT 0 COMMENT '解锁需求 0无 1VIP 2大会员 3付费',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `is_hot` tinyint NOT NULL DEFAULT 0 COMMENT '是否热门 0否 1是',
  `is_new` tinyint NOT NULL DEFAULT 0 COMMENT '是否新套装 0否 1是',
  `emote_count` int NOT NULL DEFAULT 0 COMMENT '表情数量',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `package_id`(`package_id` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_unlock_need`(`unlock_need` ASC) USING BTREE,
  INDEX `idx_is_hot`(`is_hot` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论表情包套装表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comment_emotes
-- ----------------------------
DROP TABLE IF EXISTS `comment_emotes`;
CREATE TABLE `comment_emotes`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '表情ID',
  `emote_id` int UNSIGNED NOT NULL COMMENT '表情包ID',
  `package_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '表情包套装ID',
  `text` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '表情文本',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '表情图片URL',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '表情类型 1普通 2VIP专属 3大会员专属 4付费表情',
  `size` tinyint NOT NULL DEFAULT 1 COMMENT '表情大小 1小 2中 3大',
  `is_gif` tinyint NOT NULL DEFAULT 0 COMMENT '是否GIF 0否 1是',
  `width` int NOT NULL DEFAULT 0 COMMENT '宽度',
  `height` int NOT NULL DEFAULT 0 COMMENT '高度',
  `is_hot` tinyint NOT NULL DEFAULT 0 COMMENT '是否热门 0否 1是',
  `is_new` tinyint NOT NULL DEFAULT 0 COMMENT '是否新表情 0否 1是',
  `unlock_need` tinyint NOT NULL DEFAULT 0 COMMENT '解锁需求 0无 1VIP 2大会员 3付费',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `emote_id`(`emote_id` ASC) USING BTREE,
  INDEX `idx_package_id`(`package_id` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_unlock_need`(`unlock_need` ASC) USING BTREE,
  INDEX `idx_is_hot`(`is_hot` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论表情包表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comment_hotspots
-- ----------------------------
DROP TABLE IF EXISTS `comment_hotspots`;
CREATE TABLE `comment_hotspots`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '热点ID',
  `oid` bigint UNSIGNED NOT NULL COMMENT '对象ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '对象类型',
  `rpid` bigint UNSIGNED NOT NULL COMMENT '评论ID',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '热度得分',
  `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞数',
  `reply_count` int NOT NULL DEFAULT 0 COMMENT '回复数',
  `time_weight` decimal(5, 2) NOT NULL DEFAULT 1.00 COMMENT '时间权重',
  `quality_weight` decimal(5, 2) NOT NULL DEFAULT 1.00 COMMENT '质量权重',
  `user_weight` decimal(5, 2) NOT NULL DEFAULT 1.00 COMMENT '用户权重',
  `is_hot` tinyint NOT NULL DEFAULT 0 COMMENT '是否热门 0否 1是',
  `hot_time` timestamp NULL DEFAULT NULL COMMENT '成为热门时间',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '热门过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_oid_type_rpid`(`oid` ASC, `type` ASC, `rpid` ASC) USING BTREE,
  INDEX `idx_oid_type`(`oid` ASC, `type` ASC) USING BTREE,
  INDEX `idx_rpid`(`rpid` ASC) USING BTREE,
  INDEX `idx_score`(`score` ASC) USING BTREE,
  INDEX `idx_is_hot`(`is_hot` ASC) USING BTREE,
  INDEX `idx_hot_time`(`hot_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `comment_hotspots_ibfk_1` FOREIGN KEY (`rpid`) REFERENCES `comments` (`rpid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论热点表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comment_interactions
-- ----------------------------
DROP TABLE IF EXISTS `comment_interactions`;
CREATE TABLE `comment_interactions`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '互动ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `rpid` bigint UNSIGNED NOT NULL COMMENT '评论ID',
  `oid` bigint UNSIGNED NOT NULL COMMENT '对象ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '对象类型',
  `action` tinyint NOT NULL DEFAULT 1 COMMENT '动作类型 1点赞 2点踩 3回复 4举报 5删除',
  `action_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '动作时间',
  `is_cancelled` tinyint NOT NULL DEFAULT 0 COMMENT '是否已取消 0否 1是',
  `cancel_time` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `platform` tinyint NOT NULL DEFAULT 1 COMMENT '平台类型',
  `device` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_rpid_action`(`uid` ASC, `rpid` ASC, `action` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_rpid`(`rpid` ASC) USING BTREE,
  INDEX `idx_oid_type`(`oid` ASC, `type` ASC) USING BTREE,
  INDEX `idx_action`(`action` ASC) USING BTREE,
  INDEX `idx_action_time`(`action_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `comment_interactions_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `comment_interactions_ibfk_2` FOREIGN KEY (`rpid`) REFERENCES `comments` (`rpid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论互动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comment_like_records
-- ----------------------------
DROP TABLE IF EXISTS `comment_like_records`;
CREATE TABLE `comment_like_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `uid` bigint NOT NULL COMMENT '用户UID',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态: 1=点赞 0=取消',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_comment_uid`(`comment_id` ASC, `uid` ASC) USING BTREE,
  INDEX `idx_uid_created`(`uid` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评论点赞记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for comment_reports
-- ----------------------------
DROP TABLE IF EXISTS `comment_reports`;
CREATE TABLE `comment_reports`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '举报ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '举报用户ID',
  `rpid` bigint UNSIGNED NOT NULL COMMENT '评论ID',
  `oid` bigint UNSIGNED NOT NULL COMMENT '对象ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '对象类型',
  `reason_type` tinyint NOT NULL DEFAULT 1 COMMENT '举报原因类型 1色情 2反动 3恶意攻击 4违法违规 5垃圾广告 6引战 7剧透 8刷屏 9其他',
  `reason_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '举报原因描述',
  `report_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '举报内容',
  `report_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '举报时间',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '处理状态 0待处理 1已处理 2已驳回',
  `handle_uid` bigint UNSIGNED NULL DEFAULT NULL COMMENT '处理人ID',
  `handle_time` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `handle_result` tinyint NOT NULL DEFAULT 0 COMMENT '处理结果 0无 1删除评论 2禁言用户 3封禁用户',
  `handle_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '处理描述',
  `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '举报IP',
  `platform` tinyint NOT NULL DEFAULT 1 COMMENT '平台类型',
  `device` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_rpid`(`rpid` ASC) USING BTREE,
  INDEX `idx_oid_type`(`oid` ASC, `type` ASC) USING BTREE,
  INDEX `idx_reason_type`(`reason_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_report_time`(`report_time` ASC) USING BTREE,
  INDEX `idx_handle_uid`(`handle_uid` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `comment_reports_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `comment_reports_ibfk_2` FOREIGN KEY (`rpid`) REFERENCES `comments` (`rpid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `comment_reports_ibfk_3` FOREIGN KEY (`handle_uid`) REFERENCES `users` (`uid`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论举报表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comment_sensitive_words
-- ----------------------------
DROP TABLE IF EXISTS `comment_sensitive_words`;
CREATE TABLE `comment_sensitive_words`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
  `word` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '敏感词',
  `level` tinyint NOT NULL DEFAULT 1 COMMENT '敏感级别 1低 2中 3高',
  `category` tinyint NOT NULL DEFAULT 1 COMMENT '分类 1色情 2反动 3暴力 4违法 5广告 6其他',
  `action` tinyint NOT NULL DEFAULT 1 COMMENT '处理动作 1过滤 2替换 3删除 4审核',
  `replacement` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '替换词',
  `is_regex` tinyint NOT NULL DEFAULT 0 COMMENT '是否正则 0否 1是',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_word`(`word` ASC) USING BTREE,
  INDEX `idx_level`(`level` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  INDEX `idx_action`(`action` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论敏感词表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comment_statistics
-- ----------------------------
DROP TABLE IF EXISTS `comment_statistics`;
CREATE TABLE `comment_statistics`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `oid` bigint UNSIGNED NOT NULL COMMENT '对象ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '对象类型',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `comment_count` int NOT NULL DEFAULT 0 COMMENT '评论总数',
  `reply_count` int NOT NULL DEFAULT 0 COMMENT '回复总数',
  `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞总数',
  `hate_count` int NOT NULL DEFAULT 0 COMMENT '点踩总数',
  `report_count` int NOT NULL DEFAULT 0 COMMENT '举报总数',
  `delete_count` int NOT NULL DEFAULT 0 COMMENT '删除总数',
  `fold_count` int NOT NULL DEFAULT 0 COMMENT '折叠总数',
  `top_count` int NOT NULL DEFAULT 0 COMMENT '置顶总数',
  `hot_count` int NOT NULL DEFAULT 0 COMMENT '热门总数',
  `user_count` int NOT NULL DEFAULT 0 COMMENT '参与用户数',
  `new_user_count` int NOT NULL DEFAULT 0 COMMENT '新用户数',
  `active_user_count` int NOT NULL DEFAULT 0 COMMENT '活跃用户数',
  `avg_comment_length` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '平均评论长度',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_oid_type_date`(`oid` ASC, `type` ASC, `stat_date` ASC) USING BTREE,
  INDEX `idx_oid_type`(`oid` ASC, `type` ASC) USING BTREE,
  INDEX `idx_stat_date`(`stat_date` ASC) USING BTREE,
  INDEX `idx_comment_count`(`comment_count` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comments
-- ----------------------------
DROP TABLE IF EXISTS `comments`;
CREATE TABLE `comments`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `rpid` bigint UNSIGNED NOT NULL COMMENT '评论唯一ID',
  `oid` bigint UNSIGNED NOT NULL COMMENT '对象ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '评论类型 1视频 2专栏 3动态 4直播 5番剧 6音频 7相簿 8活动 9课程 10音乐 11游戏 12漫画 13小说 14商品 15直播回放 16合集 17笔记 18话题 19投票 20抽奖',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `parent_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '父评论ID',
  `root_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '根评论ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '评论内容',
  `device` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备类型',
  `plat` tinyint NOT NULL DEFAULT 1 COMMENT '平台 1web 2ios 3android 4pc 5tv 6pad',
  `dialog_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '对话ID',
  `count` int NOT NULL DEFAULT 0 COMMENT '回复数量',
  `rcount` int NOT NULL DEFAULT 0 COMMENT '回复数量(含子评论)',
  `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞数',
  `hate_count` int NOT NULL DEFAULT 0 COMMENT '点踩数',
  `reply_count` int NOT NULL DEFAULT 0 COMMENT '回复数',
  `state` tinyint NOT NULL DEFAULT 0 COMMENT '状态 0正常 1已删除 2折叠 3被屏蔽',
  `attr` tinyint NOT NULL DEFAULT 0 COMMENT '属性位 0普通 1置顶 2热门',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `invisible` tinyint NOT NULL DEFAULT 0 COMMENT '是否隐藏 0否 1是',
  `ip_info` json NULL COMMENT 'IP信息',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '位置信息',
  `emote` json NULL COMMENT '表情信息',
  `jump_url` json NULL COMMENT '跳转链接',
  `max_line` int NOT NULL DEFAULT 0 COMMENT '最大行数',
  `note_cvid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '笔记cvid',
  `is_top` tinyint NOT NULL DEFAULT 0 COMMENT '是否置顶 0否 1是',
  `is_up_top` tinyint NOT NULL DEFAULT 0 COMMENT '是否UP主置顶 0否 1是',
  `is_lottery` tinyint NOT NULL DEFAULT 0 COMMENT '是否抽奖 0否 1是',
  `is_vote` tinyint NOT NULL DEFAULT 0 COMMENT '是否投票 0否 1是',
  `is_folder` tinyint NOT NULL DEFAULT 0 COMMENT '是否折叠 0否 1是',
  `is_hot` tinyint NOT NULL DEFAULT 0 COMMENT '是否热门 0否 1是',
  `weight` int NOT NULL DEFAULT 0 COMMENT '权重',
  `lottery_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '抽奖ID',
  `vote_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '投票ID',
  `show_follow` tinyint NOT NULL DEFAULT 0 COMMENT '是否显示关注 0否 1是',
  `is_contractor` tinyint NOT NULL DEFAULT 0 COMMENT '是否承包商 0否 1是',
  `is_senior_member` tinyint NOT NULL DEFAULT 0 COMMENT '是否高级会员 0否 1是',
  `is_note` tinyint NOT NULL DEFAULT 0 COMMENT '是否笔记 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `rpid`(`rpid` ASC) USING BTREE,
  INDEX `idx_oid_type`(`oid` ASC, `type` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_root_id`(`root_id` ASC) USING BTREE,
  INDEX `idx_state`(`state` ASC) USING BTREE,
  INDEX `idx_ctime`(`ctime` ASC) USING BTREE,
  INDEX `idx_like_count`(`like_count` ASC) USING BTREE,
  INDEX `idx_is_top`(`is_top` ASC) USING BTREE,
  INDEX `idx_is_hot`(`is_hot` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `comments_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for content_audits
-- ----------------------------
DROP TABLE IF EXISTS `content_audits`;
CREATE TABLE `content_audits`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '审核ID',
  `content_type` tinyint NOT NULL DEFAULT 1 COMMENT '内容类型 1视频 2专栏 3动态 4评论 5直播 6用户资料',
  `content_id` bigint UNSIGNED NOT NULL COMMENT '内容ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '内容作者UID',
  `audit_type` tinyint NOT NULL DEFAULT 1 COMMENT '审核类型 1机器审核 2人工审核 3复审',
  `audit_status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态 0待审核 1审核通过 2审核不通过 3需要人工审核',
  `audit_result` json NULL COMMENT '审核结果',
  `audit_score` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '审核得分',
  `risk_level` tinyint NOT NULL DEFAULT 0 COMMENT '风险等级 0低风险 1中风险 2高风险',
  `violation_type` tinyint NOT NULL DEFAULT 0 COMMENT '违规类型 0无 1色情 2暴力 3政治敏感 4垃圾广告 5其他',
  `violation_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '违规原因',
  `keywords` json NULL COMMENT '命中关键词',
  `auditor_uid` bigint UNSIGNED NULL DEFAULT NULL COMMENT '审核员UID',
  `audit_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `action_taken` tinyint NOT NULL DEFAULT 0 COMMENT '处理动作 0无 1删除 2隐藏 3限制 4封禁',
  `action_duration` int NOT NULL DEFAULT 0 COMMENT '处理时长(分钟)',
  `action_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '处理原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_content_type`(`content_type` ASC) USING BTREE,
  INDEX `idx_content_id`(`content_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_audit_type`(`audit_type` ASC) USING BTREE,
  INDEX `idx_audit_status`(`audit_status` ASC) USING BTREE,
  INDEX `idx_risk_level`(`risk_level` ASC) USING BTREE,
  INDEX `idx_violation_type`(`violation_type` ASC) USING BTREE,
  INDEX `idx_auditor_uid`(`auditor_uid` ASC) USING BTREE,
  INDEX `idx_audit_time`(`audit_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `content_audits_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `content_audits_ibfk_2` FOREIGN KEY (`auditor_uid`) REFERENCES `users` (`uid`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '内容审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for coupons
-- ----------------------------
DROP TABLE IF EXISTS `coupons`;
CREATE TABLE `coupons`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
  `coupon_id` bigint UNSIGNED NOT NULL COMMENT '优惠券唯一ID',
  `coupon_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '优惠券名称',
  `coupon_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '优惠券描述',
  `coupon_type` tinyint NOT NULL DEFAULT 1 COMMENT '优惠券类型 1满减券 2折扣券 3免费券 4现金券',
  `discount_type` tinyint NOT NULL DEFAULT 1 COMMENT '折扣类型 1固定金额 2百分比',
  `discount_value` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '折扣值',
  `min_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低消费金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大折扣金额',
  `total_quantity` int NOT NULL DEFAULT 0 COMMENT '发放总量',
  `used_quantity` int NOT NULL DEFAULT 0 COMMENT '已使用数量',
  `per_user_limit` int NOT NULL DEFAULT 1 COMMENT '每用户限制数量',
  `valid_start_time` timestamp NOT NULL COMMENT '有效开始时间',
  `valid_end_time` timestamp NOT NULL COMMENT '有效结束时间',
  `applicable_products` json NULL COMMENT '适用商品',
  `excluded_products` json NULL COMMENT '排除商品',
  `user_conditions` json NULL COMMENT '用户条件',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 0停用 1启用 2已结束',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `coupon_id`(`coupon_id` ASC) USING BTREE,
  INDEX `idx_coupon_type`(`coupon_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_valid_start_time`(`valid_start_time` ASC) USING BTREE,
  INDEX `idx_valid_end_time`(`valid_end_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '优惠券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for danmaku
-- ----------------------------
DROP TABLE IF EXISTS `danmaku`;
CREATE TABLE `danmaku`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '弹幕ID',
  `oid` bigint UNSIGNED NOT NULL COMMENT '弹幕池ID (通常等于cid)',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '弹幕类型 1=视频弹幕',
  `aid` bigint UNSIGNED NOT NULL COMMENT '视频AID',
  `bvid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '视频BVID',
  `cid` bigint UNSIGNED NOT NULL COMMENT '分P的CID',
  `mid` bigint UNSIGNED NOT NULL COMMENT '发送者UID',
  `progress` int NOT NULL COMMENT '弹幕时间(毫秒)',
  `mode` tinyint NOT NULL DEFAULT 1 COMMENT '弹幕模式 1滚动 4底部 5顶部 6逆向 7高级 8代码 9彩色',
  `fontsize` tinyint NOT NULL DEFAULT 25 COMMENT '字体大小 12/18/25',
  `color` int UNSIGNED NOT NULL DEFAULT 16777215 COMMENT '颜色(十进制)',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '弹幕内容',
  `mid_hash` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID哈希',
  `weight` int NOT NULL DEFAULT 10 COMMENT '权重',
  `action` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '动作',
  `pool` tinyint NOT NULL DEFAULT 0 COMMENT '弹幕池 0普通 1字幕 2特殊',
  `attr` int NOT NULL DEFAULT 0 COMMENT '属性',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 0删除 1正常 2屏蔽',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_cid_progress`(`cid` ASC, `progress` ASC) USING BTREE,
  INDEX `idx_oid_progress`(`oid` ASC, `progress` ASC) USING BTREE,
  INDEX `idx_aid_progress`(`aid` ASC, `progress` ASC) USING BTREE,
  INDEX `idx_bvid_progress`(`bvid` ASC, `progress` ASC) USING BTREE,
  INDEX `idx_mid`(`mid` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '弹幕表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for danmaku_filter_words
-- ----------------------------
DROP TABLE IF EXISTS `danmaku_filter_words`;
CREATE TABLE `danmaku_filter_words`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `word` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '屏蔽词',
  `type` tinyint NULL DEFAULT 1 COMMENT '类型: 1=全局屏蔽 2=用户自定义',
  `uid` bigint NULL DEFAULT NULL COMMENT '用户UID(用户自定义时)',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态: 1=启用 0=禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_word_uid`(`word` ASC, `uid` ASC) USING BTREE,
  INDEX `idx_type_status`(`type` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '弹幕屏蔽词表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dynamic_comments
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_comments`;
CREATE TABLE `dynamic_comments`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `post_id` bigint UNSIGNED NOT NULL COMMENT '动态ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '评论用户UID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容',
  `parent_id` bigint UNSIGNED NULL DEFAULT 0 COMMENT '父评论ID（回复用）',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态: 1正常 0删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dynamic_images
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_images`;
CREATE TABLE `dynamic_images`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `dynamic_id` bigint UNSIGNED NOT NULL COMMENT '动态ID',
  `img_src` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片地址',
  `img_width` int NOT NULL DEFAULT 0 COMMENT '图片宽度',
  `img_height` int NOT NULL DEFAULT 0 COMMENT '图片高度',
  `img_size` int NOT NULL DEFAULT 0 COMMENT '图片大小(字节)',
  `img_format` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片格式',
  `img_webp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'WebP格式图片',
  `img_tags` json NULL COMMENT '图片标签',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dynamic_id`(`dynamic_id` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  CONSTRAINT `dynamic_images_ibfk_1` FOREIGN KEY (`dynamic_id`) REFERENCES `dynamics` (`dynamic_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '动态图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dynamic_interactions
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_interactions`;
CREATE TABLE `dynamic_interactions`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '互动ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `dynamic_id` bigint UNSIGNED NOT NULL COMMENT '动态ID',
  `interaction_type` tinyint NOT NULL DEFAULT 1 COMMENT '互动类型 1点赞 2转发 3评论 4收藏 5分享',
  `interaction_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '互动时间',
  `is_cancelled` tinyint NOT NULL DEFAULT 0 COMMENT '是否已取消 0否 1是',
  `cancel_time` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_dynamic_type`(`uid` ASC, `dynamic_id` ASC, `interaction_type` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_dynamic_id`(`dynamic_id` ASC) USING BTREE,
  INDEX `idx_interaction_type`(`interaction_type` ASC) USING BTREE,
  INDEX `idx_interaction_time`(`interaction_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `dynamic_interactions_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `dynamic_interactions_ibfk_2` FOREIGN KEY (`dynamic_id`) REFERENCES `dynamics` (`dynamic_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '动态互动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dynamic_like_records
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_like_records`;
CREATE TABLE `dynamic_like_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `post_id` bigint UNSIGNED NOT NULL COMMENT '动态ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户UID',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '点赞状态: 1点赞 0取消',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_post`(`uid` ASC, `post_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dynamic_mention_relations
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_mention_relations`;
CREATE TABLE `dynamic_mention_relations`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `post_id` bigint UNSIGNED NOT NULL COMMENT '动态ID',
  `mentioned_uid` bigint UNSIGNED NOT NULL COMMENT '@的用户UID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_post_mentioned`(`post_id` ASC, `mentioned_uid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dynamic_posts
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_posts`;
CREATE TABLE `dynamic_posts`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '动态ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '发布用户UID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '动态内容',
  `media` json NULL COMMENT '媒体资源列表（图片/视频）',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '动态类型: 0文本 1图片 2视频 3话题',
  `privacy` tinyint NOT NULL DEFAULT 0 COMMENT '可见性: 0公开 1仅粉丝 2私密',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态: 1正常 0删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dynamic_repost_records
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_repost_records`;
CREATE TABLE `dynamic_repost_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '转发ID',
  `post_id` bigint UNSIGNED NOT NULL COMMENT '原动态ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '转发用户UID',
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '转发时附加评论',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '转发时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dynamic_tag_relations
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_tag_relations`;
CREATE TABLE `dynamic_tag_relations`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `post_id` bigint UNSIGNED NOT NULL COMMENT '动态ID',
  `tag_id` int NOT NULL COMMENT '标签ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_post_tag`(`post_id` ASC, `tag_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dynamic_video_relations
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_video_relations`;
CREATE TABLE `dynamic_video_relations`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `post_id` bigint UNSIGNED NOT NULL COMMENT '动态ID',
  `aid` bigint UNSIGNED NOT NULL COMMENT '视频AV号',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_post_aid`(`post_id` ASC, `aid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dynamics
-- ----------------------------
DROP TABLE IF EXISTS `dynamics`;
CREATE TABLE `dynamics`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '动态ID',
  `dynamic_id` bigint UNSIGNED NOT NULL COMMENT '动态唯一ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '动态类型 1文字 2图片 3视频 4音频 5直播 6专栏 7转发 8投票 9抽奖 10番剧 11音乐 12游戏 13商品 14活动 15课程 16笔记 17话题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '动态内容',
  `orig_dy_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '原动态ID',
  `pre_dy_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '转发动态ID',
  `rid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联资源ID',
  `acl` json NULL COMMENT '访问控制列表',
  `view_count` bigint NOT NULL DEFAULT 0 COMMENT '浏览数',
  `repost_count` bigint NOT NULL DEFAULT 0 COMMENT '转发数',
  `comment_count` bigint NOT NULL DEFAULT 0 COMMENT '评论数',
  `like_count` bigint NOT NULL DEFAULT 0 COMMENT '点赞数',
  `share_count` bigint NOT NULL DEFAULT 0 COMMENT '分享数',
  `is_top` tinyint NOT NULL DEFAULT 0 COMMENT '是否置顶 0否 1是',
  `visible` tinyint NOT NULL DEFAULT 1 COMMENT '可见性 0私密 1公开 2好友可见 3粉丝可见',
  `state` tinyint NOT NULL DEFAULT 1 COMMENT '状态 0已删除 1正常 2审核中 3已屏蔽',
  `reserve_source` tinyint NOT NULL DEFAULT 0 COMMENT '预约来源',
  `reserve_total` bigint NOT NULL DEFAULT 0 COMMENT '预约总数',
  `reserve_attach` json NULL COMMENT '预约附加信息',
  `display` json NULL COMMENT '显示信息',
  `extend_json` json NULL COMMENT '扩展信息',
  `extra` json NULL COMMENT '额外信息',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `dynamic_id`(`dynamic_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_orig_dy_id`(`orig_dy_id` ASC) USING BTREE,
  INDEX `idx_pre_dy_id`(`pre_dy_id` ASC) USING BTREE,
  INDEX `idx_rid`(`rid` ASC) USING BTREE,
  INDEX `idx_state`(`state` ASC) USING BTREE,
  INDEX `idx_visible`(`visible` ASC) USING BTREE,
  INDEX `idx_is_top`(`is_top` ASC) USING BTREE,
  INDEX `idx_ctime`(`ctime` ASC) USING BTREE,
  INDEX `idx_like_count`(`like_count` ASC) USING BTREE,
  INDEX `idx_view_count`(`view_count` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `dynamics_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '动态表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for email_notifications
-- ----------------------------
DROP TABLE IF EXISTS `email_notifications`;
CREATE TABLE `email_notifications`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '邮件通知ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱地址',
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件主题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件内容',
  `template_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模板ID',
  `template_data` json NULL COMMENT '模板数据',
  `send_type` tinyint NOT NULL DEFAULT 1 COMMENT '发送类型 1系统通知 2营销邮件 3验证邮件 4重要通知',
  `priority` tinyint NOT NULL DEFAULT 1 COMMENT '优先级 1低 2中 3高',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '发送状态 0待发送 1发送中 2发送成功 3发送失败',
  `send_time` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `schedule_time` timestamp NULL DEFAULT NULL COMMENT '计划发送时间',
  `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '错误信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_email`(`email` ASC) USING BTREE,
  INDEX `idx_send_type`(`send_type` ASC) USING BTREE,
  INDEX `idx_priority`(`priority` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_send_time`(`send_time` ASC) USING BTREE,
  INDEX `idx_schedule_time`(`schedule_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `email_notifications_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '邮件通知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_groups
-- ----------------------------
DROP TABLE IF EXISTS `follow_groups`;
CREATE TABLE `follow_groups`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `tagid` int NOT NULL COMMENT '标签ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分组名称',
  `count` int NOT NULL DEFAULT 0 COMMENT '关注数量',
  `tip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '提示信息',
  `attr` tinyint NOT NULL DEFAULT 0 COMMENT '属性',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_tagid`(`tagid` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  CONSTRAINT `follow_groups_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '关注分组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for garb_items
-- ----------------------------
DROP TABLE IF EXISTS `garb_items`;
CREATE TABLE `garb_items`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '装扮ID',
  `item_id` bigint UNSIGNED NOT NULL COMMENT '装扮唯一ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '装扮名称',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '装扮类型 1主题 2头像框 3挂件 4粉丝勋章 5空间背景 6动态卡片 7评论头像 8直播装扮',
  `category_id` int NOT NULL COMMENT '分类ID',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '装扮图片',
  `preview` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '预览图',
  `price` int NOT NULL DEFAULT 0 COMMENT '价格(B币)',
  `is_free` tinyint NOT NULL DEFAULT 0 COMMENT '是否免费 0否 1是',
  `is_vip` tinyint NOT NULL DEFAULT 0 COMMENT '是否VIP专享 0否 1是',
  `is_limited` tinyint NOT NULL DEFAULT 0 COMMENT '是否限时 0否 1是',
  `limit_start_time` timestamp NULL DEFAULT NULL COMMENT '限时开始时间',
  `limit_end_time` timestamp NULL DEFAULT NULL COMMENT '限时结束时间',
  `expire_days` int NOT NULL DEFAULT 0 COMMENT '有效期天数 0永久',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '描述',
  `properties` json NULL COMMENT '属性配置',
  `sales_count` int NOT NULL DEFAULT 0 COMMENT '销量',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `item_id`(`item_id` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_price`(`price` ASC) USING BTREE,
  INDEX `idx_is_free`(`is_free` ASC) USING BTREE,
  INDEX `idx_is_vip`(`is_vip` ASC) USING BTREE,
  INDEX `idx_is_limited`(`is_limited` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '装扮商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for live_areas
-- ----------------------------
DROP TABLE IF EXISTS `live_areas`;
CREATE TABLE `live_areas`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分区ID',
  `parent_id` int NOT NULL DEFAULT 0 COMMENT '父分区ID',
  `area_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分区名称',
  `area_type` tinyint NOT NULL DEFAULT 1 COMMENT '分区类型 1父分区 2子分区',
  `pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分区图片',
  `parent_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '父分区名称',
  `act_id` int NOT NULL DEFAULT 0 COMMENT '活动ID',
  `hot_status` tinyint NOT NULL DEFAULT 0 COMMENT '热门状态 0否 1是',
  `lock_status` tinyint NOT NULL DEFAULT 0 COMMENT '锁定状态 0否 1是',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_area_type`(`area_type` ASC) USING BTREE,
  INDEX `idx_hot_status`(`hot_status` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '直播分区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for live_rooms
-- ----------------------------
DROP TABLE IF EXISTS `live_rooms`;
CREATE TABLE `live_rooms`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '房间ID',
  `room_id` bigint UNSIGNED NOT NULL COMMENT '房间号',
  `uid` bigint UNSIGNED NOT NULL COMMENT '主播UID',
  `short_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '短房间号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '直播标题',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面图片',
  `background` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '背景图片',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '直播简介',
  `tags` json NULL COMMENT '标签',
  `area_id` int NOT NULL DEFAULT 0 COMMENT '分区ID',
  `parent_area_id` int NOT NULL DEFAULT 0 COMMENT '父分区ID',
  `area_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分区名称',
  `parent_area_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '父分区名称',
  `live_status` tinyint NOT NULL DEFAULT 0 COMMENT '直播状态 0未开播 1直播中 2轮播',
  `live_start_time` timestamp NULL DEFAULT NULL COMMENT '开播时间',
  `live_end_time` timestamp NULL DEFAULT NULL COMMENT '下播时间',
  `live_duration` int NOT NULL DEFAULT 0 COMMENT '直播时长(秒)',
  `live_screen` tinyint NOT NULL DEFAULT 0 COMMENT '直播画面 0竖屏 1横屏',
  `live_type` tinyint NOT NULL DEFAULT 0 COMMENT '直播类型 0普通直播 1语音直播',
  `online_count` int NOT NULL DEFAULT 0 COMMENT '在线人数',
  `attention_count` int NOT NULL DEFAULT 0 COMMENT '关注数',
  `max_online_count` int NOT NULL DEFAULT 0 COMMENT '最高在线人数',
  `live_time_total` int NOT NULL DEFAULT 0 COMMENT '总直播时长',
  `live_count` int NOT NULL DEFAULT 0 COMMENT '直播次数',
  `is_hidden` tinyint NOT NULL DEFAULT 0 COMMENT '是否隐藏 0否 1是',
  `is_locked` tinyint NOT NULL DEFAULT 0 COMMENT '是否锁定 0否 1是',
  `is_portrait` tinyint NOT NULL DEFAULT 0 COMMENT '是否竖屏 0否 1是',
  `is_special` tinyint NOT NULL DEFAULT 0 COMMENT '是否特殊直播间 0否 1是',
  `need_p2p` tinyint NOT NULL DEFAULT 0 COMMENT '是否需要P2P 0否 1是',
  `hidden_till` timestamp NULL DEFAULT NULL COMMENT '隐藏到期时间',
  `lock_till` timestamp NULL DEFAULT NULL COMMENT '锁定到期时间',
  `encrypted` tinyint NOT NULL DEFAULT 0 COMMENT '是否加密 0否 1是',
  `pwd_verified` tinyint NOT NULL DEFAULT 0 COMMENT '密码验证 0否 1是',
  `room_shield` tinyint NOT NULL DEFAULT 0 COMMENT '房间屏蔽 0否 1是',
  `is_sp` tinyint NOT NULL DEFAULT 0 COMMENT '是否特殊房间 0否 1是',
  `special_type` tinyint NOT NULL DEFAULT 0 COMMENT '特殊类型',
  `user_cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户封面',
  `keyframe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关键帧',
  `virtual_num` int NOT NULL DEFAULT 0 COMMENT '虚拟人数',
  `allow_change_area_time` timestamp NULL DEFAULT NULL COMMENT '允许改分区时间',
  `allow_upload_cover_time` timestamp NULL DEFAULT NULL COMMENT '允许上传封面时间',
  `studio_room_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '工作室房间ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `room_id`(`room_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_room_id`(`room_id` ASC) USING BTREE,
  INDEX `idx_short_id`(`short_id` ASC) USING BTREE,
  INDEX `idx_area_id`(`area_id` ASC) USING BTREE,
  INDEX `idx_live_status`(`live_status` ASC) USING BTREE,
  INDEX `idx_live_start_time`(`live_start_time` ASC) USING BTREE,
  INDEX `idx_online_count`(`online_count` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `live_rooms_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '直播房间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for live_streams
-- ----------------------------
DROP TABLE IF EXISTS `live_streams`;
CREATE TABLE `live_streams`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '流ID',
  `room_id` bigint UNSIGNED NOT NULL COMMENT '房间ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '主播UID',
  `stream_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推流密钥',
  `rtmp_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'RTMP推流地址',
  `hls_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'HLS播放地址',
  `flv_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'FLV播放地址',
  `dash_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'DASH播放地址',
  `cdn_type` tinyint NOT NULL DEFAULT 1 COMMENT 'CDN类型 1阿里云 2腾讯云 3华为云',
  `stream_status` tinyint NOT NULL DEFAULT 0 COMMENT '流状态 0停止 1推流中 2连接中',
  `bitrate` int NOT NULL DEFAULT 0 COMMENT '码率',
  `fps` int NOT NULL DEFAULT 0 COMMENT '帧率',
  `resolution` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分辨率',
  `codec` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '编码格式',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `duration` int NOT NULL DEFAULT 0 COMMENT '持续时间(秒)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_room_stream`(`room_id` ASC, `stream_key` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_stream_status`(`stream_status` ASC) USING BTREE,
  INDEX `idx_start_time`(`start_time` ASC) USING BTREE,
  CONSTRAINT `live_streams_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `live_rooms` (`room_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `live_streams_ibfk_2` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '直播流表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for live_watch_records
-- ----------------------------
DROP TABLE IF EXISTS `live_watch_records`;
CREATE TABLE `live_watch_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `room_id` bigint UNSIGNED NOT NULL COMMENT '房间ID',
  `anchor_uid` bigint UNSIGNED NOT NULL COMMENT '主播UID',
  `watch_time` int NOT NULL DEFAULT 0 COMMENT '观看时长(秒)',
  `enter_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '进入时间',
  `leave_time` timestamp NULL DEFAULT NULL COMMENT '离开时间',
  `platform` tinyint NOT NULL DEFAULT 1 COMMENT '平台 1web 2ios 3android 4pc',
  `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `is_mobile` tinyint NOT NULL DEFAULT 0 COMMENT '是否移动端 0否 1是',
  `area_id` int NOT NULL DEFAULT 0 COMMENT '分区ID',
  `parent_area_id` int NOT NULL DEFAULT 0 COMMENT '父分区ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_room_id`(`room_id` ASC) USING BTREE,
  INDEX `idx_anchor_uid`(`anchor_uid` ASC) USING BTREE,
  INDEX `idx_enter_time`(`enter_time` ASC) USING BTREE,
  INDEX `idx_area_id`(`area_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `live_watch_records_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `live_watch_records_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `live_rooms` (`room_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `live_watch_records_ibfk_3` FOREIGN KEY (`anchor_uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '直播观看记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mall_categories
-- ----------------------------
DROP TABLE IF EXISTS `mall_categories`;
CREATE TABLE `mall_categories`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int NOT NULL DEFAULT 0 COMMENT '父分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图片',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mall_items
-- ----------------------------
DROP TABLE IF EXISTS `mall_items`;
CREATE TABLE `mall_items`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `item_id` bigint UNSIGNED NOT NULL COMMENT '商品唯一ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `brief` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '简介',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '详细描述',
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主图',
  `imgs` json NULL COMMENT '商品图片',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `market_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '市场价',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '货币单位',
  `sales` int NOT NULL DEFAULT 0 COMMENT '销量',
  `stock` int NOT NULL DEFAULT 0 COMMENT '库存',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `category_id` int NOT NULL COMMENT '分类ID',
  `tags` json NULL COMMENT '标签',
  `properties` json NULL COMMENT '属性',
  `is_virtual` tinyint NOT NULL DEFAULT 0 COMMENT '是否虚拟商品 0否 1是',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_hot` tinyint NOT NULL DEFAULT 0 COMMENT '是否热门 0否 1是',
  `is_new` tinyint NOT NULL DEFAULT 0 COMMENT '是否新品 0否 1是',
  `is_recommend` tinyint NOT NULL DEFAULT 0 COMMENT '是否推荐 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `item_id`(`item_id` ASC) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_price`(`price` ASC) USING BTREE,
  INDEX `idx_sales`(`sales` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_is_hot`(`is_hot` ASC) USING BTREE,
  INDEX `idx_is_new`(`is_new` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for notification_settings
-- ----------------------------
DROP TABLE IF EXISTS `notification_settings`;
CREATE TABLE `notification_settings`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '通知类型',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `push_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否推送 0否 1是',
  `email_enabled` tinyint NOT NULL DEFAULT 0 COMMENT '是否邮件通知 0否 1是',
  `sms_enabled` tinyint NOT NULL DEFAULT 0 COMMENT '是否短信通知 0否 1是',
  `sound_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否声音提醒 0否 1是',
  `vibration_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否震动提醒 0否 1是',
  `do_not_disturb_start` time NULL DEFAULT NULL COMMENT '免打扰开始时间',
  `do_not_disturb_end` time NULL DEFAULT NULL COMMENT '免打扰结束时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_type`(`uid` ASC, `type` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE,
  CONSTRAINT `notification_settings_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通知设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for notification_statistics
-- ----------------------------
DROP TABLE IF EXISTS `notification_statistics`;
CREATE TABLE `notification_statistics`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_notifications` int NOT NULL DEFAULT 0 COMMENT '总通知数',
  `read_notifications` int NOT NULL DEFAULT 0 COMMENT '已读通知数',
  `unread_notifications` int NOT NULL DEFAULT 0 COMMENT '未读通知数',
  `push_notifications` int NOT NULL DEFAULT 0 COMMENT '推送通知数',
  `email_notifications` int NOT NULL DEFAULT 0 COMMENT '邮件通知数',
  `sms_notifications` int NOT NULL DEFAULT 0 COMMENT '短信通知数',
  `system_notifications` int NOT NULL DEFAULT 0 COMMENT '系统通知数',
  `user_notifications` int NOT NULL DEFAULT 0 COMMENT '用户通知数',
  `successful_push` int NOT NULL DEFAULT 0 COMMENT '成功推送数',
  `failed_push` int NOT NULL DEFAULT 0 COMMENT '失败推送数',
  `click_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '点击率',
  `read_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '阅读率',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date`(`stat_date` ASC) USING BTREE,
  INDEX `idx_stat_date`(`stat_date` ASC) USING BTREE,
  INDEX `idx_total_notifications`(`total_notifications` ASC) USING BTREE,
  INDEX `idx_read_rate`(`read_rate` ASC) USING BTREE,
  INDEX `idx_click_rate`(`click_rate` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通知统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for notification_templates
-- ----------------------------
DROP TABLE IF EXISTS `notification_templates`;
CREATE TABLE `notification_templates`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板代码',
  `template_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `template_type` tinyint NOT NULL DEFAULT 1 COMMENT '模板类型 1站内通知 2推送通知 3邮件通知 4短信通知',
  `title_template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题模板',
  `content_template` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容模板',
  `variables` json NULL COMMENT '变量说明',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `template_code`(`template_code` ASC) USING BTREE,
  INDEX `idx_template_code`(`template_code` ASC) USING BTREE,
  INDEX `idx_template_type`(`template_type` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通知模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '通知类型 1点赞 2评论 3关注 4私信 5@提及 6系统通知 7直播开播 8视频更新 9活动通知 10审核通知',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '通知内容',
  `source_type` tinyint NOT NULL DEFAULT 1 COMMENT '来源类型 1用户 2系统 3视频 4直播 5专栏 6动态',
  `source_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '来源ID',
  `sender_uid` bigint UNSIGNED NULL DEFAULT NULL COMMENT '发送者UID',
  `target_type` tinyint NOT NULL DEFAULT 1 COMMENT '目标类型 1视频 2专栏 3动态 4评论 5直播 6用户',
  `target_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '目标ID',
  `jump_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '跳转链接',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片链接',
  `is_read` tinyint NOT NULL DEFAULT 0 COMMENT '是否已读 0未读 1已读',
  `read_time` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `priority` tinyint NOT NULL DEFAULT 1 COMMENT '优先级 1低 2中 3高',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `extra_data` json NULL COMMENT '额外数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_source_type`(`source_type` ASC) USING BTREE,
  INDEX `idx_source_id`(`source_id` ASC) USING BTREE,
  INDEX `idx_sender_uid`(`sender_uid` ASC) USING BTREE,
  INDEX `idx_target_type`(`target_type` ASC) USING BTREE,
  INDEX `idx_target_id`(`target_id` ASC) USING BTREE,
  INDEX `idx_is_read`(`is_read` ASC) USING BTREE,
  INDEX `idx_priority`(`priority` ASC) USING BTREE,
  INDEX `idx_expire_time`(`expire_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`sender_uid`) REFERENCES `users` (`uid`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for payment_methods
-- ----------------------------
DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '支付方式ID',
  `method_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式代码',
  `method_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式名称',
  `method_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付方式描述',
  `method_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付方式图标',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `config` json NULL COMMENT '配置信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `method_code`(`method_code` ASC) USING BTREE,
  INDEX `idx_method_code`(`method_code` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '支付方式表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for payment_orders
-- ----------------------------
DROP TABLE IF EXISTS `payment_orders`;
CREATE TABLE `payment_orders`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `trade_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '交易号',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `product_id` bigint UNSIGNED NOT NULL COMMENT '商品ID',
  `product_type` tinyint NOT NULL DEFAULT 1 COMMENT '商品类型 1大会员 2B币 3装扮 4礼物 5商品 6课程 7漫画 8小说 9游戏 10直播礼物 11打赏 12众筹',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `product_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商品描述',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `unit_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '单价',
  `total_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `discount_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `coupon_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券金额',
  `actual_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实付金额',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '货币类型',
  `pay_method` tinyint NOT NULL DEFAULT 1 COMMENT '支付方式 1支付宝 2微信 3银行卡 4余额 5B币 6积分',
  `pay_channel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付渠道',
  `pay_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态 0待支付 1支付中 2已支付 3支付失败 4已取消 5已退款 6部分退款',
  `pay_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `cancel_time` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `refund_time` timestamp NULL DEFAULT NULL COMMENT '退款时间',
  `platform` tinyint NOT NULL DEFAULT 1 COMMENT '支付平台 1web 2ios 3android 4pc',
  `client_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `notify_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '通知地址',
  `return_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '返回地址',
  `attach_data` json NULL COMMENT '附加数据',
  `third_party_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '第三方订单ID',
  `third_party_trade_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '第三方交易号',
  `third_party_response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '第三方响应',
  `failure_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '失败原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_product_type`(`product_type` ASC) USING BTREE,
  INDEX `idx_pay_method`(`pay_method` ASC) USING BTREE,
  INDEX `idx_pay_status`(`pay_status` ASC) USING BTREE,
  INDEX `idx_pay_time`(`pay_time` ASC) USING BTREE,
  INDEX `idx_expire_time`(`expire_time` ASC) USING BTREE,
  INDEX `idx_trade_no`(`trade_no` ASC) USING BTREE,
  INDEX `idx_third_party_order_id`(`third_party_order_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `payment_orders_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '支付订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for payment_statistics
-- ----------------------------
DROP TABLE IF EXISTS `payment_statistics`;
CREATE TABLE `payment_statistics`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_orders` int NOT NULL DEFAULT 0 COMMENT '总订单数',
  `successful_orders` int NOT NULL DEFAULT 0 COMMENT '成功订单数',
  `failed_orders` int NOT NULL DEFAULT 0 COMMENT '失败订单数',
  `total_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `successful_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '成功金额',
  `refund_orders` int NOT NULL DEFAULT 0 COMMENT '退款订单数',
  `refund_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `new_users` int NOT NULL DEFAULT 0 COMMENT '新用户数',
  `active_users` int NOT NULL DEFAULT 0 COMMENT '活跃用户数',
  `avg_order_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '平均订单金额',
  `recharge_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '充值金额',
  `recharge_users` int NOT NULL DEFAULT 0 COMMENT '充值用户数',
  `tip_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '打赏金额',
  `tip_users` int NOT NULL DEFAULT 0 COMMENT '打赏用户数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date`(`stat_date` ASC) USING BTREE,
  INDEX `idx_stat_date`(`stat_date` ASC) USING BTREE,
  INDEX `idx_total_amount`(`total_amount` ASC) USING BTREE,
  INDEX `idx_successful_amount`(`successful_amount` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '支付统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for push_devices
-- ----------------------------
DROP TABLE IF EXISTS `push_devices`;
CREATE TABLE `push_devices`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `device_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备令牌',
  `device_type` tinyint NOT NULL DEFAULT 1 COMMENT '设备类型 1iOS 2Android 3Web 4PC',
  `device_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备标识',
  `device_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备名称',
  `app_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用版本',
  `os_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统版本',
  `push_service` tinyint NOT NULL DEFAULT 1 COMMENT '推送服务 1APNs 2FCM 3JPush 4个推 5小米 6华为 7OPPO 8VIVO',
  `is_active` tinyint NOT NULL DEFAULT 1 COMMENT '是否活跃 0否 1是',
  `last_active_time` timestamp NULL DEFAULT NULL COMMENT '最后活跃时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_device_token`(`device_token` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_device_type`(`device_type` ASC) USING BTREE,
  INDEX `idx_push_service`(`push_service` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_last_active_time`(`last_active_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `push_devices_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '推送设备表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for push_records
-- ----------------------------
DROP TABLE IF EXISTS `push_records`;
CREATE TABLE `push_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '推送记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `device_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备令牌',
  `notification_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '通知ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推送标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '推送内容',
  `push_type` tinyint NOT NULL DEFAULT 1 COMMENT '推送类型 1单播 2组播 3广播',
  `push_service` tinyint NOT NULL DEFAULT 1 COMMENT '推送服务',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '推送状态 0待推送 1推送中 2推送成功 3推送失败',
  `push_time` timestamp NULL DEFAULT NULL COMMENT '推送时间',
  `receive_time` timestamp NULL DEFAULT NULL COMMENT '接收时间',
  `click_time` timestamp NULL DEFAULT NULL COMMENT '点击时间',
  `error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '错误代码',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '错误信息',
  `extra_data` json NULL COMMENT '额外数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_device_token`(`device_token` ASC) USING BTREE,
  INDEX `idx_notification_id`(`notification_id` ASC) USING BTREE,
  INDEX `idx_push_type`(`push_type` ASC) USING BTREE,
  INDEX `idx_push_service`(`push_service` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_push_time`(`push_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `push_records_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `push_records_ibfk_2` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '推送记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for recharge_records
-- ----------------------------
DROP TABLE IF EXISTS `recharge_records`;
CREATE TABLE `recharge_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '充值记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `recharge_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '充值金额',
  `bonus_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '赠送金额',
  `total_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `currency_type` tinyint NOT NULL DEFAULT 1 COMMENT '货币类型 1人民币 2B币',
  `pay_method` tinyint NOT NULL DEFAULT 1 COMMENT '支付方式',
  `pay_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态',
  `pay_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `platform` tinyint NOT NULL DEFAULT 1 COMMENT '充值平台',
  `channel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '充值渠道',
  `third_party_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '第三方订单ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_pay_status`(`pay_status` ASC) USING BTREE,
  INDEX `idx_pay_time`(`pay_time` ASC) USING BTREE,
  INDEX `idx_currency_type`(`currency_type` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `recharge_records_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '充值记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for refund_orders
-- ----------------------------
DROP TABLE IF EXISTS `refund_orders`;
CREATE TABLE `refund_orders`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '退款ID',
  `refund_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '退款单号',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原订单号',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `refund_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `refund_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '退款原因',
  `refund_type` tinyint NOT NULL DEFAULT 1 COMMENT '退款类型 1全额退款 2部分退款',
  `refund_method` tinyint NOT NULL DEFAULT 1 COMMENT '退款方式 1原路返回 2余额 3B币',
  `refund_status` tinyint NOT NULL DEFAULT 0 COMMENT '退款状态 0申请中 1处理中 2已退款 3已拒绝 4已取消',
  `apply_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `process_time` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `complete_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `processor_uid` bigint UNSIGNED NULL DEFAULT NULL COMMENT '处理人ID',
  `process_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '处理备注',
  `third_party_refund_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '第三方退款ID',
  `third_party_response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '第三方响应',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `refund_id`(`refund_id` ASC) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_refund_status`(`refund_status` ASC) USING BTREE,
  INDEX `idx_apply_time`(`apply_time` ASC) USING BTREE,
  INDEX `idx_processor_uid`(`processor_uid` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `refund_orders_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `refund_orders_ibfk_2` FOREIGN KEY (`processor_uid`) REFERENCES `users` (`uid`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '退款订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sms_notifications
-- ----------------------------
DROP TABLE IF EXISTS `sms_notifications`;
CREATE TABLE `sms_notifications`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '短信通知ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信内容',
  `template_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模板ID',
  `template_params` json NULL COMMENT '模板参数',
  `sms_type` tinyint NOT NULL DEFAULT 1 COMMENT '短信类型 1验证码 2通知 3营销 4重要通知',
  `priority` tinyint NOT NULL DEFAULT 1 COMMENT '优先级 1低 2中 3高',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '发送状态 0待发送 1发送中 2发送成功 3发送失败',
  `send_time` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `schedule_time` timestamp NULL DEFAULT NULL COMMENT '计划发送时间',
  `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
  `error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '错误代码',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '错误信息',
  `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '短信服务商',
  `cost` decimal(8, 4) NOT NULL DEFAULT 0.0000 COMMENT '费用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_phone`(`phone` ASC) USING BTREE,
  INDEX `idx_sms_type`(`sms_type` ASC) USING BTREE,
  INDEX `idx_priority`(`priority` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_send_time`(`send_time` ASC) USING BTREE,
  INDEX `idx_schedule_time`(`schedule_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `sms_notifications_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '短信通知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_announcements
-- ----------------------------
DROP TABLE IF EXISTS `system_announcements`;
CREATE TABLE `system_announcements`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告内容',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '公告类型 1系统维护 2功能更新 3活动通知 4重要公告',
  `level` tinyint NOT NULL DEFAULT 1 COMMENT '公告级别 1一般 2重要 3紧急',
  `target_type` tinyint NOT NULL DEFAULT 1 COMMENT '目标类型 1全体用户 2指定用户 3会员用户 4新用户',
  `target_users` json NULL COMMENT '目标用户',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片链接',
  `jump_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '跳转链接',
  `is_popup` tinyint NOT NULL DEFAULT 0 COMMENT '是否弹窗 0否 1是',
  `is_top` tinyint NOT NULL DEFAULT 0 COMMENT '是否置顶 0否 1是',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `read_count` int NOT NULL DEFAULT 0 COMMENT '阅读数',
  `creator_uid` bigint UNSIGNED NULL DEFAULT NULL COMMENT '创建者UID',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 0禁用 1启用 2已过期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_level`(`level` ASC) USING BTREE,
  INDEX `idx_target_type`(`target_type` ASC) USING BTREE,
  INDEX `idx_is_popup`(`is_popup` ASC) USING BTREE,
  INDEX `idx_is_top`(`is_top` ASC) USING BTREE,
  INDEX `idx_start_time`(`start_time` ASC) USING BTREE,
  INDEX `idx_end_time`(`end_time` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_creator_uid`(`creator_uid` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `system_announcements_ibfk_1` FOREIGN KEY (`creator_uid`) REFERENCES `users` (`uid`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_logs
-- ----------------------------
DROP TABLE IF EXISTS `system_logs`;
CREATE TABLE `system_logs`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `level` tinyint NOT NULL DEFAULT 1 COMMENT '日志级别 1debug 2info 3warn 4error 5fatal',
  `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模块名称',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志消息',
  `context` json NULL COMMENT '上下文信息',
  `trace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '追踪ID',
  `exception_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '异常信息',
  `server_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '服务器名称',
  `process_id` int NULL DEFAULT NULL COMMENT '进程ID',
  `thread_id` int NULL DEFAULT NULL COMMENT '线程ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_level`(`level` ASC) USING BTREE,
  INDEX `idx_module`(`module` ASC) USING BTREE,
  INDEX `idx_trace_id`(`trace_id` ASC) USING BTREE,
  INDEX `idx_server_name`(`server_name` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tip_records
-- ----------------------------
DROP TABLE IF EXISTS `tip_records`;
CREATE TABLE `tip_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '打赏记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '打赏用户ID',
  `target_uid` bigint UNSIGNED NOT NULL COMMENT '被打赏用户ID',
  `target_type` tinyint NOT NULL DEFAULT 1 COMMENT '打赏对象类型 1视频 2专栏 3动态 4直播 5音频',
  `target_id` bigint UNSIGNED NOT NULL COMMENT '打赏对象ID',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '打赏金额',
  `currency_type` tinyint NOT NULL DEFAULT 2 COMMENT '货币类型 1人民币 2B币',
  `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '打赏留言',
  `is_anonymous` tinyint NOT NULL DEFAULT 0 COMMENT '是否匿名 0否 1是',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单号',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1成功 2失败 3已退款',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_target_uid`(`target_uid` ASC) USING BTREE,
  INDEX `idx_target_type`(`target_type` ASC) USING BTREE,
  INDEX `idx_target_id`(`target_id` ASC) USING BTREE,
  INDEX `idx_amount`(`amount` ASC) USING BTREE,
  INDEX `idx_currency_type`(`currency_type` ASC) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `tip_records_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `tip_records_ibfk_2` FOREIGN KEY (`target_uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '打赏记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for upload_chunks
-- ----------------------------
DROP TABLE IF EXISTS `upload_chunks`;
CREATE TABLE `upload_chunks`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `task_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `chunk_index` bigint NOT NULL,
  `chunk_size` bigint NOT NULL,
  `chunk_md5` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` bigint NULL DEFAULT 0,
  `uploaded_at` datetime(3) NULL DEFAULT NULL,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for upload_statistics
-- ----------------------------
DROP TABLE IF EXISTS `upload_statistics`;
CREATE TABLE `upload_statistics`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `date` date NOT NULL COMMENT '统计日期',
  `total_uploads` int NULL DEFAULT 0 COMMENT '总上传数',
  `successful_uploads` int NULL DEFAULT 0 COMMENT '成功上传数',
  `failed_uploads` int NULL DEFAULT 0 COMMENT '失败上传数',
  `total_size` bigint NULL DEFAULT 0 COMMENT '总上传大小(字节)',
  `average_upload_time` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '平均上传时间(秒)',
  `average_process_time` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '平均处理时间(秒)',
  `peak_concurrent_uploads` int NULL DEFAULT 0 COMMENT '峰值并发上传数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_date`(`date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '上传统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_actions
-- ----------------------------
DROP TABLE IF EXISTS `user_actions`;
CREATE TABLE `user_actions`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `action_type` tinyint NOT NULL COMMENT '行为类型 1点赞 2投币 3收藏 4分享 5观看',
  `target_type` tinyint NOT NULL COMMENT '目标类型 1视频 2动态 3专栏 4音频',
  `target_id` bigint UNSIGNED NOT NULL COMMENT '目标ID',
  `target_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标标题',
  `target_author` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标作者',
  `target_cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标封面',
  `extra_data` json NULL COMMENT '额外数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid_action`(`uid` ASC, `action_type` ASC) USING BTREE,
  INDEX `idx_target`(`target_type` ASC, `target_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_actions_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户行为记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_announcement_reads
-- ----------------------------
DROP TABLE IF EXISTS `user_announcement_reads`;
CREATE TABLE `user_announcement_reads`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '阅读记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `announcement_id` bigint UNSIGNED NOT NULL COMMENT '公告ID',
  `read_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_announcement`(`uid` ASC, `announcement_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_announcement_id`(`announcement_id` ASC) USING BTREE,
  INDEX `idx_read_time`(`read_time` ASC) USING BTREE,
  CONSTRAINT `user_announcement_reads_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_announcement_reads_ibfk_2` FOREIGN KEY (`announcement_id`) REFERENCES `system_announcements` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户公告阅读记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_auth_logs
-- ----------------------------
DROP TABLE IF EXISTS `user_auth_logs`;
CREATE TABLE `user_auth_logs`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `auth_type` int NULL DEFAULT NULL,
  `device_info` json NULL COMMENT '设备信息',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `status` int NULL DEFAULT NULL,
  `failure_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '失败原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime(6) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_auth_type`(`auth_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_auth_logs_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户认证记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_balance
-- ----------------------------
DROP TABLE IF EXISTS `user_balance`;
CREATE TABLE `user_balance`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '余额ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `balance` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '余额',
  `frozen_balance` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '冻结余额',
  `b_coin` int NOT NULL DEFAULT 0 COMMENT 'B币数量',
  `frozen_b_coin` int NOT NULL DEFAULT 0 COMMENT '冻结B币数量',
  `version` int NOT NULL DEFAULT 1 COMMENT '版本号',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_balance`(`balance` ASC) USING BTREE,
  INDEX `idx_b_coin`(`b_coin` ASC) USING BTREE,
  CONSTRAINT `user_balance_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户余额表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_blacklist
-- ----------------------------
DROP TABLE IF EXISTS `user_blacklist`;
CREATE TABLE `user_blacklist`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `target_uid` bigint UNSIGNED NOT NULL COMMENT '被拉黑用户ID',
  `target_username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '被拉黑用户名',
  `target_nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '被拉黑用户昵称',
  `target_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '被拉黑用户头像',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '拉黑原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '拉黑时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_target`(`uid` ASC, `target_uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_target_uid`(`target_uid` ASC) USING BTREE,
  CONSTRAINT `user_blacklist_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户黑名单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_coin_log
-- ----------------------------
DROP TABLE IF EXISTS `user_coin_log`;
CREATE TABLE `user_coin_log`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `coin_change` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '硬币变化',
  `coin_type` tinyint NOT NULL DEFAULT 0 COMMENT '硬币类型 1收入 2支出',
  `source_type` tinyint NOT NULL DEFAULT 0 COMMENT '来源类型 1充值 2投币 3打赏 4任务',
  `source_id` bigint NOT NULL DEFAULT 0 COMMENT '来源ID',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '变化原因',
  `balance` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '余额',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_coin_type`(`coin_type` ASC) USING BTREE,
  INDEX `idx_source_type`(`source_type` ASC) USING BTREE,
  INDEX `idx_source_id`(`source_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_coin_log_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户硬币记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_coin_records
-- ----------------------------
DROP TABLE IF EXISTS `user_coin_records`;
CREATE TABLE `user_coin_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `change_type` tinyint NOT NULL COMMENT '变动类型 1获得 2消费',
  `change_amount` decimal(10, 2) NOT NULL COMMENT '变动数量',
  `balance_after` decimal(10, 2) NOT NULL COMMENT '变动后余额',
  `source_type` tinyint NOT NULL COMMENT '来源类型 1签到 2投稿 3充值 4投币 5打赏',
  `source_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '来源ID',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_change_type`(`change_type` ASC) USING BTREE,
  INDEX `idx_source_type`(`source_type` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_coin_records_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户硬币记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_coupons
-- ----------------------------
DROP TABLE IF EXISTS `user_coupons`;
CREATE TABLE `user_coupons`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户优惠券ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `coupon_id` bigint UNSIGNED NOT NULL COMMENT '优惠券ID',
  `coupon_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '优惠券码',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1未使用 2已使用 3已过期',
  `used_time` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `used_order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '使用订单号',
  `expire_time` timestamp NOT NULL COMMENT '过期时间',
  `source` tinyint NOT NULL DEFAULT 1 COMMENT '来源 1系统发放 2用户领取 3活动赠送 4推荐奖励',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_coupon_code`(`coupon_code` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_coupon_id`(`coupon_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_expire_time`(`expire_time` ASC) USING BTREE,
  INDEX `idx_used_time`(`used_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_coupons_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_coupons_ibfk_2` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`coupon_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户优惠券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_devices
-- ----------------------------
DROP TABLE IF EXISTS `user_devices`;
CREATE TABLE `user_devices`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `device_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备ID',
  `device_type` int NULL DEFAULT NULL,
  `device_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备名称',
  `device_brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备品牌',
  `device_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备型号',
  `os_type` int NULL DEFAULT NULL,
  `os_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统版本',
  `app_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用版本',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `screen_resolution` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '屏幕分辨率',
  `network_type` int NULL DEFAULT NULL,
  `is_trusted` bit(1) NULL DEFAULT NULL,
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int NOT NULL DEFAULT 0 COMMENT '登录次数',
  `status` int NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_device_id`(`device_id` ASC) USING BTREE,
  INDEX `idx_device_type`(`device_type` ASC) USING BTREE,
  INDEX `idx_os_type`(`os_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_last_login_time`(`last_login_time` ASC) USING BTREE,
  CONSTRAINT `user_devices_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户设备表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_exp_log
-- ----------------------------
DROP TABLE IF EXISTS `user_exp_log`;
CREATE TABLE `user_exp_log`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `exp_change` int NOT NULL DEFAULT 0 COMMENT '经验变化',
  `exp_type` tinyint NOT NULL DEFAULT 0 COMMENT '经验类型 1登录 2观看 3投稿 4投币 5分享',
  `source_type` tinyint NOT NULL DEFAULT 0 COMMENT '来源类型 1视频 2文章 3直播 4动态',
  `source_id` bigint NOT NULL DEFAULT 0 COMMENT '来源ID',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '变化原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_exp_type`(`exp_type` ASC) USING BTREE,
  INDEX `idx_source_type`(`source_type` ASC) USING BTREE,
  INDEX `idx_source_id`(`source_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_exp_log_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户经验值记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_favorite_items
-- ----------------------------
DROP TABLE IF EXISTS `user_favorite_items`;
CREATE TABLE `user_favorite_items`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `favorite_id` bigint UNSIGNED NOT NULL COMMENT '收藏夹ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `item_type` tinyint NOT NULL DEFAULT 1 COMMENT '内容类型 1视频 2音频 3专栏 4相簿',
  `item_id` bigint UNSIGNED NOT NULL COMMENT '内容ID',
  `item_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容标题',
  `item_cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '内容封面',
  `item_author` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '内容作者',
  `item_duration` int NOT NULL DEFAULT 0 COMMENT '内容时长(秒)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_favorite_item`(`favorite_id` ASC, `item_type` ASC, `item_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_item_type_id`(`item_type` ASC, `item_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_favorite_items_ibfk_1` FOREIGN KEY (`favorite_id`) REFERENCES `user_favorites` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_favorite_items_ibfk_2` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '收藏夹内容表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_favorites
-- ----------------------------
DROP TABLE IF EXISTS `user_favorites`;
CREATE TABLE `user_favorites`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '收藏夹ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收藏夹标题',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收藏夹描述',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收藏夹封面',
  `is_public` tinyint NOT NULL DEFAULT 1 COMMENT '是否公开 0私密 1公开',
  `media_count` int NOT NULL DEFAULT 0 COMMENT '收藏数量',
  `view_count` int NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '类型 1视频 2音频 3专栏 4相簿',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_is_public`(`is_public` ASC) USING BTREE,
  CONSTRAINT `user_favorites_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户收藏夹表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_follow_groups
-- ----------------------------
DROP TABLE IF EXISTS `user_follow_groups`;
CREATE TABLE `user_follow_groups`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `group_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分组名称',
  `group_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分组描述',
  `member_count` int NOT NULL DEFAULT 0 COMMENT '成员数量',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认分组',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_is_default`(`is_default` ASC) USING BTREE,
  CONSTRAINT `user_follow_groups_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户关注分组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_garb
-- ----------------------------
DROP TABLE IF EXISTS `user_garb`;
CREATE TABLE `user_garb`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `pendant_id` int NOT NULL DEFAULT 0 COMMENT '挂件ID',
  `pendant_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '挂件名称',
  `pendant_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '挂件图片',
  `pendant_image_enhance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '挂件增强图片',
  `pendant_image_enhance_frame` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '挂件增强帧图片',
  `pendant_expire` timestamp NULL DEFAULT NULL COMMENT '挂件过期时间',
  `nameplate_nid` int NOT NULL DEFAULT 0 COMMENT '铭牌ID',
  `nameplate_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '铭牌名称',
  `nameplate_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '铭牌图片',
  `nameplate_image_small` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '铭牌小图片',
  `nameplate_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '铭牌等级',
  `nameplate_condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '铭牌条件',
  `face_nft` tinyint NOT NULL DEFAULT 0 COMMENT '是否NFT头像',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_pendant_id`(`pendant_id` ASC) USING BTREE,
  INDEX `idx_nameplate_nid`(`nameplate_nid` ASC) USING BTREE,
  CONSTRAINT `user_garb_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户装扮表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_garb_owned
-- ----------------------------
DROP TABLE IF EXISTS `user_garb_owned`;
CREATE TABLE `user_garb_owned`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '拥有记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `item_id` bigint UNSIGNED NOT NULL COMMENT '装扮ID',
  `obtain_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `is_equipped` tinyint NOT NULL DEFAULT 0 COMMENT '是否佩戴 0否 1是',
  `obtain_type` tinyint NOT NULL DEFAULT 1 COMMENT '获得方式 1购买 2活动赠送 3系统赠送 4兑换',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单号',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_item`(`uid` ASC, `item_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_item_id`(`item_id` ASC) USING BTREE,
  INDEX `idx_is_equipped`(`is_equipped` ASC) USING BTREE,
  INDEX `idx_obtain_type`(`obtain_type` ASC) USING BTREE,
  INDEX `idx_expire_time`(`expire_time` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_garb_owned_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_garb_owned_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `garb_items` (`item_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户装扮拥有表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_levels
-- ----------------------------
DROP TABLE IF EXISTS `user_levels`;
CREATE TABLE `user_levels`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `level` int NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '等级名称',
  `min_exp` int NOT NULL DEFAULT 0 COMMENT '最小经验值',
  `max_exp` int NOT NULL DEFAULT 0 COMMENT '最大经验值',
  `color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#FFFFFF' COMMENT '等级颜色',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '等级图标',
  `privileges` json NULL COMMENT '等级特权',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `exp` int NOT NULL,
  `level_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `next_level_exp` int NOT NULL,
  `status` int NULL DEFAULT NULL,
  `uid` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `level`(`level` ASC) USING BTREE,
  INDEX `idx_level`(`level` ASC) USING BTREE,
  INDEX `idx_min_exp`(`min_exp` ASC) USING BTREE,
  INDEX `idx_max_exp`(`max_exp` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户等级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_login_devices
-- ----------------------------
DROP TABLE IF EXISTS `user_login_devices`;
CREATE TABLE `user_login_devices`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint NOT NULL COMMENT '用户UID',
  `device_fingerprint` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备指纹',
  `device_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备名称',
  `device_type` int NULL DEFAULT 1 COMMENT '设备类型：1浏览器 2手机APP 3平板APP 4桌面应用 5其他',
  `operating_system` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作系统',
  `browser_or_app` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '浏览器/应用',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地理位置',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'User Agent',
  `token_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '当前JWT Token的唯一标识',
  `first_login_time` datetime NULL DEFAULT NULL COMMENT '首次登录时间',
  `last_access_time` datetime NULL DEFAULT NULL COMMENT '最后访问时间',
  `login_count` int NULL DEFAULT 1 COMMENT '登录次数',
  `is_online` tinyint(1) NULL DEFAULT 1 COMMENT '是否在线',
  `is_trusted` tinyint(1) NULL DEFAULT 0 COMMENT '是否信任设备',
  `status` int NULL DEFAULT 1 COMMENT '状态：0已下线 1在线',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_device_fingerprint`(`uid` ASC, `device_fingerprint` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_device_fingerprint`(`device_fingerprint` ASC) USING BTREE,
  INDEX `idx_token_id`(`token_id` ASC) USING BTREE,
  INDEX `idx_last_access_time`(`last_access_time` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户登录设备管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_notification_settings
-- ----------------------------
DROP TABLE IF EXISTS `user_notification_settings`;
CREATE TABLE `user_notification_settings`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `reply_notify` tinyint NOT NULL DEFAULT 1 COMMENT '回复通知 0关闭 1开启',
  `at_notify` tinyint NOT NULL DEFAULT 1 COMMENT '@我的通知 0关闭 1开启',
  `like_notify` tinyint NOT NULL DEFAULT 1 COMMENT '点赞通知 0关闭 1开启',
  `follow_notify` tinyint NOT NULL DEFAULT 1 COMMENT '关注通知 0关闭 1开启',
  `system_notify` tinyint NOT NULL DEFAULT 1 COMMENT '系统通知 0关闭 1开启',
  `live_notify` tinyint NOT NULL DEFAULT 1 COMMENT '直播通知 0关闭 1开启',
  `dynamic_notify` tinyint NOT NULL DEFAULT 1 COMMENT '动态通知 0关闭 1开启',
  `email_notify` tinyint NOT NULL DEFAULT 0 COMMENT '邮件通知 0关闭 1开启',
  `sms_notify` tinyint NOT NULL DEFAULT 0 COMMENT '短信通知 0关闭 1开启',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  CONSTRAINT `user_notification_settings_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户通知设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_official_verify
-- ----------------------------
DROP TABLE IF EXISTS `user_official_verify`;
CREATE TABLE `user_official_verify`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `type` tinyint NOT NULL DEFAULT -1 COMMENT '认证类型 -1无 0个人 1机构',
  `role` tinyint NOT NULL DEFAULT 0 COMMENT '角色类型',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '认证标题',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '认证描述',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态 0待审核 1通过 2拒绝',
  `apply_time` timestamp NULL DEFAULT NULL COMMENT '申请时间',
  `verify_time` timestamp NULL DEFAULT NULL COMMENT '认证时间',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_apply_time`(`apply_time` ASC) USING BTREE,
  CONSTRAINT `user_official_verify_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户认证表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_operation_logs
-- ----------------------------
DROP TABLE IF EXISTS `user_operation_logs`;
CREATE TABLE `user_operation_logs`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作动作',
  `resource_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源类型',
  `resource_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '资源ID',
  `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求方法',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求URL',
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地理位置',
  `platform` tinyint NOT NULL DEFAULT 1 COMMENT '平台类型 1web 2ios 3android 4pc',
  `device_info` json NULL COMMENT '设备信息',
  `request_data` json NULL COMMENT '请求数据',
  `response_data` json NULL COMMENT '响应数据',
  `status_code` int NOT NULL DEFAULT 200 COMMENT '响应状态码',
  `duration` int NOT NULL DEFAULT 0 COMMENT '请求耗时(毫秒)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_action`(`action` ASC) USING BTREE,
  INDEX `idx_resource_type`(`resource_type` ASC) USING BTREE,
  INDEX `idx_resource_id`(`resource_id` ASC) USING BTREE,
  INDEX `idx_ip`(`ip` ASC) USING BTREE,
  INDEX `idx_platform`(`platform` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_operation_logs_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户操作日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_privacy_settings
-- ----------------------------
DROP TABLE IF EXISTS `user_privacy_settings`;
CREATE TABLE `user_privacy_settings`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `show_favorites` tinyint NOT NULL DEFAULT 1 COMMENT '公开我的收藏 0否 1是',
  `show_bangumi` tinyint NOT NULL DEFAULT 1 COMMENT '公开我的追番追剧 0否 1是',
  `show_coins` tinyint NOT NULL DEFAULT 1 COMMENT '公开最近投币的视频 0否 1是',
  `show_likes` tinyint NOT NULL DEFAULT 1 COMMENT '公开最近点赞的视频 0否 1是',
  `show_following` tinyint NOT NULL DEFAULT 1 COMMENT '公开我的关注列表 0否 1是',
  `show_followers` tinyint NOT NULL DEFAULT 1 COMMENT '公开我的粉丝列表 0否 1是',
  `show_birthday` tinyint NOT NULL DEFAULT 1 COMMENT '公开我的生日、个人标签 0否 1是',
  `show_school` tinyint NOT NULL DEFAULT 1 COMMENT '公开学校信息 0否 1是',
  `show_games` tinyint NOT NULL DEFAULT 1 COMMENT '公开最近玩过的游戏 0否 1是',
  `show_fan_badge` tinyint NOT NULL DEFAULT 1 COMMENT '公开佩戴的粉丝勋章 0否 1是',
  `show_manga` tinyint NOT NULL DEFAULT 1 COMMENT '公开我的追漫 0否 1是',
  `show_fan_dress` tinyint NOT NULL DEFAULT 1 COMMENT '公开拥有的粉丝装扮 0否 1是',
  `show_live_replay` tinyint NOT NULL DEFAULT 1 COMMENT '投稿视频列表中展现直播回放 0否 1是',
  `show_class_video` tinyint NOT NULL DEFAULT 1 COMMENT '投稿视频列表中展现课堂视频 0否 1是',
  `show_charge_video` tinyint NOT NULL DEFAULT 1 COMMENT '投稿视频列表中展现包月充电专属视频 0否 1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  CONSTRAINT `user_privacy_settings_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户隐私设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_real_name_auth
-- ----------------------------
DROP TABLE IF EXISTS `user_real_name_auth`;
CREATE TABLE `user_real_name_auth`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '真实姓名',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '身份证号',
  `id_card_front` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证正面照片',
  `id_card_back` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证背面照片',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '认证状态 0待审核 1已通过 2已拒绝',
  `reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `submit_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `audit_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `auditor_uid` bigint UNSIGNED NULL DEFAULT NULL COMMENT '审核员ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_id_card`(`id_card` ASC) USING BTREE,
  CONSTRAINT `user_real_name_auth_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户实名认证表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_relations
-- ----------------------------
DROP TABLE IF EXISTS `user_relations`;
CREATE TABLE `user_relations`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `target_uid` bigint UNSIGNED NOT NULL COMMENT '目标用户ID',
  `relation_type` tinyint NOT NULL DEFAULT 1 COMMENT '关系类型 1关注 2拉黑 3特别关注 4悄悄关注',
  `attribute` tinyint NOT NULL DEFAULT 0 COMMENT '关系属性',
  `is_mutual` tinyint NOT NULL DEFAULT 0 COMMENT '是否互相关注',
  `group_id` int NOT NULL DEFAULT 0 COMMENT '分组ID',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_target`(`uid` ASC, `target_uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_target_uid`(`target_uid` ASC) USING BTREE,
  INDEX `idx_relation_type`(`relation_type` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `user_relations_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_relations_ibfk_2` FOREIGN KEY (`target_uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_security
-- ----------------------------
DROP TABLE IF EXISTS `user_security`;
CREATE TABLE `user_security`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `two_factor_enabled` tinyint NOT NULL DEFAULT 0 COMMENT '是否开启双因子认证',
  `two_factor_type` tinyint NOT NULL DEFAULT 0 COMMENT '双因子类型 1短信 2邮箱 3谷歌验证器',
  `two_factor_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '双因子密钥',
  `login_protection` tinyint NOT NULL DEFAULT 0 COMMENT '登录保护',
  `payment_protection` tinyint NOT NULL DEFAULT 0 COMMENT '支付保护',
  `privacy_level` tinyint NOT NULL DEFAULT 0 COMMENT '隐私等级 0公开 1部分公开 2私密',
  `allow_stranger_msg` tinyint NOT NULL DEFAULT 1 COMMENT '允许陌生人私信',
  `allow_stranger_follow` tinyint NOT NULL DEFAULT 1 COMMENT '允许陌生人关注',
  `block_keywords` json NULL COMMENT '屏蔽关键词',
  `blocked_users` json NULL COMMENT '屏蔽用户列表',
  `password_update_time` timestamp NULL DEFAULT NULL COMMENT '密码更新时间',
  `security_question` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '安全问题',
  `security_answer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '安全答案',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_two_factor_enabled`(`two_factor_enabled` ASC) USING BTREE,
  INDEX `idx_privacy_level`(`privacy_level` ASC) USING BTREE,
  CONSTRAINT `user_security_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户安全设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_space
-- ----------------------------
DROP TABLE IF EXISTS `user_space`;
CREATE TABLE `user_space`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `top_photo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '空间头图',
  `theme_id` int NOT NULL DEFAULT 0 COMMENT '主题ID',
  `theme_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主题名称',
  `theme_package` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主题包',
  `loading_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '加载动画',
  `tail_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '尾部图标',
  `head_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头部图标',
  `color_config` json NULL COMMENT '颜色配置',
  `bg_id` int NOT NULL DEFAULT 0 COMMENT '背景ID',
  `bg_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '背景名称',
  `bg_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '背景图片',
  `bg_image_small` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '背景小图片',
  `bg_image_original` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '背景原图',
  `privacy_settings` json NULL COMMENT '隐私设置',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_theme_id`(`theme_id` ASC) USING BTREE,
  INDEX `idx_bg_id`(`bg_id` ASC) USING BTREE,
  CONSTRAINT `user_space_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户空间配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_stats
-- ----------------------------
DROP TABLE IF EXISTS `user_stats`;
CREATE TABLE `user_stats`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `following_count` int NOT NULL DEFAULT 0 COMMENT '关注数',
  `follower_count` int NOT NULL DEFAULT 0 COMMENT '粉丝数',
  `video_count` int NOT NULL DEFAULT 0 COMMENT '视频数',
  `view_count` bigint NOT NULL DEFAULT 0 COMMENT '总播放数',
  `like_count` bigint NOT NULL DEFAULT 0 COMMENT '总点赞数',
  `coin_count` bigint NOT NULL DEFAULT 0 COMMENT '总投币数',
  `favorite_count` bigint NOT NULL DEFAULT 0 COMMENT '总收藏数',
  `share_count` bigint NOT NULL DEFAULT 0 COMMENT '总分享数',
  `comment_count` bigint NOT NULL DEFAULT 0 COMMENT '总评论数',
  `danmu_count` bigint NOT NULL DEFAULT 0 COMMENT '总弹幕数',
  `live_time` bigint NOT NULL DEFAULT 0 COMMENT '直播时长(秒)',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_following_count`(`following_count` ASC) USING BTREE,
  INDEX `idx_follower_count`(`follower_count` ASC) USING BTREE,
  INDEX `idx_video_count`(`video_count` ASC) USING BTREE,
  INDEX `idx_view_count`(`view_count` ASC) USING BTREE,
  CONSTRAINT `user_stats_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_vip
-- ----------------------------
DROP TABLE IF EXISTS `user_vip`;
CREATE TABLE `user_vip`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `vip_type` tinyint NOT NULL DEFAULT 0 COMMENT 'VIP类型 0无 1月会员 2年会员',
  `vip_status` tinyint NOT NULL DEFAULT 0 COMMENT 'VIP状态 0无效 1有效',
  `vip_start_time` timestamp NULL DEFAULT NULL COMMENT 'VIP开始时间',
  `vip_due_date` timestamp NULL DEFAULT NULL COMMENT 'VIP到期时间',
  `vip_pay_type` tinyint NOT NULL DEFAULT 0 COMMENT '支付类型 0无 1支付宝 2微信',
  `theme_type` tinyint NOT NULL DEFAULT 0 COMMENT '主题类型',
  `label_theme` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签主题',
  `label_text` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签文本',
  `label_text_color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签文本颜色',
  `label_bg_color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签背景颜色',
  `label_bg_style` tinyint NOT NULL DEFAULT 0 COMMENT '标签背景样式',
  `label_border_color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签边框颜色',
  `avatar_subscript` tinyint NOT NULL DEFAULT 0 COMMENT '头像角标',
  `avatar_subscript_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像角标URL',
  `nickname_color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称颜色',
  `role` tinyint NOT NULL DEFAULT 0 COMMENT '角色',
  `tv_vip_status` tinyint NOT NULL DEFAULT 0 COMMENT 'TV VIP状态',
  `tv_vip_pay_type` tinyint NOT NULL DEFAULT 0 COMMENT 'TV VIP支付类型',
  `tv_due_date` timestamp NULL DEFAULT NULL COMMENT 'TV到期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_vip_type`(`vip_type` ASC) USING BTREE,
  INDEX `idx_vip_status`(`vip_status` ASC) USING BTREE,
  INDEX `idx_vip_due_date`(`vip_due_date` ASC) USING BTREE,
  CONSTRAINT `user_vip_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户VIP表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_vip_privileges
-- ----------------------------
DROP TABLE IF EXISTS `user_vip_privileges`;
CREATE TABLE `user_vip_privileges`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `privilege_type` tinyint NOT NULL DEFAULT 0 COMMENT '权益类型 1免广告 2高清 3下载 4空间装扮 5表情包',
  `privilege_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权益名称',
  `privilege_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '权益描述',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用',
  `usage_count` int NOT NULL DEFAULT 0 COMMENT '使用次数',
  `usage_limit` int NOT NULL DEFAULT 0 COMMENT '使用限制 0无限制',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_privilege_type`(`privilege_type` ASC) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE,
  INDEX `idx_expire_time`(`expire_time` ASC) USING BTREE,
  CONSTRAINT `user_vip_privileges_ibfk_1` FOREIGN KEY (`uid`) REFERENCES `users` (`uid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户会员权益表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户唯一标识',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '',
  `salt` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `banner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '横幅URL',
  `gender` int NULL DEFAULT NULL,
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `signature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '个人签名',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '位置',
  `level` int NULL DEFAULT NULL,
  `exp` int NOT NULL DEFAULT 0 COMMENT '经验值',
  `coins` double NULL DEFAULT NULL,
  `vip_type` int NULL DEFAULT NULL,
  `vip_status` int NULL DEFAULT NULL,
  `vip_due_date` timestamp NULL DEFAULT NULL COMMENT 'VIP到期时间',
  `status` int NULL DEFAULT NULL,
  `is_verified` bit(1) NULL DEFAULT NULL,
  `verified_type` int NULL DEFAULT NULL,
  `verified_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '认证信息',
  `register_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `phone_bound` bit(1) NULL DEFAULT NULL,
  `wechat_open_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `wechat_union_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `school` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '学校信息',
  `personal_tags` json NULL COMMENT '个人标签',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号',
  `real_name_verified` int NULL DEFAULT NULL,
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除标记: 0=未删除, 1=已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid` ASC) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  UNIQUE INDEX `email`(`email` ASC) USING BTREE,
  UNIQUE INDEX `phone`(`phone` ASC) USING BTREE,
  UNIQUE INDEX `UK_aqa1b3jlr4kptna2h00ucodh0`(`wechat_open_id` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_username`(`username` ASC) USING BTREE,
  INDEX `idx_email`(`email` ASC) USING BTREE,
  INDEX `idx_phone`(`phone` ASC) USING BTREE,
  INDEX `idx_level`(`level` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_vip_status`(`vip_status` ASC) USING BTREE,
  INDEX `idx_vip_type`(`vip_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户基础信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for video_audit_logs
-- ----------------------------
DROP TABLE IF EXISTS `video_audit_logs`;
CREATE TABLE `video_audit_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `aid` bigint NOT NULL COMMENT '视频AV号',
  `auditor_uid` bigint NULL DEFAULT NULL COMMENT '审核员UID',
  `audit_type` tinyint NOT NULL COMMENT '审核类型: 1机器审核 2人工审核',
  `audit_result` tinyint NOT NULL COMMENT '审核结果: 0待审核 1通过 2不通过',
  `audit_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核理由',
  `audit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_aid`(`aid` ASC) USING BTREE,
  INDEX `idx_auditor_time`(`auditor_uid` ASC, `audit_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频审核记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_categories
-- ----------------------------
DROP TABLE IF EXISTS `video_categories`;
CREATE TABLE `video_categories`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分区名称',
  `parent_id` int NOT NULL DEFAULT 0 COMMENT '父分区ID',
  `icon` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标URL',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态: 0禁用 1启用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_sort`(`parent_id` ASC, `sort_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频分区表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_coin_records
-- ----------------------------
DROP TABLE IF EXISTS `video_coin_records`;
CREATE TABLE `video_coin_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '投币记录ID',
  `uid` bigint NOT NULL COMMENT '用户UID',
  `aid` bigint NOT NULL COMMENT '视频AID',
  `coin_num` tinyint NOT NULL DEFAULT 1 COMMENT '投币数量: 1或2',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态: 1=有效 0=已撤销',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '投币时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_aid`(`uid` ASC, `aid` ASC) USING BTREE,
  INDEX `idx_aid_status`(`aid` ASC, `status` ASC) USING BTREE,
  INDEX `idx_uid_created`(`uid` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频投币记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_comments
-- ----------------------------
DROP TABLE IF EXISTS `video_comments`;
CREATE TABLE `video_comments`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `aid` bigint NOT NULL COMMENT '视频AID',
  `uid` bigint NOT NULL COMMENT '用户UID',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父评论ID: 0=主评论',
  `root_id` bigint NULL DEFAULT 0 COMMENT '根评论ID: 0=主评论',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `reply_count` int NULL DEFAULT 0 COMMENT '回复数',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态: 1=正常 0=已删除 2=待审核',
  `ip_hash` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP哈希值',
  `device_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备类型',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_aid_created`(`aid` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_root_id`(`root_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_uid_created`(`uid` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_danmaku
-- ----------------------------
DROP TABLE IF EXISTS `video_danmaku`;
CREATE TABLE `video_danmaku`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '弹幕ID',
  `aid` bigint NOT NULL COMMENT '视频AID',
  `uid` bigint NOT NULL COMMENT '用户UID',
  `content` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '弹幕内容',
  `time_point` decimal(10, 3) NOT NULL COMMENT '弹幕时间点(秒)',
  `mode` tinyint NOT NULL DEFAULT 1 COMMENT '弹幕模式: 1=滚动 2=顶部 3=底部',
  `font_size` tinyint NOT NULL DEFAULT 25 COMMENT '字体大小',
  `color` int NOT NULL DEFAULT 16777215 COMMENT '颜色值',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态: 1=正常 0=已删除',
  `ip_hash` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP哈希值',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_aid_time`(`aid` ASC, `time_point` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_uid_created`(`uid` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频弹幕表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_favorite_folders
-- ----------------------------
DROP TABLE IF EXISTS `video_favorite_folders`;
CREATE TABLE `video_favorite_folders`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '收藏夹ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收藏夹名称',
  `is_private` tinyint(1) NULL DEFAULT 0 COMMENT '是否私密收藏夹',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态: 1=正常, 0=逻辑删除',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_uid_name`(`uid` ASC, `name` ASC) USING BTREE,
  INDEX `idx_uid_status`(`uid` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户收藏夹表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_favorite_records
-- ----------------------------
DROP TABLE IF EXISTS `video_favorite_records`;
CREATE TABLE `video_favorite_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `aid` bigint UNSIGNED NOT NULL COMMENT '视频ID',
  `folder_id` bigint UNSIGNED NOT NULL COMMENT '收藏夹ID',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态: 1=已收藏, 0=已取消',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_uid_aid_folder`(`uid` ASC, `aid` ASC, `folder_id` ASC) USING BTREE,
  INDEX `idx_aid_status`(`aid` ASC, `status` ASC) USING BTREE,
  INDEX `idx_uid_created`(`uid` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频收藏记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_files
-- ----------------------------
DROP TABLE IF EXISTS `video_files`;
CREATE TABLE `video_files`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `aid` bigint NOT NULL COMMENT '视频AV号',
  `quality` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `cdn_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'CDN加速URL',
  `file_size` bigint NULL DEFAULT NULL,
  `file_md5` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件MD5',
  `duration` bigint NULL DEFAULT NULL,
  `bitrate` int NULL DEFAULT NULL COMMENT '码率',
  `format` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'mp4',
  `width` int NULL DEFAULT NULL COMMENT '视频宽度',
  `height` int NULL DEFAULT NULL COMMENT '视频高度',
  `fps` int NULL DEFAULT 30 COMMENT '帧率',
  `codec` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码格式',
  `video_codec` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'h264' COMMENT '视频编码',
  `audio_codec` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'aac' COMMENT '音频编码',
  `container_format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'mp4' COMMENT '容器格式',
  `storage_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'minio' COMMENT '存储类型: minio,oss,cos',
  `status` bigint NULL DEFAULT 0,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `a_id` bigint UNSIGNED NOT NULL,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_aid_quality`(`aid` ASC, `quality` ASC) USING BTREE,
  INDEX `fk_videos_video_files`(`a_id` ASC) USING BTREE,
  INDEX `idx_aid`(`aid` ASC) USING BTREE,
  INDEX `idx_file_md5`(`file_md5` ASC) USING BTREE,
  INDEX `idx_video_files_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频文件信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_info
-- ----------------------------
DROP TABLE IF EXISTS `video_info`;
CREATE TABLE `video_info`  (
  `aid` bigint NOT NULL AUTO_INCREMENT COMMENT '视频AV号',
  `bvid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '视频BV号',
  `upload_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联的上传任务ID',
  `uid` bigint NOT NULL COMMENT '上传者UID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '视频标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '视频描述',
  `cover` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '封面URL',
  `duration` int NOT NULL DEFAULT 0 COMMENT '视频时长(秒)',
  `category_id` int NOT NULL COMMENT '分区ID',
  `tags` json NULL COMMENT '视频标签',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态: 0待审核 1已发布 2已下架 3审核不通过',
  `process_status` tinyint NULL DEFAULT 0 COMMENT '处理状态: 0=待处理 1=处理中 2=处理完成 3=处理失败',
  `privacy` tinyint NOT NULL DEFAULT 0 COMMENT '隐私: 0公开 1私密 2仅粉丝',
  `original_filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原始文件名',
  `file_size` bigint NULL DEFAULT NULL COMMENT '文件大小(字节)',
  `source_file_md5` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源文件MD5',
  `video_codec` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '视频编码格式',
  `audio_codec` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '音频编码格式',
  `resolution` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分辨率',
  `fps` int NULL DEFAULT NULL COMMENT '帧率',
  `bitrate` int NULL DEFAULT NULL COMMENT '码率',
  `upload_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `publish_time` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记',
  `version` int NULL DEFAULT 0,
  `allow_comment` tinyint NULL DEFAULT 1 COMMENT '是否允许评论: 1=允许 0=禁止',
  `allow_danmaku` tinyint NULL DEFAULT 1 COMMENT '是否允许弹幕: 1=允许 0=禁止',
  PRIMARY KEY (`aid`) USING BTREE,
  UNIQUE INDEX `uk_bvid`(`bvid` ASC) USING BTREE,
  INDEX `idx_category_publish`(`category_id` ASC, `publish_time` ASC) USING BTREE,
  INDEX `idx_category_publish_time`(`category_id` ASC, `publish_time` DESC) USING BTREE,
  INDEX `idx_category_status_publish`(`category_id` ASC, `status` ASC, `publish_time` DESC) USING BTREE,
  INDEX `idx_process_status`(`process_status` ASC) USING BTREE,
  INDEX `idx_status_publish`(`status` ASC, `publish_time` ASC) USING BTREE,
  INDEX `idx_uid_status`(`uid` ASC, `status` ASC, `publish_time` ASC) USING BTREE,
  INDEX `idx_uid_status_publish`(`uid` ASC, `status` ASC, `publish_time` DESC) USING BTREE,
  INDEX `idx_upload_id`(`upload_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频基础信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_like_records
-- ----------------------------
DROP TABLE IF EXISTS `video_like_records`;
CREATE TABLE `video_like_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '点赞记录ID',
  `uid` bigint NOT NULL COMMENT '用户UID',
  `aid` bigint NOT NULL COMMENT '视频AV号',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '点赞状态: 0=取消点赞 1=点赞',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_aid`(`uid` ASC, `aid` ASC) USING BTREE,
  INDEX `idx_aid_status`(`aid` ASC, `status` ASC) USING BTREE,
  INDEX `idx_uid_created`(`uid` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频点赞记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_play_history
-- ----------------------------
DROP TABLE IF EXISTS `video_play_history`;
CREATE TABLE `video_play_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uid` bigint NOT NULL COMMENT '用户UID',
  `aid` bigint NOT NULL COMMENT '视频AID',
  `play_progress` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '播放进度百分比',
  `play_duration` int NULL DEFAULT 0 COMMENT '本次播放时长(秒)',
  `last_position` decimal(10, 3) NULL DEFAULT 0.000 COMMENT '最后播放位置(秒)',
  `device_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备类型: web,mobile,app',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uid_aid`(`uid` ASC, `aid` ASC) USING BTREE,
  INDEX `idx_aid_updated`(`aid` ASC, `updated_at` ASC) USING BTREE,
  INDEX `idx_play_history_uid_aid`(`uid` ASC, `aid` ASC) USING BTREE,
  INDEX `idx_uid_updated`(`uid` ASC, `updated_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频播放历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_play_logs
-- ----------------------------
DROP TABLE IF EXISTS `video_play_logs`;
CREATE TABLE `video_play_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `aid` bigint NOT NULL COMMENT '视频AV号',
  `uid` bigint NOT NULL COMMENT '用户UID',
  `play_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '播放时间',
  `play_duration` int NOT NULL DEFAULT 0 COMMENT '播放时长(秒)',
  `play_progress` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '播放进度百分比',
  `quality` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '播放清晰度',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户代理',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `platform` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '平台: web,ios,android',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_aid_time`(`aid` ASC, `play_time` ASC) USING BTREE,
  INDEX `idx_uid_time`(`uid` ASC, `play_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频播放记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_process_queue
-- ----------------------------
DROP TABLE IF EXISTS `video_process_queue`;
CREATE TABLE `video_process_queue`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '队列ID',
  `aid` bigint NOT NULL COMMENT '视频AID',
  `task_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务类型: transcode,thumbnail,audit,analysis',
  `priority` int NULL DEFAULT 0 COMMENT '优先级: 数字越大优先级越高',
  `params` json NULL COMMENT '任务参数',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态: 0=待处理 1=处理中 2=完成 3=失败',
  `worker_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理节点ID',
  `progress` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '处理进度',
  `result` json NULL COMMENT '处理结果',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误信息',
  `retry_count` int NULL DEFAULT 0 COMMENT '重试次数',
  `max_retries` int NULL DEFAULT 3 COMMENT '最大重试次数',
  `scheduled_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '计划执行时间',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始处理时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_aid`(`aid` ASC) USING BTREE,
  INDEX `idx_scheduled_at`(`scheduled_at` ASC) USING BTREE,
  INDEX `idx_status_priority`(`status` ASC, `priority` DESC) USING BTREE,
  INDEX `idx_task_type_status`(`task_type` ASC, `status` ASC) USING BTREE,
  INDEX `idx_worker_id`(`worker_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频处理队列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_share_records
-- ----------------------------
DROP TABLE IF EXISTS `video_share_records`;
CREATE TABLE `video_share_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分享记录ID',
  `aid` bigint NOT NULL COMMENT '视频AID',
  `uid` bigint NOT NULL COMMENT '分享用户UID',
  `share_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分享类型: link,qq,wechat,weibo,copy',
  `share_target` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分享目标描述',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_aid_created`(`aid` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_share_type`(`share_type` ASC) USING BTREE,
  INDEX `idx_uid_created`(`uid` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频分享记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_stats
-- ----------------------------
DROP TABLE IF EXISTS `video_stats`;
CREATE TABLE `video_stats`  (
  `aid` bigint NOT NULL,
  `view_count` bigint NOT NULL DEFAULT 0 COMMENT '播放量',
  `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞数',
  `coin_count` int NOT NULL DEFAULT 0 COMMENT '投币数',
  `favorite_count` int NOT NULL DEFAULT 0 COMMENT '收藏数',
  `share_count` int NOT NULL DEFAULT 0 COMMENT '分享数',
  `comment_count` int NOT NULL DEFAULT 0 COMMENT '评论数',
  `danmaku_count` int NOT NULL DEFAULT 0 COMMENT '弹幕数',
  `score` decimal(3, 1) NOT NULL DEFAULT 0.0 COMMENT '综合评分',
  `hot_score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '热度分数',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`aid`) USING BTREE,
  INDEX `idx_hot_score`(`hot_score` ASC) USING BTREE,
  INDEX `idx_view_count`(`view_count` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_transcode_tasks
-- ----------------------------
DROP TABLE IF EXISTS `video_transcode_tasks`;
CREATE TABLE `video_transcode_tasks`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务唯一标识',
  `aid` bigint NOT NULL COMMENT '视频AV号',
  `original_file` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始文件路径',
  `target_quality` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '目标清晰度',
  `target_format` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '目标格式',
  `priority` int NULL DEFAULT 0 COMMENT '优先级',
  `progress` tinyint NOT NULL DEFAULT 0 COMMENT '进度百分比',
  `worker_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理节点ID',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态: 0待处理 1处理中 2已完成 3失败',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误信息',
  `retry_count` int NULL DEFAULT 0 COMMENT '重试次数',
  `max_retries` int NULL DEFAULT 3 COMMENT '最大重试次数',
  `output_file` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '输出文件路径',
  `file_size` bigint NULL DEFAULT NULL COMMENT '输出文件大小',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_aid`(`aid` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_priority`(`priority` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_task_id`(`task_id` ASC) USING BTREE,
  INDEX `idx_worker_id`(`worker_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频转码任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_upload_chunks
-- ----------------------------
DROP TABLE IF EXISTS `video_upload_chunks`;
CREATE TABLE `video_upload_chunks`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分片ID',
  `upload_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '上传任务ID',
  `chunk_number` int NOT NULL COMMENT '分片序号',
  `chunk_size` int NOT NULL COMMENT '分片大小(字节)',
  `chunk_md5` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分片MD5',
  `storage_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分片存储路径',
  `etag` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分片ETag',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态: 0=待上传 1=上传中 2=上传完成 3=上传失败',
  `retry_count` int NULL DEFAULT 0 COMMENT '重试次数',
  `uploaded_at` timestamp NULL DEFAULT NULL COMMENT '上传完成时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_upload_chunk`(`upload_id` ASC, `chunk_number` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_upload_id`(`upload_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频上传分片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_upload_tasks
-- ----------------------------
DROP TABLE IF EXISTS `video_upload_tasks`;
CREATE TABLE `video_upload_tasks`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '上传任务ID',
  `upload_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '上传任务唯一标识',
  `aid` bigint NULL DEFAULT NULL COMMENT '视频AID，上传完成后关联',
  `uid` bigint NOT NULL COMMENT '用户UID',
  `original_filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始文件名',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_md5` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件MD5',
  `file_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件MIME类型',
  `chunk_size` bigint NOT NULL COMMENT '分片大小(字节)',
  `total_chunks` bigint NOT NULL COMMENT '总分片数',
  `uploaded_chunks` json NULL COMMENT '已上传分片列表',
  `uploaded_size` bigint NULL DEFAULT 0 COMMENT '已上传大小',
  `upload_token` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '上传令牌',
  `storage_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '存储路径',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态: 0=待上传 1=上传中 2=上传完成 3=上传失败 4=已取消',
  `progress` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '上传进度百分比',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误信息',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '任务过期时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_upload_id`(`upload_id` ASC) USING BTREE,
  INDEX `idx_expires_at`(`expires_at` ASC) USING BTREE,
  INDEX `idx_status_created`(`status` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_uid_status`(`uid` ASC, `status` ASC) USING BTREE,
  INDEX `idx_upload_id`(`upload_id` ASC) USING BTREE,
  INDEX `idx_video_upload_tasks_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频上传任务表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
