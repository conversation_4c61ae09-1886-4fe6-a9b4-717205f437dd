# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: BASIC
  httpclient:
    enabled: true
    max-connections: 200
    max-connections-per-route: 50
    connection-timeout: 5000
    time-to-live: 900

# 熔断器配置
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000
      circuitBreaker:
        requestVolumeThreshold: 20
        sleepWindowInMilliseconds: 5000
        errorThresholdPercentage: 50

# OpenFeign 服务调用配置
ribbon:
  ReadTimeout: 10000
  ConnectTimeout: 5000
  MaxAutoRetries: 1
  MaxAutoRetriesNextServer: 2
