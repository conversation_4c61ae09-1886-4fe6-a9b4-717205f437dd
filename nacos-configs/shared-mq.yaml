# 共享RabbitMQ配置
# Data ID: shared-mq.yaml
# Group: DEFAULT_GROUP

spring:
  rabbitmq:
    host: ***********
    port: 5672
    username: admin
    password: "@Xc123456"
    virtual-host: /

    # 连接配置
    connection-timeout: 15000

    # 生产者配置
    publisher-confirm-type: correlated
    publisher-returns: true
    template:
      mandatory: true

    # 消费者配置
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 1000ms
          max-attempts: 3
          max-interval: 10000ms
          multiplier: 2

# RabbitMQ 消息队列配置
mq:
  bilibili:
    # 用户相关队列
    user:
      register:
        queue: bilibili.user.register
        exchange: bilibili.user.exchange
        routing-key: user.register
      login:
        queue: bilibili.user.login
        exchange: bilibili.user.exchange
        routing-key: user.login
    # 认证相关队列
    auth:
      token:
        queue: bilibili.auth.token
        exchange: bilibili.auth.exchange
        routing-key: auth.token
