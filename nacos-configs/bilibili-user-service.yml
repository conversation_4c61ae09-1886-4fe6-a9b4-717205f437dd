# Bilibili用户服务配置
# Data ID: bilibili-user-service.yml
# Group: DEFAULT_GROUP

# 服务基础配置
server:
  port: 8091

# 应用配置
spring:
  application:
    name: user-service

# 用户服务特定配置
user:
  # 用户等级配置
  level:
    # 等级计算规则
    exp-rules:
      # 观看视频经验值
      watch-video: 5
      # 点赞经验值
      like-video: 2
      # 投币经验值
      coin-video: 10
      # 分享经验值
      share-video: 5
      # 评论经验值
      comment: 3
      # 每日登录经验值
      daily-login: 10
    # 等级经验值要求
    level-exp:
      1: 0
      2: 200
      3: 1500
      4: 4500
      5: 10800
      6: 28800
    # 等级权益配置
    level-privileges:
      1: ["基础观看", "基础评论"]
      2: ["基础观看", "基础评论", "点赞"]
      3: ["基础观看", "基础评论", "点赞", "投币"]
      4: ["基础观看", "基础评论", "点赞", "投币", "分享"]
      5: ["基础观看", "基础评论", "点赞", "投币", "分享", "高级弹幕"]
      6: ["基础观看", "基础评论", "点赞", "投币", "分享", "高级弹幕", "直播"]

  # 用户头像配置
  avatar:
    # 默认头像URL
    default-url: "https://static.bilibili.com/avatar/default.jpg"
    # 头像上传限制
    max-size: 5MB
    allowed-formats: ["jpg", "jpeg", "png", "gif"]
    # 头像存储路径
    upload-path: "/uploads/avatars/"

  # 用户昵称配置
  nickname:
    # 昵称长度限制
    min-length: 2
    max-length: 20
    # 禁用词汇检查
    enable-word-filter: true
    # 昵称修改冷却时间（天）
    change-cooldown-days: 30

  # 用户隐私设置
  privacy:
    # 默认隐私设置
    default-settings:
      show-following: true
      show-followers: true
      show-favorites: false
      show-watch-history: false
      allow-private-message: true
      allow-friend-request: true

  # 用户关系配置
  relationship:
    # 最大关注数量
    max-following: 2000
    # 最大粉丝数量（-1表示无限制）
    max-followers: -1
    # 黑名单最大数量
    max-blacklist: 1000

  # 用户统计配置
  stats:
    # 统计数据更新间隔（分钟）
    update-interval: 30
    # 是否启用实时统计
    enable-realtime: false
    # 统计数据缓存时间（小时）
    cache-hours: 24

# 业务规则配置
business:
  # 用户注册规则
  register:
    # 是否需要邮箱验证
    require-email-verification: false
    # 是否需要手机验证
    require-phone-verification: true
    # 新用户默认等级
    default-level: 1
    # 新用户初始经验值
    initial-exp: 0

  # 用户认证规则
  verification:
    # 实名认证配置
    real-name:
      enabled: true
      required-for-upload: false
    # 企业认证配置
    enterprise:
      enabled: true
      review-required: true

# 缓存配置
cache:
  # 用户信息缓存时间（秒）
  user-info-ttl: 3600
  # 用户统计缓存时间（秒）
  user-stats-ttl: 1800
  # 用户关系缓存时间（秒）
  user-relationship-ttl: 7200

# 日志配置
logging:
  level:
    com.bilibili.user: DEBUG
    com.bilibili.user.service: DEBUG
    com.bilibili.user.mapper: DEBUG
    com.baomidou.mybatisplus: INFO
    org.apache.ibatis: INFO
  file:
    name: logs/bilibili-user.log
