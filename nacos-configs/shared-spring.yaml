# 共享Spring配置
# Data ID: shared-spring.yaml
# Group: DEFAULT_GROUP

spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************************************
    username: root
    password: "@Xc123456"
    type: com.alibaba.druid.pool.DruidDataSource

    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20

      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin

      # Web统计过滤器
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

      # SQL监控
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 2000
        wall:
          enabled: true
          config:
            multi-statement-allow: true

  # Jackson JSON配置
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
      write-date-timestamps-as-nanoseconds: false
    deserialization:
      read-date-timestamps-as-nanoseconds: false
    generator:
      write-numbers-as-strings: false
    parser:
      allow-comments: true
      allow-yaml-comments: true

  # 主配置
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: false
    banner-mode: console  # 保留横幅但简化
    log-startup-info: false  # 减少启动信息输出
    lazy-initialization: false

  # MVC配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    servlet:
      load-on-startup: 1
    format:
      date: yyyy-MM-dd
      date-time: yyyy-MM-dd HH:mm:ss
      time: HH:mm:ss

  # JPA配置（如果使用）
  jpa:
    hibernate:
      ddl-auto: update  # 改为update，自动更新表结构
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        batch_versioned_data: true

  # 事务配置
  transaction:
    default-timeout: 30
    rollback-on-commit-failure: true

  # 任务调度配置
  task:
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: "bilibili-task-"
    scheduling:
      pool:
        size: 4
      thread-name-prefix: "bilibili-scheduling-"

  # 国际化配置
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600s
    fallback-to-system-locale: true

# 服务器配置
server:
  # 编码配置
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

  # 错误页面配置
  error:
    whitelabel:
      enabled: false
    include-message: always
    include-binding-errors: always
    include-stacktrace: on_param
    include-exception: false


