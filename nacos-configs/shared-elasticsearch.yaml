# 共享Elasticsearch配置
# Data ID: shared-elasticsearch.yaml
# Group: DEFAULT_GROUP

spring:
  # Elasticsearch配置
  elasticsearch:
    # 连接配置
    uris: http://8.156.72.38:9200
    username: 
    password: 
    connection-timeout: 10s
    socket-timeout: 30s
    
    # 连接池配置
    webclient:
      max-in-memory-size: 100MB
    
    # 客户端配置
    restclient:
      sniffer:
        interval: 5m
        delay-after-failure: 1m
      
  # Spring Data Elasticsearch配置
  data:
    elasticsearch:
      # 仓库配置
      repositories:
        enabled: true
      
      # 客户端配置
      client:
        reactive:
          endpoints: 8.156.72.38:9200
          use-ssl: false
          socket-timeout: 30s
          connection-timeout: 10s
          max-in-memory-size: 100MB

# Elasticsearch自定义配置
elasticsearch:
  # 集群配置
  cluster:
    name: bili-es-cluster
    nodes: 8.156.72.38:9200
  
  # 索引配置
  index:
    # 默认分片数
    number-of-shards: 1
    # 默认副本数
    number-of-replicas: 0
    # 刷新间隔
    refresh-interval: 1s
    # 最大结果窗口
    max-result-window: 10000
  
  # 搜索配置
  search:
    # 默认搜索超时时间
    timeout: 30s
    # 默认分页大小
    default-size: 20
    # 最大分页大小
    max-size: 1000
    # 高亮配置
    highlight:
      pre-tags: ["<em>"]
      post-tags: ["</em>"]
      fragment-size: 100
      number-of-fragments: 3
  
  # 批量操作配置
  bulk:
    # 批量大小
    size: 1000
    # 刷新策略
    refresh: false
    # 超时时间
    timeout: 60s
  
  # 模板配置
  template:
    # 用户相关索引模板
    user:
      index-patterns: ["bilibili-user-*"]
      settings:
        number-of-shards: 1
        number-of-replicas: 0
        refresh-interval: "5s"
      mappings:
        properties:
          uid:
            type: long
          username:
            type: keyword
          nickname:
            type: text
            analyzer: ik_max_word
            search_analyzer: ik_smart
          email:
            type: keyword
          phone:
            type: keyword
          avatar:
            type: keyword
          bio:
            type: text
            analyzer: ik_max_word
          created_time:
            type: date
            format: "yyyy-MM-dd HH:mm:ss"
          updated_time:
            type: date
            format: "yyyy-MM-dd HH:mm:ss"
    
    # 内容相关索引模板
    content:
      index-patterns: ["bilibili-content-*"]
      settings:
        number-of-shards: 2
        number-of-replicas: 0
        refresh-interval: "1s"
      mappings:
        properties:
          id:
            type: long
          title:
            type: text
            analyzer: ik_max_word
            search_analyzer: ik_smart
          content:
            type: text
            analyzer: ik_max_word
            search_analyzer: ik_smart
          tags:
            type: keyword
          category:
            type: keyword
          author_id:
            type: long
          created_time:
            type: date
            format: "yyyy-MM-dd HH:mm:ss"
          view_count:
            type: long
          like_count:
            type: long

# 健康检查配置
management:
  health:
    elasticsearch:
      enabled: true
