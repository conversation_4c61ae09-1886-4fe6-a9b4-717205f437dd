# Bilibili网关服务配置
# Data ID: bilibili-gateway-service.yml
# Group: DEFAULT_GROUP

# 服务基础配置
server:
  port: 8080

# 应用配置
spring:
  application:
    name: gateway-service

# 网关特定配置
gateway:
  # 路由配置
  routes:
    # 认证服务路由
    auth-service:
      enabled: true
      strip-prefix: 2
      timeout: 30s
    
    # 用户服务路由
    user-service:
      enabled: true
      strip-prefix: 2
      timeout: 30s
    
    # AI助手服务路由
    ai-service:
      enabled: true
      strip-prefix: 2
      timeout: 60s
  
  # 限流配置
  rate-limit:
    # 默认限流配置
    default:
      replenish-rate: 100
      burst-capacity: 200
      requested-tokens: 1
    
    # 认证接口限流
    auth:
      replenish-rate: 10
      burst-capacity: 20
      requested-tokens: 1
    
    # 用户接口限流
    user:
      replenish-rate: 50
      burst-capacity: 100
      requested-tokens: 1
    
    # AI接口限流
    ai:
      replenish-rate: 5
      burst-capacity: 10
      requested-tokens: 1
  
  # 熔断配置
  circuit-breaker:
    enabled: true
    failure-rate-threshold: 50
    slow-call-rate-threshold: 50
    slow-call-duration-threshold: 2s
    minimum-number-of-calls: 10
    sliding-window-size: 100
    wait-duration-in-open-state: 30s
  
  # 重试配置
  retry:
    enabled: true
    retries: 3
    statuses: BAD_GATEWAY,GATEWAY_TIMEOUT
    methods: GET,POST
    exceptions: java.io.IOException,java.util.concurrent.TimeoutException

# 跨域配置
cors:
  enabled: true
  allowed-origins: 
    - "http://localhost:3000"
    - "http://localhost:8080"
    - "https://bilibili.com"
  allowed-methods: 
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600

# 安全配置
security:
  # JWT白名单路径
  jwt:
    whitelist:
      - "/api/v1/auth/login-by-password"
      - "/api/v1/auth/login-by-code"
      - "/api/v1/auth/send-code"
      - "/api/v1/auth/refresh-token"
      - "/api/v1/auth/logout"
      - "/api/v1/auth/register"
      - "/api/v1/auth/wechat/**"
      - "/api/v1/auth/health"
      - "/api/v1/user/test/**"
      - "/actuator/**"
      - "/doc.html"
      - "/docs/**"
      - "/swagger-ui/**"
      - "/v3/api-docs/**"
      - "/webjars/**"
      - "/favicon.ico"
  
  # IP白名单（可选）
  ip:
    whitelist:
      enabled: false
      addresses: []
  
  # 请求头安全
  headers:
    frame-options: DENY
    content-type-options: nosniff
    xss-protection: "1; mode=block"
    referrer-policy: strict-origin-when-cross-origin

# 日志配置
logging:
  level:
    com.bilibili.gateway: DEBUG
    org.springframework.cloud.gateway: INFO
    org.springframework.web.reactive: INFO
    reactor.netty: INFO
  file:
    name: logs/bilibili-gateway.log

# 缓存配置
cache:
  # 路由缓存时间（秒）
  route-cache-ttl: 300
  # 用户信息缓存时间（秒）
  user-info-ttl: 600
  # JWT缓存时间（秒）
  jwt-cache-ttl: 3600
