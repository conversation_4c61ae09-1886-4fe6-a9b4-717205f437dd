# Bilibili认证服务配置
# Data ID: bilibili-auth-service.yml
# Group: DEFAULT_GROUP

# 服务基础配置
server:
  port: 8090

# 应用配置
spring:
  application:
    name: auth-service

# 认证服务特定配置
auth:
  # 验证码配置
  captcha:
    enabled: true
    expire-time: 300 # 5分钟
    length: 4
    type: math # math, char, chinese

  # 登录配置
  login:
    max-attempts: 5
    lock-time: 1800 # 30分钟
    enable-remember-me: true
    remember-me-duration: 604800 # 7天

  # 设备管理
  device:
    max-devices: 5
    trust-duration: 2592000 # 30天
    enable-device-tracking: true

  # 密码策略
  password:
    min-length: 8
    max-length: 32
    require-uppercase: false
    require-lowercase: true
    require-digit: true
    require-special-char: false
    history-count: 5 # 记住最近5个密码

  # 会话管理
  session:
    max-sessions: 3
    prevent-login-if-maximum-exceeded: false
    expire-if-maximum-exceeded: true

# 第三方登录配置
oauth:
  # 微信登录配置
  wechat:
    appid: wxed9954c01bb89b47
    secret: a7482517235173ddb4083788de60b90e
    redirect-uri: http://localhost:8090/api/v1/auth/wechat/callback
    scope: snsapi_userinfo
    # API URLs
    api:
      access-token-url: https://api.weixin.qq.com/sns/oauth2/access_token
      user-info-url: https://api.weixin.qq.com/sns/userinfo
      refresh-token-url: https://api.weixin.qq.com/sns/oauth2/refresh_token

  # QQ登录配置（预留）
  qq:
    enabled: false
    app-id: ""
    app-key: ""

  # 支付宝登录配置（预留）
  alipay:
    enabled: false
    app-id: ""
    private-key: ""

# 业务规则配置
business:
  # 注册规则
  register:
    enabled: true
    require-email-verification: false
    require-phone-verification: true
    default-avatar: "https://static.bilibili.com/avatar/default.jpg"
    welcome-message: "欢迎加入Bilibili大家庭！"

  # 登录规则
  login:
    enable-captcha-after-failures: 3
    captcha-expire-minutes: 5
    enable-ip-whitelist: false
    ip-whitelist: []

# 缓存配置
cache:
  # 验证码缓存时间（秒）
  captcha-ttl: 300
  # 登录失败次数缓存时间（秒）
  login-failure-ttl: 1800
  # 用户会话缓存时间（秒）
  user-session-ttl: 86400



# 日志配置
logging:
  level:
    com.bilibili.auth: DEBUG
    org.springframework.security: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
  file:
    name: logs/bilibili-auth.log
