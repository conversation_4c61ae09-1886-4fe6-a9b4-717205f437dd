# 共享监控配置
# Data ID: shared-monitoring.yaml
# Group: DEFAULT_GROUP

# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway,routes,refresh,env
      base-path: /actuator
      cors:
        allowed-origins: "*"
        allowed-methods: "GET,POST"
  
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
      probes:
        enabled: true
    
    info:
      enabled: true
    
    metrics:
      enabled: true
    
    prometheus:
      enabled: true
    
    # 网关特定端点
    gateway:
      enabled: true
    
    routes:
      enabled: true
  
  # 健康检查配置
  health:
    # 数据库健康检查
    db:
      enabled: true

    # Redis健康检查
    redis:
      enabled: true

    # Elasticsearch健康检查
    elasticsearch:
      enabled: false  # 暂时禁用，避免连接错误

    # RabbitMQ健康检查
    rabbit:
      enabled: true

    # 磁盘空间检查
    diskspace:
      enabled: true
      threshold: 10MB

    # 自定义健康检查
    defaults:
      enabled: true
  
  # 指标配置
  metrics:
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active:default}
    
    export:
      prometheus:
        enabled: true
        descriptions: true
        step: 60s
    
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.9, 0.95, 0.99
      slo:
        http.server.requests: 10ms, 50ms, 100ms, 200ms, 500ms, 1s, 2s, 5s
  
  # 信息端点配置
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true
    git:
      mode: full
    build:
      enabled: true

# 日志配置 - 减少启动时冗余输出
logging:
  # 日志级别 - 优化启动输出
  level:
    root: WARN
    # 业务日志保持INFO
    com.bilibili: INFO
    # Spring框架日志减少
    org.springframework: WARN
    org.springframework.boot: WARN
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.springframework.cloud: WARN
    org.springframework.cloud.gateway: WARN
    org.springframework.data: WARN
    # 数据库相关日志减少
    org.hibernate: WARN
    org.hibernate.SQL: WARN
    com.zaxxer.hikari: WARN
    com.alibaba.druid: WARN
    org.apache.ibatis: WARN
    com.baomidou.mybatisplus: WARN
    # 中间件日志减少
    com.alibaba.nacos: ERROR
    com.alibaba.cloud.nacos: ERROR
    io.lettuce: WARN
    reactor.netty: WARN
    org.elasticsearch: WARN
    org.apache.http: ERROR
    # 其他框架日志
    io.swagger: WARN
    springfox: WARN
    # 屏蔽Spring Cloud LoadBalancer的BeanPostProcessor警告
    org.springframework.beans.factory.support.DefaultListableBeanFactory$BeanPostProcessorChecker: ERROR
    org.springframework.context.annotation.ConfigurationClassPostProcessor$ImportAwareBeanPostProcessor: ERROR
  
  # 日志格式 - 简化启动输出
  pattern:
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} : %m%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
  
  # 日志文件配置
  file:
    max-size: 100MB
    max-history: 30
    total-size-cap: 1GB
  
  # 日志归档
  logback:
    rollingpolicy:
      clean-history-on-start: true
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 1GB

# 链路追踪配置（如果使用）
spring:
  sleuth:
    enabled: false
    zipkin:
      enabled: false
      base-url: http://localhost:9411
    sampler:
      probability: 0.1
