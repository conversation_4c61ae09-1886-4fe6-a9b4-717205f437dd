# Redis配置
spring:
  data:
    redis:
      host: ***********
      port: 6379
      password: "@Xc123456"
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 1
          max-wait: 300ms

# Redis缓存配置
cache:
  redis:
    time-to-live: 600000  # 10分钟
    cache-null-values: true
    key-prefix: "bilibili:"

# 分布式锁配置
redisson:
  address: redis://***********:6379
  password: "@Xc123456"  # 添加密码
  database: 0
  connection-pool-size: 10
  connection-minimum-idle-size: 5
  timeout: 3000
  retry-attempts: 3
  retry-interval: 1500
