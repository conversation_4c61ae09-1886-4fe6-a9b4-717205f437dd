# Bilibili AI助手服务配置文件
# 包含AI模型配置、向量数据库配置、缓存配置等

# AI模型配置
spring:
  ai:
    # OpenAI配置 (暂时禁用，避免付费)
    openai:
      api-key: disabled
      base-url: https://api.openai.com
      chat:
        enabled: false  # 禁用OpenAI聊天模型
        options:
          model: gpt-4o-mini
          temperature: 0.7
          max-tokens: 2000
          top-p: 1.0
          frequency-penalty: 0.0
          presence-penalty: 0.0
      embedding:
        enabled: false  # 禁用OpenAI嵌入模型
        options:
          model: text-embedding-3-small

    # 阿里云通义千问配置 (主要模型)
    dashscope:
      api-key: sk-8dddcff3955a489bb2d5240bf198cbef
      base-url: https://dashscope.aliyuncs.com/api/v1
      chat:
        enabled: true   # 启用通义千问聊天模型
        options:
          model: qwen-turbo
          temperature: 0.7
          max-tokens: 2000
          top-p: 0.8
          stream: true  # 支持流式响应
      embedding:
        enabled: true   # 启用通义千问嵌入模型
        options:
          model: text-embedding-v2
          
    # 向量数据库配置
    vectorstore:
      chroma:
        client:
          host: ${CHROMA_HOST:localhost}
          port: ${CHROMA_PORT:8000}
        initialize-schema: true
        collection-name: bilibili-content

# AI助手服务特定配置
ai:
  # 模型选择策略
  model:
    primary: dashscope  # 主要使用通义千问模型
    fallback: dashscope  # 备用模型也是通义千问
    embedding: dashscope  # 向量化模型使用通义千问
    
  # RAG配置
  rag:
    # 文档分块配置
    chunk:
      size: 1000  # 分块大小
      overlap: 200  # 重叠大小
      
    # 检索配置
    retrieval:
      top-k: 5  # 检索Top-K文档
      similarity-threshold: 0.7  # 相似度阈值
      
    # 生成配置
    generation:
      max-context-length: 8000  # 最大上下文长度
      include-source: true  # 是否包含来源信息
      
  # 缓存配置
  cache:
    # 问答缓存
    qa:
      enabled: true
      ttl: 3600  # 1小时
      max-size: 1000
      
    # 向量缓存
    embedding:
      enabled: true
      ttl: 86400  # 24小时
      max-size: 5000
      
  # 内容分析配置
  content:
    # 视频分析
    video:
      max-description-length: 2000
      include-comments: true
      max-comments: 50
      
    # UP主分析
    uploader:
      include-recent-videos: true
      max-recent-videos: 10
      include-stats: true
      
  # 安全配置
  security:
    # 内容过滤
    content-filter:
      enabled: true
      sensitive-words-check: true
      
    # 频率限制
    rate-limit:
      enabled: true
      requests-per-minute: 60
      requests-per-hour: 1000
      
  # 监控配置
  monitoring:
    # 性能监控
    performance:
      enabled: true
      slow-query-threshold: 5000  # 5秒
      
    # 成本监控
    cost:
      enabled: true
      daily-limit: 100.0  # 每日成本限制(美元)
      alert-threshold: 80.0  # 告警阈值

# 应用监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,ai
  endpoint:
    health:
      show-details: when-authorized
    ai:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      service: ai-assistant

# 日志配置
logging:
  level:
    org.springframework.ai: INFO
    com.bilibili.aiassistant: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"
  file:
    name: logs/bilibili-aiassistant.log
    max-size: 10MB
    max-history: 30

# 异步配置
async:
  core-pool-size: 5
  max-pool-size: 20
  queue-capacity: 100
  thread-name-prefix: ai-async-

# Feign配置
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 30000  # AI调用可能较慢
        logger-level: basic
