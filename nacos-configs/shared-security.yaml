# 共享安全配置
# Data ID: shared-security.yaml
# Group: DEFAULT_GROUP

# JWT配置 - 统一管理
app:
  jwt:
    # JWT密钥 - 生产环境应使用环境变量
    secret: bilibiliAuthSecretKey2024SunsetTeamVeryLongSecretKeyForJWTTokenHS512
    # 访问令牌过期时间（毫秒）
    access-token-expiration: 86400000  # 24小时
    # 刷新令牌过期时间（毫秒）
    refresh-token-expiration: 604800000 # 7天
    # 发行者
    issuer: bilibili-auth
    # 受众
    audience: bilibili-users
    # 算法
    algorithm: HS512

# Spring Security配置
spring:
  security:
    # OAuth2配置（如果需要）
    oauth2:
      client:
        registration:
          wechat:
            client-id: wxed9954c01bb89b47
            client-secret: a7482517235173ddb4083788de60b90e
            scope: snsapi_userinfo
            authorization-grant-type: authorization_code
            redirect-uri: "{baseUrl}/api/v1/auth/wechat/callback"
        provider:
          wechat:
            authorization-uri: https://open.weixin.qq.com/connect/oauth2/authorize
            token-uri: https://api.weixin.qq.com/sns/oauth2/access_token
            user-info-uri: https://api.weixin.qq.com/sns/userinfo

# 认证配置
auth:
  # 验证码配置
  captcha:
    enabled: true
    expire-time: 300 # 5分钟
    length: 4
    type: math # math, char, chinese
  
  # 登录配置
  login:
    max-attempts: 5
    lock-time: 1800 # 30分钟
    enable-remember-me: true
    remember-me-duration: 604800 # 7天
  
  # 设备管理
  device:
    max-devices: 5
    trust-duration: 2592000 # 30天
    enable-device-tracking: true
  
  # 密码策略
  password:
    min-length: 8
    max-length: 32
    require-uppercase: true
    require-lowercase: true
    require-digit: true
    require-special-char: false
    history-count: 5 # 记住最近5个密码
  
  # 会话管理
  session:
    max-sessions: 3
    prevent-login-if-maximum-exceeded: false
    expire-if-maximum-exceeded: true

# CORS配置 - 由网关统一处理，服务端禁用
cors:
  enabled: false
  allowed-origins: "*"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600
