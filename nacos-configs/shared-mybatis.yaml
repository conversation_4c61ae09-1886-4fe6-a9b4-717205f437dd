# 共享MyBatis配置
# Data ID: shared-mybatis.yaml
# Group: DEFAULT_GROUP

# MyBatis-Plus配置
mybatis-plus:
  # Mapper XML文件位置
  mapper-locations: classpath*:mapper/**/*.xml
  # 实体类包路径
  type-aliases-package: com.bilibili.**.entity,com.bilibili.**.po

  # 全局配置
  global-config:
    # 关闭横幅
    banner: false
    # 数据库配置
    db-config:
      # 主键类型 - 雪花算法
      id-type: ASSIGN_ID
      # 字段策略 - 非空判断
      field-strategy: NOT_EMPTY
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
      # 数据库类型
      db-type: mysql

  # MyBatis配置
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启二级缓存
    cache-enabled: true
    # 延迟加载
    lazy-loading-enabled: true
    # 关闭积极延迟加载
    aggressive-lazy-loading: false
    # 允许多结果集
    multiple-result-sets-enabled: true
    # 使用列标签
    use-column-label: true
    # 使用生成的键
    use-generated-keys: true
    # 默认执行器类型
    default-executor-type: REUSE
    # 默认语句超时时间（秒）
    default-statement-timeout: 30
    # 默认获取数据大小
    default-fetch-size: 100
    # 本地缓存范围
    local-cache-scope: SESSION
    # JDBC类型为空时的处理
    jdbc-type-for-null: OTHER
    # 延迟加载触发方法
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    # 当结果集中含有Null值时是否执行映射对象的setter
    call-setters-on-nulls: false
    # 是否返回空行的实例
    return-instance-for-empty-row: false
    # 自动映射行为
    auto-mapping-behavior: PARTIAL
    # 自动映射未知列行为
    auto-mapping-unknown-column-behavior: NONE
    # 默认枚举处理器
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
    # 日志实现
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
