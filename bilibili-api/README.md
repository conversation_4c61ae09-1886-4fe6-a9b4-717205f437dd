# Bilibili API 模块

## 📁 项目结构

本模块采用按功能模块组织的包结构，每个功能模块包含独立的 client、dto、vo、api 子包：

```
com.bilibili.api/
├── user/                           # 用户模块
│   ├── client/
│   │   └── UserServiceClient.java  # 用户服务Feign客户端
│   ├── dto/
│   │   ├── UserInfoDTO.java        # 用户信息传输对象
│   │   ├── UserUpdateDTO.java      # 用户更新请求
│   │   ├── UserStatsDTO.java       # 用户统计数据
│   │   └── UserSearchDTO.java      # 用户搜索条件
│   ├── vo/
│   │   ├── UserProfileVO.java      # 用户资料视图对象
│   │   └── UserStatsVO.java        # 用户统计视图对象
│   └── api/
│       └── UserApi.java            # 用户API接口定义
├── auth/                           # 认证模块
│   ├── client/
│   │   └── AuthServiceClient.java  # 认证服务Feign客户端
│   ├── dto/
│   │   ├── LoginRequestDTO.java    # 登录请求
│   │   ├── LoginResponseDTO.java   # 登录响应
│   │   ├── RegisterRequestDTO.java # 注册请求
│   │   ├── TokenValidationDTO.java # 令牌验证
│   │   └── SendCodeRequestDTO.java # 发送验证码请求
│   ├── vo/
│   │   └── LoginResultVO.java      # 登录结果视图
│   └── api/
│       └── AuthApi.java            # 认证API接口定义
├── ai/                             # AI模块
│   ├── client/
│   │   └── AiServiceClient.java    # AI服务Feign客户端
│   ├── dto/
│   │   ├── AiChatRequestDTO.java   # AI聊天请求
│   │   ├── AiChatResponseDTO.java  # AI聊天响应
│   │   ├── ContentAnalysisDTO.java # 内容分析
│   │   └── RecommendationDTO.java  # 推荐数据
│   ├── vo/
│   │   └── ChatSessionVO.java      # 聊天会话视图
│   └── api/
│       └── AiApi.java              # AI API接口定义
└── common/                         # 通用模块
    ├── dto/
    │   ├── PageRequestDTO.java     # 分页请求
    │   └── PageResponseDTO.java    # 分页响应
    └── vo/
        └── BaseVO.java             # 基础视图对象
```

## 🎯 设计原则

### 1. 模块化设计
- **按功能分包**：每个业务功能独立成包，便于维护和扩展
- **职责清晰**：client、dto、vo、api 各司其职
- **低耦合**：模块间通过接口交互，减少直接依赖

### 2. 包结构说明

#### client/ - Feign客户端
- 定义服务间调用接口
- 使用 `@FeignClient` 注解
- 返回统一的 `ApiResponse<T>` 格式

#### dto/ - 数据传输对象
- 用于服务间数据传输
- 包含完整的验证注解
- 支持序列化/反序列化

#### vo/ - 视图对象
- 面向前端的数据展示
- 可能包含计算字段
- 优化的数据结构

#### api/ - API接口定义
- 定义REST API规范
- 包含完整的Swagger文档
- 统一的参数和返回格式

### 3. 命名规范

#### 类命名
- **DTO**: `XxxDTO.java` - 数据传输对象
- **VO**: `XxxVO.java` - 视图对象  
- **Client**: `XxxServiceClient.java` - Feign客户端
- **Api**: `XxxApi.java` - API接口定义

#### 包命名
- 使用小写字母
- 按业务功能命名：user、auth、ai、video、comment等
- 子包固定：client、dto、vo、api

## 🔧 使用示例

### 1. Feign客户端调用
```java
@Autowired
private UserServiceClient userServiceClient;

public UserInfoDTO getUserInfo(Long userId) {
    ApiResponse<UserInfoDTO> response = userServiceClient.getUserInfo(userId);
    return response.getData();
}
```

### 2. API接口实现
```java
@RestController
@RequestMapping("/api/v1/user")
public class UserController implements UserApi {
    
    @Override
    public ApiResponse<UserInfoDTO> getUserInfo(Long userId) {
        // 实现逻辑
        return ApiResponse.success(userInfo);
    }
}
```

### 3. 分页查询
```java
PageRequestDTO pageRequest = new PageRequestDTO();
pageRequest.setPage(1);
pageRequest.setSize(20);

ApiResponse<PageResponseDTO<UserInfoDTO>> response = 
    userServiceClient.searchUsers(searchDTO);
```

## 📝 重构完成情况

### ✅ 已完成模块

#### 1. User 用户模块
- **Client**: `UserServiceClient` - 完整的用户服务调用接口
- **DTO**:
  - `UserInfoDTO` - 用户基本信息
  - `UserStatsDTO` - 用户统计数据
  - `UserUpdateDTO` - 用户更新请求
  - `UserSearchDTO` - 用户搜索条件
- **VO**:
  - `UserProfileVO` - 用户资料展示（含隐私设置）
  - `UserStatsVO` - 用户统计展示
- **API**: `UserApi` - 完整的用户API接口定义

#### 2. Auth 认证模块
- **Client**: `AuthServiceClient` - 完整的认证服务调用接口
- **DTO**:
  - `LoginRequestDTO` - 登录请求（支持多种登录方式）
  - `LoginResponseDTO` - 登录响应（含VIP信息）
  - `RegisterRequestDTO` - 注册请求（含验证）
  - `TokenValidationDTO` - 令牌验证结果
  - `SendCodeRequestDTO` - 验证码发送请求
- **VO**:
  - `LoginResultVO` - 登录结果展示（含安全提醒）

#### 3. AI 模块
- **Client**: `AiServiceClient` - 完整的AI服务调用接口
- **DTO**:
  - `AiChatRequestDTO` - AI聊天请求（支持多模态）
  - `AiChatResponseDTO` - AI聊天响应（含流式支持）
  - `ContentAnalysisDTO` - 内容分析结果（多维度分析）
  - `RecommendationDTO` - 推荐结果（个性化推荐）
- **VO**:
  - `ChatSessionVO` - 聊天会话展示（含统计信息）

#### 4. Common 通用模块
- **DTO**:
  - `PageRequestDTO` - 标准分页请求
  - `PageResponseDTO` - 标准分页响应

## 🚀 架构优势

1. **清晰的结构**：按功能模块组织，易于理解和维护
2. **高内聚低耦合**：每个模块职责单一，模块间松耦合
3. **易于扩展**：新增功能只需添加对应模块
4. **统一规范**：所有模块遵循相同的结构和命名规范
5. **便于测试**：模块化设计便于单元测试和集成测试
6. **类型安全**：完整的类型定义，减少运行时错误
7. **文档完善**：Swagger注解提供完整的API文档

## 📋 后续扩展建议

### 待添加模块
- **video** - 视频模块（视频上传、播放、管理）
- **comment** - 评论模块（评论管理、回复）
- **live** - 直播模块（直播管理、弹幕）
- **message** - 消息模块（私信、通知）
- **search** - 搜索模块（全文搜索、推荐）

### 优化建议
- 添加更多的验证注解和业务规则
- 完善错误码和异常处理
- 添加缓存策略注解
- 支持API版本控制
- 添加性能监控注解
