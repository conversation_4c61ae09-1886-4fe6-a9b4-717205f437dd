package com.bilibili.api.user.api;

import com.bilibili.api.user.dto.UserInfoDTO;
import com.bilibili.api.user.dto.UserStatsDTO;
import com.bilibili.api.user.dto.UserUpdateDTO;
import com.bilibili.api.user.dto.UserSearchDTO;
import com.bilibili.api.user.vo.UserProfileVO;
import com.bilibili.api.user.vo.UserStatsVO;
import com.bilibili.api.common.dto.PageResponseDTO;
import com.bilibili.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 用户API接口定义
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Tag(name = "用户管理", description = "用户相关API接口")
public interface UserApi {

    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @GetMapping("/info/{userId}")
    ApiResponse<UserInfoDTO> getUserInfo(
            @Parameter(description = "用户ID", required = true) @PathVariable("userId") Long userId);

    @Operation(summary = "获取用户资料", description = "获取用户完整资料信息")
    @GetMapping("/profile/{userId}")
    ApiResponse<UserProfileVO> getUserProfile(
            @Parameter(description = "用户ID", required = true) @PathVariable("userId") Long userId);

    @Operation(summary = "获取用户统计", description = "获取用户统计数据")
    @GetMapping("/stats/{userId}")
    ApiResponse<UserStatsVO> getUserStats(
            @Parameter(description = "用户ID", required = true) @PathVariable("userId") Long userId);

    @Operation(summary = "更新用户信息", description = "更新用户基本信息")
    @PutMapping("/info/{userId}")
    ApiResponse<Void> updateUserInfo(
            @Parameter(description = "用户ID", required = true) @PathVariable("userId") Long userId,
            @Parameter(description = "更新信息", required = true) @RequestBody @Valid UserUpdateDTO updateDTO);

    @Operation(summary = "搜索用户", description = "根据条件搜索用户")
    @PostMapping("/search")
    ApiResponse<PageResponseDTO<UserInfoDTO>> searchUsers(
            @Parameter(description = "搜索条件", required = true) @RequestBody @Valid UserSearchDTO searchDTO);

    @Operation(summary = "批量获取用户信息", description = "批量获取多个用户的基本信息")
    @PostMapping("/batch")
    ApiResponse<List<UserInfoDTO>> batchGetUserInfo(
            @Parameter(description = "用户ID列表", required = true) @RequestBody List<Long> userIds);

    @Operation(summary = "关注用户", description = "关注指定用户")
    @PostMapping("/{userId}/follow/{targetUserId}")
    ApiResponse<Void> followUser(
            @Parameter(description = "当前用户ID", required = true) @PathVariable("userId") Long userId,
            @Parameter(description = "目标用户ID", required = true) @PathVariable("targetUserId") Long targetUserId);

    @Operation(summary = "取消关注", description = "取消关注指定用户")
    @DeleteMapping("/{userId}/follow/{targetUserId}")
    ApiResponse<Void> unfollowUser(
            @Parameter(description = "当前用户ID", required = true) @PathVariable("userId") Long userId,
            @Parameter(description = "目标用户ID", required = true) @PathVariable("targetUserId") Long targetUserId);

    @Operation(summary = "获取关注列表", description = "获取用户关注的用户列表")
    @GetMapping("/{userId}/following")
    ApiResponse<PageResponseDTO<UserInfoDTO>> getFollowing(
            @Parameter(description = "用户ID", required = true) @PathVariable("userId") Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size);

    @Operation(summary = "获取粉丝列表", description = "获取用户的粉丝列表")
    @GetMapping("/{userId}/followers")
    ApiResponse<PageResponseDTO<UserInfoDTO>> getFollowers(
            @Parameter(description = "用户ID", required = true) @PathVariable("userId") Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size);

    @Operation(summary = "检查关注关系", description = "检查是否已关注指定用户")
    @GetMapping("/{userId}/following/{targetUserId}")
    ApiResponse<Boolean> isFollowing(
            @Parameter(description = "当前用户ID", required = true) @PathVariable("userId") Long userId,
            @Parameter(description = "目标用户ID", required = true) @PathVariable("targetUserId") Long targetUserId);

    @Operation(summary = "检查用户存在", description = "检查用户是否存在")
    @GetMapping("/exists/{userId}")
    ApiResponse<Boolean> userExists(
            @Parameter(description = "用户ID", required = true) @PathVariable("userId") Long userId);

    @Operation(summary = "根据用户名获取信息", description = "根据用户名获取用户信息")
    @GetMapping("/info/by-username")
    ApiResponse<UserInfoDTO> getUserInfoByUsername(
            @Parameter(description = "用户名", required = true) @RequestParam("username") String username);

    @Operation(summary = "添加黑名单", description = "将用户添加到黑名单")
    @PostMapping("/{userId}/blacklist/{targetUserId}")
    ApiResponse<Void> addToBlacklist(
            @Parameter(description = "当前用户ID", required = true) @PathVariable("userId") Long userId,
            @Parameter(description = "目标用户ID", required = true) @PathVariable("targetUserId") Long targetUserId);

    @Operation(summary = "移除黑名单", description = "将用户从黑名单中移除")
    @DeleteMapping("/{userId}/blacklist/{targetUserId}")
    ApiResponse<Void> removeFromBlacklist(
            @Parameter(description = "当前用户ID", required = true) @PathVariable("userId") Long userId,
            @Parameter(description = "目标用户ID", required = true) @PathVariable("targetUserId") Long targetUserId);

    @Operation(summary = "获取黑名单", description = "获取用户的黑名单列表")
    @GetMapping("/{userId}/blacklist")
    ApiResponse<PageResponseDTO<UserInfoDTO>> getBlacklist(
            @Parameter(description = "用户ID", required = true) @PathVariable("userId") Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size);
}
