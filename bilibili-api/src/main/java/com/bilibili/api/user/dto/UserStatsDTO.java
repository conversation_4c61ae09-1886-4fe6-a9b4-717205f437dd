package com.bilibili.api.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户统计信息DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Accessors(chain = true)
@Schema(description = "用户统计信息数据传输对象")
public class UserStatsDTO {

    @Schema(description = "用户ID", example = "123456")
    private Long uid;

    @Schema(description = "关注数", example = "100")
    private Integer followingCount;

    @Schema(description = "粉丝数", example = "1000")
    private Integer followerCount;

    @Schema(description = "视频数", example = "50")
    private Integer videoCount;

    @Schema(description = "专栏数", example = "20")
    private Integer articleCount;

    @Schema(description = "动态数", example = "200")
    private Integer dynamicCount;

    @Schema(description = "总播放量", example = "100000")
    private Long totalPlayCount;

    @Schema(description = "总点赞数", example = "5000")
    private Long totalLikeCount;

    @Schema(description = "总投币数", example = "2000")
    private Long totalCoinCount;

    @Schema(description = "总收藏数", example = "3000")
    private Long totalFavoriteCount;

    @Schema(description = "总分享数", example = "1000")
    private Long totalShareCount;

    @Schema(description = "总评论数", example = "8000")
    private Long totalCommentCount;

    @Schema(description = "总弹幕数", example = "15000")
    private Long totalDanmakuCount;

    @Schema(description = "黑名单数", example = "10")
    private Integer blacklistCount;

    @Schema(description = "收藏夹数", example = "5")
    private Integer favoriteFolderCount;

    @Schema(description = "订阅数", example = "30")
    private Integer subscriptionCount;

    @Schema(description = "历史记录数", example = "500")
    private Integer historyCount;

    @Schema(description = "观看时长(分钟)", example = "12000")
    private Long watchDuration;

    @Schema(description = "创建时间", example = "2023-01-01T12:00:00")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间", example = "2024-01-01T12:00:00")
    private LocalDateTime updatedAt;
}
