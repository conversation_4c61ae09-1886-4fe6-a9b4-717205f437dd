package com.bilibili.api.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 用户搜索DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "用户搜索请求")
public class UserSearchDTO {

    @Schema(description = "搜索关键词", example = "小明")
    private String keyword;

    @Schema(description = "用户等级", example = "5")
    private Integer level;

    @Schema(description = "VIP类型", example = "2", allowableValues = {"0", "1", "2"})
    private Integer vipType;

    @Schema(description = "认证类型", example = "1", allowableValues = {"0", "1", "2", "3"})
    private Integer verifiedType;

    @Schema(description = "性别", example = "1", allowableValues = {"0", "1", "2"})
    private Integer gender;

    @Schema(description = "位置", example = "北京")
    private String location;

    @Schema(description = "最小粉丝数", example = "1000")
    @Min(value = 0, message = "最小粉丝数不能小于0")
    private Integer minFollowers;

    @Schema(description = "最大粉丝数", example = "100000")
    @Min(value = 0, message = "最大粉丝数不能小于0")
    private Integer maxFollowers;

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "20")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 20;

    @Schema(description = "排序字段", example = "followerCount", 
            allowableValues = {"followerCount", "level", "registerTime", "lastLoginTime"})
    private String sortBy = "followerCount";

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortDir = "desc";
}
