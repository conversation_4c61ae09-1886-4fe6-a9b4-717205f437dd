package com.bilibili.api.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户统计VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户统计信息")
public class UserStatsVO {

    @Schema(description = "用户ID", example = "123456")
    private Long uid;

    @Schema(description = "关注数", example = "100")
    private Integer followingCount;

    @Schema(description = "粉丝数", example = "1000")
    private Integer followerCount;

    @Schema(description = "视频数", example = "50")
    private Integer videoCount;

    @Schema(description = "播放数", example = "100000")
    private Long viewCount;

    @Schema(description = "获赞数", example = "10000")
    private Long likeCount;

    @Schema(description = "投币数", example = "5000")
    private Long coinCount;

    @Schema(description = "收藏数", example = "3000")
    private Long favoriteCount;

    @Schema(description = "分享数", example = "2000")
    private Long shareCount;

    @Schema(description = "评论数", example = "8000")
    private Long commentCount;

    @Schema(description = "弹幕数", example = "15000")
    private Long danmuCount;

    @Schema(description = "直播时长(秒)", example = "36000")
    private Long liveTime;
}
