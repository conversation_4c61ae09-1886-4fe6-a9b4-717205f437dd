package com.bilibili.api.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDate;

/**
 * 用户更新DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "用户更新请求")
public class UserUpdateDTO {

    @Schema(description = "昵称", example = "小明")
    @Size(min = 2, max = 20, message = "昵称长度必须在2-20个字符之间")
    private String nickname;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "横幅URL", example = "https://example.com/banner.jpg")
    private String banner;

    @Schema(description = "个人签名", example = "这个人很懒，什么都没有留下")
    @Size(max = 255, message = "个人签名不能超过255个字符")
    private String signature;

    @Schema(description = "性别", example = "1", allowableValues = {"0", "1", "2"})
    private Integer gender; // 0未知 1男 2女

    @Schema(description = "生日", example = "1990-01-01")
    private LocalDate birthday;

    @Schema(description = "位置", example = "北京市")
    @Size(max = 100, message = "位置不能超过100个字符")
    private String location;

    @Schema(description = "学校", example = "清华大学")
    @Size(max = 100, message = "学校名称不能超过100个字符")
    private String school;

    @Schema(description = "个人标签", example = "技术,音乐,旅行")
    private String personalTags;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
}
