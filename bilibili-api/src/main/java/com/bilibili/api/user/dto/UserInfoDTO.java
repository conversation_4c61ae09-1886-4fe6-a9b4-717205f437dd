package com.bilibili.api.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Accessors(chain = true)
@Schema(description = "用户信息数据传输对象")
public class UserInfoDTO {

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户UID", example = "123456")
    private Long uid;

    @Schema(description = "用户名", example = "user123")
    private String username;

    @Schema(description = "昵称", example = "小明")
    private String nickname;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "横幅URL", example = "https://example.com/banner.jpg")
    private String banner;

    @Schema(description = "性别", example = "1", allowableValues = {"0", "1", "2"})
    private Integer gender; // 0未知 1男 2女

    @Schema(description = "生日", example = "1990-01-01")
    private LocalDate birthday;

    @Schema(description = "个人签名", example = "这个人很懒，什么都没有留下")
    private String signature;

    @Schema(description = "位置", example = "北京市")
    private String location;

    @Schema(description = "用户等级", example = "5")
    private Integer level;

    @Schema(description = "经验值", example = "12500")
    private Integer exp;

    @Schema(description = "硬币数量", example = "1000.0")
    private Double coins;

    @Schema(description = "VIP类型", example = "2", allowableValues = {"0", "1", "2"})
    private Integer vipType; // 0普通 1月会员 2年会员

    @Schema(description = "VIP状态", example = "1", allowableValues = {"0", "1"})
    private Integer vipStatus; // 0无效 1有效

    @Schema(description = "VIP到期时间", example = "2024-12-31T23:59:59")
    private LocalDateTime vipDueDate;

    @Schema(description = "是否已认证", example = "true")
    private Boolean isVerified;

    @Schema(description = "认证类型", example = "1", allowableValues = {"0", "1", "2", "3"})
    private Integer verifiedType; // 0无认证 1个人认证 2企业认证 3机构认证

    @Schema(description = "认证信息", example = "知名UP主")
    private String verifiedInfo;

    @Schema(description = "学校", example = "清华大学")
    private String school;

    @Schema(description = "个人标签", example = "技术,音乐,旅行")
    private String personalTags;

    @Schema(description = "用户状态", example = "1", allowableValues = {"0", "1", "2"})
    private Integer status; // 0禁用 1正常 2锁定

    @Schema(description = "是否绑定手机", example = "true")
    private Boolean phoneBound;

    @Schema(description = "是否绑定邮箱", example = "true")
    private Boolean emailBound;

    @Schema(description = "是否实名认证", example = "true")
    private Boolean realNameVerified;

    @Schema(description = "注册时间", example = "2023-01-01T12:00:00")
    private LocalDateTime registerTime;

    @Schema(description = "最后登录时间", example = "2024-01-01T12:00:00")
    private LocalDateTime lastLoginTime;

    @Schema(description = "创建时间", example = "2023-01-01T12:00:00")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间", example = "2024-01-01T12:00:00")
    private LocalDateTime updatedAt;
}
