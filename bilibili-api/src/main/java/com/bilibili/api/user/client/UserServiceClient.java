package com.bilibili.api.user.client;

import com.bilibili.api.user.dto.UserInfoDTO;
import com.bilibili.api.user.dto.UserStatsDTO;
import com.bilibili.api.user.dto.UserUpdateDTO;
import com.bilibili.api.user.dto.UserSearchDTO;
import com.bilibili.common.response.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@FeignClient(name = "bilibili-user", path = "/api/v1/user")
public interface UserServiceClient {

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/info/{userId}")
    ApiResponse<UserInfoDTO> getUserInfo(@PathVariable("userId") Long userId);

    /**
     * 根据用户名获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    @GetMapping("/info/by-username")
    ApiResponse<UserInfoDTO> getUserInfoByUsername(@RequestParam("username") String username);

    /**
     * 获取用户统计信息
     *
     * @param userId 用户ID
     * @return 用户统计信息
     */
    @GetMapping("/stats/{userId}")
    ApiResponse<UserStatsDTO> getUserStats(@PathVariable("userId") Long userId);

    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param updateDTO 更新信息
     * @return 更新结果
     */
    @PutMapping("/info/{userId}")
    ApiResponse<Void> updateUserInfo(@PathVariable("userId") Long userId, 
                                   @RequestBody UserUpdateDTO updateDTO);

    /**
     * 检查用户是否存在
     *
     * @param userId 用户ID
     * @return 是否存在
     */
    @GetMapping("/exists/{userId}")
    ApiResponse<Boolean> userExists(@PathVariable("userId") Long userId);

    /**
     * 批量获取用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户信息列表
     */
    @PostMapping("/batch")
    ApiResponse<List<UserInfoDTO>> batchGetUserInfo(@RequestBody List<Long> userIds);

    /**
     * 搜索用户
     *
     * @param searchDTO 搜索条件
     * @return 搜索结果
     */
    @PostMapping("/search")
    ApiResponse<List<UserInfoDTO>> searchUsers(@RequestBody UserSearchDTO searchDTO);

    /**
     * 关注用户
     *
     * @param userId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 操作结果
     */
    @PostMapping("/{userId}/follow/{targetUserId}")
    ApiResponse<Void> followUser(@PathVariable("userId") Long userId, 
                               @PathVariable("targetUserId") Long targetUserId);

    /**
     * 取消关注用户
     *
     * @param userId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 操作结果
     */
    @DeleteMapping("/{userId}/follow/{targetUserId}")
    ApiResponse<Void> unfollowUser(@PathVariable("userId") Long userId, 
                                 @PathVariable("targetUserId") Long targetUserId);

    /**
     * 获取用户关注列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 关注列表
     */
    @GetMapping("/{userId}/following")
    ApiResponse<List<UserInfoDTO>> getFollowing(@PathVariable("userId") Long userId,
                                              @RequestParam(defaultValue = "1") Integer page,
                                              @RequestParam(defaultValue = "20") Integer size);

    /**
     * 获取用户粉丝列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 粉丝列表
     */
    @GetMapping("/{userId}/followers")
    ApiResponse<List<UserInfoDTO>> getFollowers(@PathVariable("userId") Long userId,
                                              @RequestParam(defaultValue = "1") Integer page,
                                              @RequestParam(defaultValue = "20") Integer size);

    /**
     * 检查关注关系
     *
     * @param userId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 是否已关注
     */
    @GetMapping("/{userId}/following/{targetUserId}")
    ApiResponse<Boolean> isFollowing(@PathVariable("userId") Long userId,
                                   @PathVariable("targetUserId") Long targetUserId);
}
