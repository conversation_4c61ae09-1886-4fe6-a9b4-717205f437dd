package com.bilibili.api.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分页响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分页响应结果")
public class PageResponseDTO<T> {

    @Schema(description = "数据列表")
    private List<T> records;

    @Schema(description = "总记录数", example = "100")
    private Long total;

    @Schema(description = "当前页码", example = "1")
    private Integer page;

    @Schema(description = "每页大小", example = "20")
    private Integer size;

    @Schema(description = "总页数", example = "5")
    private Integer pages;

    @Schema(description = "是否有下一页", example = "true")
    private Boolean hasNext;

    @Schema(description = "是否有上一页", example = "false")
    private Boolean hasPrevious;

    @Schema(description = "是否为第一页", example = "true")
    private Boolean isFirst;

    @Schema(description = "是否为最后一页", example = "false")
    private Boolean isLast;

    /**
     * 创建分页响应
     */
    public static <T> PageResponseDTO<T> of(List<T> records, Long total, Integer page, Integer size) {
        Integer pages = (int) Math.ceil((double) total / size);
        
        return PageResponseDTO.<T>builder()
                .records(records)
                .total(total)
                .page(page)
                .size(size)
                .pages(pages)
                .hasNext(page < pages)
                .hasPrevious(page > 1)
                .isFirst(page == 1)
                .isLast(page.equals(pages))
                .build();
    }

    /**
     * 创建空的分页响应
     */
    public static <T> PageResponseDTO<T> empty(Integer page, Integer size) {
        return PageResponseDTO.<T>builder()
                .records(List.of())
                .total(0L)
                .page(page)
                .size(size)
                .pages(0)
                .hasNext(false)
                .hasPrevious(false)
                .isFirst(true)
                .isLast(true)
                .build();
    }
}
