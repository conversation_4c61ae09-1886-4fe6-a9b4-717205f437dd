package com.bilibili.api.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 分页请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "分页请求参数")
public class PageRequestDTO {

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "20")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 20;

    @Schema(description = "排序字段", example = "createdAt")
    private String sortBy;

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortDir = "desc";

    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (page - 1) * size;
    }

    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return size;
    }
}
