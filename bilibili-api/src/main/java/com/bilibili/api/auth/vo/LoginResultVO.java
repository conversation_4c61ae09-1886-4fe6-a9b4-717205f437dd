package com.bilibili.api.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 登录结果VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "登录结果信息")
public class LoginResultVO {

    @Schema(description = "登录状态", example = "success", allowableValues = {"success", "failed", "locked", "need_verify"})
    private String status;

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String accessToken;

    @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String refreshToken;

    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    @Schema(description = "访问令牌过期时间(秒)", example = "3600")
    private Long expiresIn;

    @Schema(description = "用户基本信息")
    private UserBasicInfo userInfo;

    @Schema(description = "登录设备信息")
    private DeviceInfo deviceInfo;

    @Schema(description = "安全提醒")
    private List<SecurityAlert> securityAlerts;

    @Schema(description = "是否首次登录", example = "false")
    private Boolean isFirstLogin;

    @Schema(description = "是否需要修改密码", example = "false")
    private Boolean needChangePassword;

    @Schema(description = "是否需要绑定手机", example = "false")
    private Boolean needBindPhone;

    @Schema(description = "是否需要实名认证", example = "false")
    private Boolean needRealNameAuth;

    @Schema(description = "登录时间", example = "2024-01-01T12:00:00")
    private LocalDateTime loginTime;

    @Schema(description = "上次登录时间", example = "2024-01-01T10:00:00")
    private LocalDateTime lastLoginTime;

    @Schema(description = "登录IP地址", example = "***********")
    private String loginIp;

    @Schema(description = "登录地点", example = "北京市")
    private String loginLocation;

    @Schema(description = "错误信息", example = "用户名或密码错误")
    private String errorMessage;

    @Schema(description = "错误代码", example = "INVALID_CREDENTIALS")
    private String errorCode;

    @Schema(description = "重试次数", example = "2")
    private Integer retryCount;

    @Schema(description = "账户锁定剩余时间(秒)", example = "300")
    private Long lockRemainingTime;

    /**
     * 用户基本信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户基本信息")
    public static class UserBasicInfo {

        @Schema(description = "用户ID", example = "123456")
        private Long userId;

        @Schema(description = "用户名", example = "user123")
        private String username;

        @Schema(description = "昵称", example = "小明")
        private String nickname;

        @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
        private String avatar;

        @Schema(description = "用户等级", example = "5")
        private Integer level;

        @Schema(description = "VIP类型", example = "2", allowableValues = {"0", "1", "2"})
        private Integer vipType;

        @Schema(description = "VIP状态", example = "1", allowableValues = {"0", "1"})
        private Integer vipStatus;

        @Schema(description = "VIP到期时间", example = "2024-12-31T23:59:59")
        private LocalDateTime vipDueDate;

        @Schema(description = "用户角色", example = "[\"USER\", \"VIP\"]")
        private List<String> roles;

        @Schema(description = "用户权限", example = "[\"user:read\", \"user:write\"]")
        private List<String> permissions;
    }

    /**
     * 设备信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "登录设备信息")
    public static class DeviceInfo {

        @Schema(description = "设备ID", example = "device_123456")
        private String deviceId;

        @Schema(description = "设备名称", example = "iPhone 13")
        private String deviceName;

        @Schema(description = "设备类型", example = "mobile", allowableValues = {"mobile", "desktop", "tablet"})
        private String deviceType;

        @Schema(description = "操作系统", example = "iOS 15.0")
        private String os;

        @Schema(description = "浏览器", example = "Safari 15.0")
        private String browser;

        @Schema(description = "是否新设备", example = "false")
        private Boolean isNewDevice;

        @Schema(description = "是否可信设备", example = "true")
        private Boolean isTrustedDevice;

        @Schema(description = "设备指纹", example = "fp_123456")
        private String fingerprint;
    }

    /**
     * 安全提醒
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "安全提醒")
    public static class SecurityAlert {

        @Schema(description = "提醒类型", example = "new_device", 
                allowableValues = {"new_device", "unusual_location", "password_weak", "account_risk"})
        private String type;

        @Schema(description = "提醒标题", example = "检测到新设备登录")
        private String title;

        @Schema(description = "提醒内容", example = "您的账户在新设备上登录，请确认是否为本人操作")
        private String message;

        @Schema(description = "严重程度", example = "medium", allowableValues = {"low", "medium", "high"})
        private String severity;

        @Schema(description = "建议操作", example = "建议修改密码")
        private String suggestion;

        @Schema(description = "是否需要立即处理", example = "false")
        private Boolean requiresAction;
    }

    /**
     * 创建成功的登录结果
     */
    public static LoginResultVO success(String accessToken, String refreshToken, UserBasicInfo userInfo) {
        return LoginResultVO.builder()
                .status("success")
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .userInfo(userInfo)
                .loginTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败的登录结果
     */
    public static LoginResultVO failed(String errorMessage, String errorCode) {
        return LoginResultVO.builder()
                .status("failed")
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .build();
    }
}
