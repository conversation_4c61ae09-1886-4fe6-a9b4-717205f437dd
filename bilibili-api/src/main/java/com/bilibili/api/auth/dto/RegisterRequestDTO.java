package com.bilibili.api.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 用户注册请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "用户注册请求")
public class RegisterRequestDTO {

    @Schema(description = "用户名", example = "user123")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    @Schema(description = "密码", example = "password123")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    @Schema(description = "确认密码", example = "password123")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "验证码", example = "123456")
    @NotBlank(message = "验证码不能为空")
    @Size(min = 4, max = 6, message = "验证码长度必须在4-6位之间")
    private String code;

    @Schema(description = "验证码类型", example = "sms", allowableValues = {"sms", "email"})
    @NotBlank(message = "验证码类型不能为空")
    private String codeType;

    @Schema(description = "昵称", example = "小明")
    @Size(max = 20, message = "昵称不能超过20个字符")
    private String nickname;

    @Schema(description = "邀请码", example = "INVITE123")
    private String inviteCode;

    @Schema(description = "注册来源", example = "web", allowableValues = {"web", "mobile", "wechat"})
    private String source = "web";

    @Schema(description = "设备信息", example = "iPhone 13")
    private String deviceInfo;

    @Schema(description = "IP地址", example = "***********")
    private String ipAddress;

    @Schema(description = "用户代理", example = "Mozilla/5.0...")
    private String userAgent;

    @Schema(description = "是否同意用户协议", example = "true")
    private Boolean agreeTerms = false;

    @Schema(description = "是否同意隐私政策", example = "true")
    private Boolean agreePrivacy = false;
}
