package com.bilibili.api.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 登录响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "登录响应")
public class LoginResponseDTO {

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String accessToken;

    @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String refreshToken;

    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    @Schema(description = "访问令牌过期时间(秒)", example = "3600")
    private Long expiresIn;

    @Schema(description = "刷新令牌过期时间(秒)", example = "604800")
    private Long refreshExpiresIn;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户名", example = "user123")
    private String username;

    @Schema(description = "昵称", example = "小明")
    private String nickname;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "用户等级", example = "5")
    private Integer level;

    @Schema(description = "VIP类型", example = "2", allowableValues = {"0", "1", "2"})
    private Integer vipType;

    @Schema(description = "VIP状态", example = "1", allowableValues = {"0", "1"})
    private Integer vipStatus;

    @Schema(description = "VIP到期时间", example = "2024-12-31T23:59:59")
    private LocalDateTime vipDueDate;

    @Schema(description = "是否首次登录", example = "false")
    private Boolean isFirstLogin;

    @Schema(description = "登录时间", example = "2024-01-01T12:00:00")
    private LocalDateTime loginTime;

    @Schema(description = "权限列表", example = "[\"user:read\", \"user:write\"]")
    private java.util.List<String> permissions;

    @Schema(description = "角色列表", example = "[\"USER\", \"VIP\"]")
    private java.util.List<String> roles;
}
