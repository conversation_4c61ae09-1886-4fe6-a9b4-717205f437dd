package com.bilibili.api.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 令牌验证结果DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "令牌验证结果")
public class TokenValidationDTO {

    @Schema(description = "是否有效", example = "true")
    private Boolean valid;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户名", example = "user123")
    private String username;

    @Schema(description = "令牌ID", example = "token_123456")
    private String tokenId;

    @Schema(description = "令牌类型", example = "access", allowableValues = {"access", "refresh"})
    private String tokenType;

    @Schema(description = "发布时间", example = "2024-01-01T12:00:00")
    private LocalDateTime issuedAt;

    @Schema(description = "过期时间", example = "2024-01-01T13:00:00")
    private LocalDateTime expiresAt;

    @Schema(description = "剩余有效时间(秒)", example = "3600")
    private Long remainingTime;

    @Schema(description = "用户角色列表", example = "[\"USER\", \"VIP\"]")
    private List<String> roles;

    @Schema(description = "用户权限列表", example = "[\"user:read\", \"user:write\"]")
    private List<String> permissions;

    @Schema(description = "VIP信息")
    private VipInfo vipInfo;

    @Schema(description = "设备信息", example = "iPhone 13")
    private String deviceInfo;

    @Schema(description = "IP地址", example = "***********")
    private String ipAddress;

    @Schema(description = "最后活跃时间", example = "2024-01-01T12:30:00")
    private LocalDateTime lastActiveTime;

    @Schema(description = "错误信息", example = "令牌已过期")
    private String errorMessage;

    @Schema(description = "错误代码", example = "TOKEN_EXPIRED")
    private String errorCode;

    /**
     * VIP信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "VIP信息")
    public static class VipInfo {
        
        @Schema(description = "VIP类型", example = "2", allowableValues = {"0", "1", "2"})
        private Integer vipType; // 0普通 1月会员 2年会员

        @Schema(description = "VIP状态", example = "1", allowableValues = {"0", "1"})
        private Integer vipStatus; // 0无效 1有效

        @Schema(description = "VIP到期时间", example = "2024-12-31T23:59:59")
        private LocalDateTime vipDueDate;

        @Schema(description = "VIP等级", example = "5")
        private Integer vipLevel;

        @Schema(description = "VIP权限列表", example = "[\"vip:download\", \"vip:quality\"]")
        private List<String> vipPermissions;
    }

    /**
     * 创建有效的验证结果
     */
    public static TokenValidationDTO valid(Long userId, String username, String tokenId) {
        return TokenValidationDTO.builder()
                .valid(true)
                .userId(userId)
                .username(username)
                .tokenId(tokenId)
                .build();
    }

    /**
     * 创建无效的验证结果
     */
    public static TokenValidationDTO invalid(String errorMessage, String errorCode) {
        return TokenValidationDTO.builder()
                .valid(false)
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .build();
    }
}
