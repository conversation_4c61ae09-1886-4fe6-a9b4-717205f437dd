package com.bilibili.api.auth.client;

import com.bilibili.api.auth.dto.LoginRequestDTO;
import com.bilibili.api.auth.dto.LoginResponseDTO;
import com.bilibili.api.auth.dto.RegisterRequestDTO;
import com.bilibili.api.auth.dto.TokenValidationDTO;
import com.bilibili.api.auth.dto.SendCodeRequestDTO;
import com.bilibili.common.response.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 认证服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@FeignClient(name = "bilibili-auth", path = "/api/v1/auth")
public interface AuthServiceClient {

    /**
     * 密码登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @PostMapping("/login-by-password")
    ApiResponse<LoginResponseDTO> loginByPassword(@RequestBody LoginRequestDTO request);

    /**
     * 验证码登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @PostMapping("/login-by-code")
    ApiResponse<LoginResponseDTO> loginByCode(@RequestBody LoginRequestDTO request);

    /**
     * 微信登录
     *
     * @param code 微信授权码
     * @param state 状态参数
     * @return 登录响应
     */
    @PostMapping("/wechat/login")
    ApiResponse<LoginResponseDTO> wechatLogin(@RequestParam("code") String code,
                                            @RequestParam(value = "state", required = false) String state);

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 注册响应
     */
    @PostMapping("/register")
    ApiResponse<Void> register(@RequestBody RegisterRequestDTO request);

    /**
     * 发送验证码
     *
     * @param request 发送验证码请求
     * @return 发送结果
     */
    @PostMapping("/send-code")
    ApiResponse<Void> sendCode(@RequestBody SendCodeRequestDTO request);

    /**
     * 验证访问令牌
     *
     * @param token 访问令牌
     * @return 令牌验证结果
     */
    @PostMapping("/validate-token")
    ApiResponse<TokenValidationDTO> validateToken(@RequestParam("token") String token);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
    @PostMapping("/refresh-token")
    ApiResponse<LoginResponseDTO> refreshToken(@RequestParam("refreshToken") String refreshToken);

    /**
     * 用户登出
     *
     * @param token 访问令牌
     * @return 登出结果
     */
    @PostMapping("/logout")
    ApiResponse<Void> logout(@RequestParam("token") String token);

    /**
     * 重置密码
     *
     * @param account 账号（手机号或邮箱）
     * @param code 验证码
     * @param newPassword 新密码
     * @return 重置结果
     */
    @PostMapping("/reset-password")
    ApiResponse<Void> resetPassword(@RequestParam("account") String account,
                                  @RequestParam("code") String code,
                                  @RequestParam("newPassword") String newPassword);

    /**
     * 修改密码
     *
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @param token 访问令牌
     * @return 修改结果
     */
    @PostMapping("/change-password")
    ApiResponse<Void> changePassword(@RequestParam("oldPassword") String oldPassword,
                                   @RequestParam("newPassword") String newPassword,
                                   @RequestHeader("Authorization") String token);

    /**
     * 绑定手机号
     *
     * @param phone 手机号
     * @param code 验证码
     * @param token 访问令牌
     * @return 绑定结果
     */
    @PostMapping("/bind-phone")
    ApiResponse<Void> bindPhone(@RequestParam("phone") String phone,
                              @RequestParam("code") String code,
                              @RequestHeader("Authorization") String token);

    /**
     * 绑定邮箱
     *
     * @param email 邮箱
     * @param code 验证码
     * @param token 访问令牌
     * @return 绑定结果
     */
    @PostMapping("/bind-email")
    ApiResponse<Void> bindEmail(@RequestParam("email") String email,
                              @RequestParam("code") String code,
                              @RequestHeader("Authorization") String token);

    /**
     * 检查用户名是否可用
     *
     * @param username 用户名
     * @return 是否可用
     */
    @GetMapping("/check-username")
    ApiResponse<Boolean> checkUsername(@RequestParam("username") String username);

    /**
     * 检查手机号是否已注册
     *
     * @param phone 手机号
     * @return 是否已注册
     */
    @GetMapping("/check-phone")
    ApiResponse<Boolean> checkPhone(@RequestParam("phone") String phone);

    /**
     * 检查邮箱是否已注册
     *
     * @param email 邮箱
     * @return 是否已注册
     */
    @GetMapping("/check-email")
    ApiResponse<Boolean> checkEmail(@RequestParam("email") String email);
}
