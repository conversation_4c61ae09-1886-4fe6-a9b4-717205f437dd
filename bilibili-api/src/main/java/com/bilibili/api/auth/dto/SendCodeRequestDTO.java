package com.bilibili.api.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;

/**
 * 发送验证码请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "发送验证码请求")
public class SendCodeRequestDTO {

    @Schema(description = "接收方式", example = "sms", allowableValues = {"sms", "email"})
    @NotBlank(message = "接收方式不能为空")
    private String type;

    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "验证码用途", example = "register", 
            allowableValues = {"register", "login", "reset_password", "bind_phone", "bind_email", "change_phone", "change_email"})
    @NotBlank(message = "验证码用途不能为空")
    private String purpose;

    @Schema(description = "图形验证码", example = "ABCD")
    private String captcha;

    @Schema(description = "图形验证码ID", example = "captcha_123456")
    private String captchaId;

    @Schema(description = "IP地址", example = "***********")
    private String ipAddress;

    @Schema(description = "用户代理", example = "Mozilla/5.0...")
    private String userAgent;

    @Schema(description = "设备指纹", example = "device_fingerprint_123")
    private String deviceFingerprint;

    /**
     * 获取接收地址
     */
    public String getReceiver() {
        if ("sms".equals(type)) {
            return phone;
        } else if ("email".equals(type)) {
            return email;
        }
        return null;
    }

    /**
     * 验证接收地址是否有效
     */
    public boolean isReceiverValid() {
        if ("sms".equals(type)) {
            return phone != null && phone.matches("^1[3-9]\\d{9}$");
        } else if ("email".equals(type)) {
            return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
        }
        return false;
    }
}
