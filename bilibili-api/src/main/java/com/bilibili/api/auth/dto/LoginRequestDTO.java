package com.bilibili.api.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 登录请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "登录请求")
public class LoginRequestDTO {

    @Schema(description = "账号（用户名/手机号/邮箱）", example = "user123")
    @NotBlank(message = "账号不能为空")
    private String account;

    @Schema(description = "密码", example = "password123")
    private String password;

    @Schema(description = "验证码", example = "123456")
    private String code;

    @Schema(description = "登录类型", example = "password", allowableValues = {"password", "sms", "email"})
    @NotBlank(message = "登录类型不能为空")
    private String loginType; // password, sms, email

    @Schema(description = "设备信息", example = "iPhone 13")
    private String deviceInfo;

    @Schema(description = "IP地址", example = "***********")
    private String ipAddress;

    @Schema(description = "用户代理", example = "Mozilla/5.0...")
    private String userAgent;

    @Schema(description = "是否记住登录", example = "false")
    private Boolean rememberMe = false;
}
