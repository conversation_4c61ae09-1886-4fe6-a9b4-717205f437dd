package com.bilibili.api.ai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 推荐结果DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "推荐结果")
public class RecommendationDTO {

    @Schema(description = "推荐ID", example = "rec_123456")
    private String recommendationId;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "推荐类型", example = "video", 
            allowableValues = {"video", "user", "article", "live", "playlist"})
    private String recommendType;

    @Schema(description = "推荐算法", example = "collaborative_filtering", 
            allowableValues = {"collaborative_filtering", "content_based", "hybrid", "trending", "personalized"})
    private String algorithm;

    @Schema(description = "推荐项目列表")
    private List<RecommendationItem> items;

    @Schema(description = "推荐原因")
    private List<String> reasons;

    @Schema(description = "推荐置信度", example = "0.85")
    private Double confidence;

    @Schema(description = "推荐多样性得分", example = "0.7")
    private Double diversity;

    @Schema(description = "推荐新颖性得分", example = "0.6")
    private Double novelty;

    @Schema(description = "推荐上下文")
    private RecommendationContext context;

    @Schema(description = "扩展数据")
    private Map<String, Object> metadata;

    @Schema(description = "生成时间", example = "2024-01-01T12:00:00")
    private LocalDateTime generatedAt;

    @Schema(description = "过期时间", example = "2024-01-01T18:00:00")
    private LocalDateTime expiresAt;

    /**
     * 推荐项目
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "推荐项目")
    public static class RecommendationItem {

        @Schema(description = "项目ID", example = "123456")
        private String itemId;

        @Schema(description = "项目类型", example = "video")
        private String itemType;

        @Schema(description = "项目标题", example = "精彩视频推荐")
        private String title;

        @Schema(description = "项目描述", example = "这是一个很有趣的视频")
        private String description;

        @Schema(description = "缩略图URL", example = "https://example.com/thumb.jpg")
        private String thumbnail;

        @Schema(description = "项目URL", example = "https://example.com/video/123456")
        private String url;

        @Schema(description = "推荐得分", example = "0.9")
        private Double score;

        @Schema(description = "推荐排名", example = "1")
        private Integer rank;

        @Schema(description = "推荐权重", example = "0.8")
        private Double weight;

        @Schema(description = "项目标签")
        private List<String> tags;

        @Schema(description = "项目分类", example = "科技")
        private String category;

        @Schema(description = "作者信息")
        private AuthorInfo author;

        @Schema(description = "统计信息")
        private ItemStats stats;

        @Schema(description = "推荐原因")
        private List<String> reasons;

        @Schema(description = "项目元数据")
        private Map<String, Object> metadata;
    }

    /**
     * 作者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "作者信息")
    public static class AuthorInfo {

        @Schema(description = "作者ID", example = "123456")
        private Long authorId;

        @Schema(description = "作者名称", example = "UP主小明")
        private String authorName;

        @Schema(description = "作者头像", example = "https://example.com/avatar.jpg")
        private String authorAvatar;

        @Schema(description = "是否已关注", example = "false")
        private Boolean isFollowed;

        @Schema(description = "粉丝数", example = "10000")
        private Integer followerCount;
    }

    /**
     * 项目统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "项目统计信息")
    public static class ItemStats {

        @Schema(description = "播放量", example = "10000")
        private Long playCount;

        @Schema(description = "点赞数", example = "500")
        private Long likeCount;

        @Schema(description = "评论数", example = "100")
        private Long commentCount;

        @Schema(description = "分享数", example = "50")
        private Long shareCount;

        @Schema(description = "收藏数", example = "200")
        private Long favoriteCount;

        @Schema(description = "发布时间", example = "2024-01-01T10:00:00")
        private LocalDateTime publishTime;

        @Schema(description = "时长(秒)", example = "300")
        private Integer duration;
    }

    /**
     * 推荐上下文
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "推荐上下文")
    public static class RecommendationContext {

        @Schema(description = "推荐场景", example = "homepage", 
                allowableValues = {"homepage", "search", "related", "category", "personal"})
        private String scene;

        @Schema(description = "用户当前位置", example = "首页")
        private String location;

        @Schema(description = "推荐时间段", example = "evening")
        private String timeSlot;

        @Schema(description = "设备类型", example = "mobile", allowableValues = {"mobile", "desktop", "tablet"})
        private String deviceType;

        @Schema(description = "用户行为历史")
        private List<String> recentActions;

        @Schema(description = "用户兴趣标签")
        private List<String> interestTags;

        @Schema(description = "上下文参数")
        private Map<String, Object> parameters;
    }

    /**
     * 创建空的推荐结果
     */
    public static RecommendationDTO empty(Long userId, String recommendType) {
        return RecommendationDTO.builder()
                .userId(userId)
                .recommendType(recommendType)
                .items(List.of())
                .confidence(0.0)
                .generatedAt(LocalDateTime.now())
                .build();
    }
}
