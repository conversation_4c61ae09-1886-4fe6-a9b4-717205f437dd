package com.bilibili.api.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天会话VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "聊天会话信息")
public class ChatSessionVO {

    @Schema(description = "会话ID", example = "session_123456")
    private String sessionId;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "会话标题", example = "关于视频制作的讨论")
    private String title;

    @Schema(description = "会话描述", example = "讨论视频制作技巧和工具")
    private String description;

    @Schema(description = "会话状态", example = "active", allowableValues = {"active", "archived", "deleted"})
    private String status;

    @Schema(description = "会话类型", example = "general", 
            allowableValues = {"general", "creative", "analytical", "assistant"})
    private String sessionType;

    @Schema(description = "消息总数", example = "25")
    private Integer messageCount;

    @Schema(description = "最后一条消息", example = "感谢您的建议，我会尝试的！")
    private String lastMessage;

    @Schema(description = "最后消息时间", example = "2024-01-01T12:30:00")
    private LocalDateTime lastMessageTime;

    @Schema(description = "最后消息发送者", example = "user", allowableValues = {"user", "ai"})
    private String lastMessageSender;

    @Schema(description = "会话标签")
    private List<String> tags;

    @Schema(description = "是否置顶", example = "false")
    private Boolean isPinned;

    @Schema(description = "是否收藏", example = "false")
    private Boolean isFavorite;

    @Schema(description = "未读消息数", example = "0")
    private Integer unreadCount;

    @Schema(description = "会话配置")
    private SessionConfig config;

    @Schema(description = "会话统计")
    private SessionStats stats;

    @Schema(description = "创建时间", example = "2024-01-01T10:00:00")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间", example = "2024-01-01T12:30:00")
    private LocalDateTime updatedAt;

    /**
     * 会话配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "会话配置")
    public static class SessionConfig {

        @Schema(description = "AI模型", example = "gpt-4")
        private String model;

        @Schema(description = "温度参数", example = "0.7")
        private Double temperature;

        @Schema(description = "最大回复长度", example = "1000")
        private Integer maxLength;

        @Schema(description = "上下文窗口大小", example = "10")
        private Integer contextWindow;

        @Schema(description = "是否启用流式响应", example = "false")
        private Boolean streamEnabled;

        @Schema(description = "是否启用历史上下文", example = "true")
        private Boolean historyEnabled;

        @Schema(description = "自动保存间隔(秒)", example = "30")
        private Integer autoSaveInterval;
    }

    /**
     * 会话统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "会话统计")
    public static class SessionStats {

        @Schema(description = "用户消息数", example = "12")
        private Integer userMessageCount;

        @Schema(description = "AI消息数", example = "13")
        private Integer aiMessageCount;

        @Schema(description = "总Token使用量", example = "5000")
        private Integer totalTokens;

        @Schema(description = "平均响应时间(毫秒)", example = "1500")
        private Long avgResponseTime;

        @Schema(description = "会话时长(分钟)", example = "45")
        private Integer sessionDuration;

        @Schema(description = "最后活跃时间", example = "2024-01-01T12:30:00")
        private LocalDateTime lastActiveTime;

        @Schema(description = "满意度评分", example = "4.5")
        private Double satisfactionScore;
    }

    /**
     * 获取会话摘要
     */
    public String getSummary() {
        if (description != null && !description.isEmpty()) {
            return description;
        }
        if (lastMessage != null && lastMessage.length() > 50) {
            return lastMessage.substring(0, 50) + "...";
        }
        return lastMessage;
    }

    /**
     * 检查会话是否活跃
     */
    public boolean isActive() {
        return "active".equals(status) && 
               lastMessageTime != null && 
               lastMessageTime.isAfter(LocalDateTime.now().minusHours(24));
    }
}
