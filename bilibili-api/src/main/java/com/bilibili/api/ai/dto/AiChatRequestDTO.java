package com.bilibili.api.ai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * AI聊天请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "AI聊天请求")
public class AiChatRequestDTO {

    @Schema(description = "用户ID", example = "123456")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "会话ID", example = "session_123456")
    private String sessionId;

    @Schema(description = "消息内容", example = "你好，请帮我分析一下这个视频")
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 4000, message = "消息内容不能超过4000个字符")
    private String message;

    @Schema(description = "消息类型", example = "text", allowableValues = {"text", "image", "video", "audio"})
    private String messageType = "text";

    @Schema(description = "聊天模式", example = "general", 
            allowableValues = {"general", "creative", "analytical", "assistant"})
    private String chatMode = "general";

    @Schema(description = "是否流式响应", example = "false")
    private Boolean stream = false;

    @Schema(description = "最大回复长度", example = "1000")
    private Integer maxLength = 1000;

    @Schema(description = "温度参数", example = "0.7")
    private Double temperature = 0.7;

    @Schema(description = "上下文窗口大小", example = "10")
    private Integer contextWindow = 10;

    @Schema(description = "附加文件列表")
    private List<AttachmentFile> attachments;

    @Schema(description = "扩展参数")
    private Map<String, Object> extraParams;

    @Schema(description = "引用的消息ID", example = "msg_123456")
    private String replyToMessageId;

    @Schema(description = "是否包含历史上下文", example = "true")
    private Boolean includeHistory = true;

    @Schema(description = "优先级", example = "normal", allowableValues = {"low", "normal", "high"})
    private String priority = "normal";

    /**
     * 附件文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "附件文件")
    public static class AttachmentFile {
        
        @Schema(description = "文件ID", example = "file_123456")
        private String fileId;

        @Schema(description = "文件名", example = "video.mp4")
        private String fileName;

        @Schema(description = "文件类型", example = "video/mp4")
        private String fileType;

        @Schema(description = "文件大小(字节)", example = "1048576")
        private Long fileSize;

        @Schema(description = "文件URL", example = "https://example.com/file.mp4")
        private String fileUrl;

        @Schema(description = "文件描述", example = "需要分析的视频文件")
        private String description;
    }
}
