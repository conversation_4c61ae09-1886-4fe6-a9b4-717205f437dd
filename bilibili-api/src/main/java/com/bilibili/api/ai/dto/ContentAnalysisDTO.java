package com.bilibili.api.ai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 内容分析结果DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "内容分析结果")
public class ContentAnalysisDTO {

    @Schema(description = "分析ID", example = "analysis_123456")
    private String analysisId;

    @Schema(description = "内容ID", example = "content_123456")
    private String contentId;

    @Schema(description = "内容类型", example = "video", allowableValues = {"text", "video", "image", "audio"})
    private String contentType;

    @Schema(description = "分析类型", example = "sentiment", 
            allowableValues = {"sentiment", "topic", "quality", "safety", "summary", "tags"})
    private String analysisType;

    @Schema(description = "分析状态", example = "completed", allowableValues = {"pending", "processing", "completed", "failed"})
    private String status;

    @Schema(description = "分析结果")
    private AnalysisResult result;

    @Schema(description = "置信度", example = "0.95")
    private Double confidence;

    @Schema(description = "处理时间(毫秒)", example = "1500")
    private Long processingTime;

    @Schema(description = "错误信息", example = "分析失败")
    private String errorMessage;

    @Schema(description = "创建时间", example = "2024-01-01T12:00:00")
    private LocalDateTime createdAt;

    @Schema(description = "完成时间", example = "2024-01-01T12:00:01")
    private LocalDateTime completedAt;

    /**
     * 分析结果内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "分析结果详情")
    public static class AnalysisResult {

        @Schema(description = "情感分析结果")
        private SentimentAnalysis sentiment;

        @Schema(description = "主题分析结果")
        private TopicAnalysis topic;

        @Schema(description = "质量评估结果")
        private QualityAssessment quality;

        @Schema(description = "安全检测结果")
        private SafetyCheck safety;

        @Schema(description = "摘要生成结果")
        private String summary;

        @Schema(description = "标签提取结果")
        private List<String> tags;

        @Schema(description = "关键词提取结果")
        private List<String> keywords;

        @Schema(description = "扩展数据")
        private Map<String, Object> metadata;
    }

    /**
     * 情感分析结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "情感分析结果")
    public static class SentimentAnalysis {

        @Schema(description = "整体情感", example = "positive", allowableValues = {"positive", "negative", "neutral"})
        private String overall;

        @Schema(description = "积极情感得分", example = "0.8")
        private Double positiveScore;

        @Schema(description = "消极情感得分", example = "0.1")
        private Double negativeScore;

        @Schema(description = "中性情感得分", example = "0.1")
        private Double neutralScore;

        @Schema(description = "情感强度", example = "0.7")
        private Double intensity;

        @Schema(description = "情感关键词")
        private List<String> emotionKeywords;
    }

    /**
     * 主题分析结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "主题分析结果")
    public static class TopicAnalysis {

        @Schema(description = "主要主题")
        private List<Topic> mainTopics;

        @Schema(description = "主题分布")
        private Map<String, Double> topicDistribution;

        @Schema(description = "主题相关性")
        private Double relevance;
    }

    /**
     * 主题信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "主题信息")
    public static class Topic {

        @Schema(description = "主题名称", example = "科技")
        private String name;

        @Schema(description = "主题权重", example = "0.8")
        private Double weight;

        @Schema(description = "相关关键词")
        private List<String> keywords;
    }

    /**
     * 质量评估结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "质量评估结果")
    public static class QualityAssessment {

        @Schema(description = "整体质量得分", example = "8.5")
        private Double overallScore;

        @Schema(description = "内容原创性", example = "0.9")
        private Double originality;

        @Schema(description = "内容完整性", example = "0.8")
        private Double completeness;

        @Schema(description = "内容准确性", example = "0.9")
        private Double accuracy;

        @Schema(description = "内容可读性", example = "0.7")
        private Double readability;

        @Schema(description = "质量建议")
        private List<String> suggestions;
    }

    /**
     * 安全检测结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "安全检测结果")
    public static class SafetyCheck {

        @Schema(description = "是否安全", example = "true")
        private Boolean isSafe;

        @Schema(description = "风险等级", example = "low", allowableValues = {"low", "medium", "high"})
        private String riskLevel;

        @Schema(description = "检测到的问题")
        private List<SafetyIssue> issues;

        @Schema(description = "安全得分", example = "0.95")
        private Double safetyScore;
    }

    /**
     * 安全问题
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "安全问题")
    public static class SafetyIssue {

        @Schema(description = "问题类型", example = "spam")
        private String type;

        @Schema(description = "问题描述", example = "检测到垃圾信息")
        private String description;

        @Schema(description = "严重程度", example = "medium", allowableValues = {"low", "medium", "high"})
        private String severity;

        @Schema(description = "置信度", example = "0.8")
        private Double confidence;
    }
}
