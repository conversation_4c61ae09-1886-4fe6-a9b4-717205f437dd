package com.bilibili.api.ai.client;

import com.bilibili.api.ai.dto.AiChatRequestDTO;
import com.bilibili.api.ai.dto.AiChatResponseDTO;
import com.bilibili.api.ai.dto.ContentAnalysisDTO;
import com.bilibili.api.ai.dto.RecommendationDTO;
import com.bilibili.common.response.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@FeignClient(name = "bilibili-aiassistant", path = "/api/v1/ai")
public interface AiServiceClient {

    /**
     * AI聊天对话
     *
     * @param request 聊天请求
     * @return 聊天响应
     */
    @PostMapping("/chat")
    ApiResponse<AiChatResponseDTO> chat(@RequestBody AiChatRequestDTO request);

    /**
     * 流式AI聊天对话
     *
     * @param request 聊天请求
     * @return 流式响应
     */
    @PostMapping("/chat/stream")
    ApiResponse<String> chatStream(@RequestBody AiChatRequestDTO request);

    /**
     * 获取聊天会话历史
     *
     * @param sessionId 会话ID
     * @param page 页码
     * @param size 每页大小
     * @return 聊天历史
     */
    @GetMapping("/chat/history/{sessionId}")
    ApiResponse<List<AiChatResponseDTO>> getChatHistory(@PathVariable("sessionId") String sessionId,
                                                       @RequestParam(defaultValue = "1") Integer page,
                                                       @RequestParam(defaultValue = "20") Integer size);

    /**
     * 创建新的聊天会话
     *
     * @param userId 用户ID
     * @param title 会话标题
     * @return 会话ID
     */
    @PostMapping("/chat/session")
    ApiResponse<String> createChatSession(@RequestParam("userId") Long userId,
                                        @RequestParam(value = "title", required = false) String title);

    /**
     * 删除聊天会话
     *
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/chat/session/{sessionId}")
    ApiResponse<Void> deleteChatSession(@PathVariable("sessionId") String sessionId,
                                      @RequestParam("userId") Long userId);

    /**
     * 获取用户的聊天会话列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 会话列表
     */
    @GetMapping("/chat/sessions")
    ApiResponse<List<Object>> getChatSessions(@RequestParam("userId") Long userId,
                                            @RequestParam(defaultValue = "1") Integer page,
                                            @RequestParam(defaultValue = "20") Integer size);

    /**
     * 内容分析
     *
     * @param content 内容文本
     * @param analysisType 分析类型
     * @return 分析结果
     */
    @PostMapping("/analysis/content")
    ApiResponse<ContentAnalysisDTO> analyzeContent(@RequestParam("content") String content,
                                                  @RequestParam("analysisType") String analysisType);

    /**
     * 视频内容分析
     *
     * @param videoId 视频ID
     * @param analysisType 分析类型
     * @return 分析结果
     */
    @PostMapping("/analysis/video/{videoId}")
    ApiResponse<ContentAnalysisDTO> analyzeVideo(@PathVariable("videoId") Long videoId,
                                               @RequestParam("analysisType") String analysisType);

    /**
     * 获取个性化推荐
     *
     * @param userId 用户ID
     * @param recommendType 推荐类型
     * @param limit 推荐数量
     * @return 推荐结果
     */
    @GetMapping("/recommendation")
    ApiResponse<List<RecommendationDTO>> getRecommendations(@RequestParam("userId") Long userId,
                                                           @RequestParam("recommendType") String recommendType,
                                                           @RequestParam(defaultValue = "10") Integer limit);

    /**
     * 智能标签生成
     *
     * @param content 内容
     * @param maxTags 最大标签数
     * @return 标签列表
     */
    @PostMapping("/tags/generate")
    ApiResponse<List<String>> generateTags(@RequestParam("content") String content,
                                         @RequestParam(defaultValue = "5") Integer maxTags);

    /**
     * 内容摘要生成
     *
     * @param content 内容
     * @param maxLength 最大长度
     * @return 摘要
     */
    @PostMapping("/summary/generate")
    ApiResponse<String> generateSummary(@RequestParam("content") String content,
                                      @RequestParam(defaultValue = "200") Integer maxLength);

    /**
     * 智能回复建议
     *
     * @param context 上下文
     * @param replyType 回复类型
     * @return 回复建议
     */
    @PostMapping("/reply/suggest")
    ApiResponse<List<String>> suggestReplies(@RequestParam("context") String context,
                                           @RequestParam("replyType") String replyType);

    /**
     * 敏感内容检测
     *
     * @param content 内容
     * @return 检测结果
     */
    @PostMapping("/moderation/check")
    ApiResponse<Object> checkContent(@RequestParam("content") String content);

    /**
     * 获取AI模型状态
     *
     * @return 模型状态
     */
    @GetMapping("/status")
    ApiResponse<Object> getAiStatus();
}
