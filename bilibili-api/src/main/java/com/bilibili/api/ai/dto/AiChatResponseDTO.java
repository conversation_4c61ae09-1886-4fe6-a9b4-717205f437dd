package com.bilibili.api.ai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * AI聊天响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "AI聊天响应")
public class AiChatResponseDTO {

    @Schema(description = "消息ID", example = "msg_123456")
    private String messageId;

    @Schema(description = "会话ID", example = "session_123456")
    private String sessionId;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "消息内容", example = "根据您提供的视频，我分析如下...")
    private String content;

    @Schema(description = "消息类型", example = "text", allowableValues = {"text", "image", "video", "audio"})
    private String messageType = "text";

    @Schema(description = "发送者类型", example = "ai", allowableValues = {"user", "ai", "system"})
    private String senderType;

    @Schema(description = "AI模型名称", example = "gpt-4")
    private String modelName;

    @Schema(description = "响应状态", example = "success", allowableValues = {"success", "error", "partial"})
    private String status = "success";

    @Schema(description = "错误信息", example = "模型暂时不可用")
    private String errorMessage;

    @Schema(description = "置信度", example = "0.95")
    private Double confidence;

    @Schema(description = "处理时间(毫秒)", example = "1500")
    private Long processingTime;

    @Schema(description = "Token使用量")
    private TokenUsage tokenUsage;

    @Schema(description = "建议的后续问题")
    private List<String> suggestedQuestions;

    @Schema(description = "相关资源链接")
    private List<ResourceLink> relatedResources;

    @Schema(description = "扩展数据")
    private Map<String, Object> metadata;

    @Schema(description = "创建时间", example = "2024-01-01T12:00:00")
    private LocalDateTime createdAt;

    @Schema(description = "是否为流式响应的最后一块", example = "true")
    private Boolean isLast = true;

    @Schema(description = "流式响应序号", example = "1")
    private Integer chunkIndex;

    /**
     * Token使用量统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Token使用量")
    public static class TokenUsage {
        
        @Schema(description = "输入Token数", example = "100")
        private Integer inputTokens;

        @Schema(description = "输出Token数", example = "200")
        private Integer outputTokens;

        @Schema(description = "总Token数", example = "300")
        private Integer totalTokens;

        @Schema(description = "估算费用(分)", example = "5")
        private Integer estimatedCost;
    }

    /**
     * 相关资源链接
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "相关资源链接")
    public static class ResourceLink {
        
        @Schema(description = "资源标题", example = "相关视频推荐")
        private String title;

        @Schema(description = "资源URL", example = "https://example.com/video/123")
        private String url;

        @Schema(description = "资源类型", example = "video", allowableValues = {"video", "article", "user", "playlist"})
        private String type;

        @Schema(description = "资源描述", example = "这个视频与您的问题相关")
        private String description;

        @Schema(description = "缩略图URL", example = "https://example.com/thumb.jpg")
        private String thumbnail;
    }
}
