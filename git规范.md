------

# Git 开发规范与工作流指南

## 1. 简介

为提升团队协作效率、保证代码库的整洁性、可追溯性以及稳定性，我们统一采用本规范作为团队的 Git 工作流标准。所有团队成员**必须**遵守本文档中定义的规范和流程。

**核心目标：**

- 保持 master 分支的稳定与纯净。
- 实现清晰、可读、有意义的提交历史。
- 简化并行开发与功能集成的流程。
- 提高代码审查（Code Review）的效率。

## 2. 核心原则

1. **主分支保护**：master 分支是项目的主分支，它包含的是稳定、可随时部署发布的版本。**严禁直接向 master 分支推送（push）任何代码**。
2. **功能分支驱动**：所有新的功能开发、Bug 修复、代码重构等任务，都**必须**在一个新的、独立的**功能分支**上进行。
3. **提交原子化**：每次提交（commit）应尽可能只包含一个逻辑上独立的改动点，保持颗粒度精细。
4. **Pull Request (PR) 机制**：所有功能分支在开发完成后，**必须**通过创建 Pull Request (合并请求) 的方式合入 master 分支，并至少需要一名其他成员审查（Review）通过。

## 3. 分支管理策略

### 3.1. 主要分支

- master: 主分支。永远处于可发布状态。所有的提交都来自于其他分支的合并。

### 3.2. 辅助分支（功能分支）

所有开发工作都在功能分支上进行。功能分支的生命周期是短暂的，完成合并后即被删除。

#### 分支命名规范

分支命名应清晰、简洁、具有描述性。统一采用 类型/简要描述 的格式。

- **新功能开发**: feature/功能简述
  - feature/user-login
  - feature/order-management
- **Bug 修复**: fix/问题描述
  - fix/login-validation-error
  - fix/npe-in-order-service
- **代码重构**: refactor/模块名
  - refactor/payment-module
- **文档相关**: docs/文档描述
  - docs/update-api-readme
- **紧急修复 (Hotfix)**: hotfix/紧急问题描述
  - hotfix/critical-security-vulnerability

------



## 4. 标准开发工作流程

这是一个完整的、从领任务到代码上线的标准流程。

### 第 1 步：开始新任务（创建功能分支）

在开始编码前，务必从最新的 master 分支创建你的功能分支。

Generated bash

```
# 1. 切换到本地 master 分支
git checkout master

# 2. 拉取远程 master 分支的最新代码，确保本地与远程同步
git pull origin master

# 3. 从最新的 master 分支创建并切换到你的新功能分支
#    (请根据任务类型和内容修改分支名)
git checkout -b feature/user-registration
```

Use code [with caution](https://support.google.com/legal/answer/13505487).Bash

### 第 2 步：在功能分支上开发与提交

现在你可以在 feature/user-registration 分支上进行开发。

- **频繁提交**：完成一个小的功能点就进行一次提交。
- **遵循提交信息规范**：每次提交都必须编写清晰、规范的 Commit Message (详见第 5 节)。

Generated bash

```
# ...进行代码编写和修改...

# 将修改的文件添加到暂存区
git add .

# 提交你的改动
git commit -m "feat: 实现用户注册接口"

# ...继续开发和提交...
git commit -m "fix: 修复密码加密逻辑错误"
```

Use code [with caution](https://support.google.com/legal/answer/13505487).Bash

### 第 3 步：推送功能分支到远程仓库

为了代码备份和后续的 Pull Request，你需要将本地的功能分支推送到远程仓库。

Generated bash

```
# 第一次推送时，使用 -u 参数将本地分支与远程分支关联
git push -u origin feature/user-registration

# 后续的推送，直接使用 git push 即可
git push
```

Use code [with caution](https://support.google.com/legal/answer/13505487).Bash

### 第 4 步：创建 Pull Request (PR)

当功能开发完毕，并且在本地自测通过后，就可以发起 Pull Request 请求将代码合并到 master 分支。

1. 打开项目的 GitHub/GitLab 页面。
2. 系统通常会自动检测到你新推送的分支，并提示你创建 Pull Request。
3. 点击 "Compare & pull request" 按钮。
4. **确保目标分支 (base) 是 master，源分支 (compare) 是你的功能分支。**
5. **填写标题和描述**：
   - **标题**: 简明扼要地概括这个 PR 的主要内容，例如 Feat: Add User Registration Functionality。
   - **描述**: 详细说明你做了什么、解决了什么问题、如何测试。如果有相关的任务链接，也请附上。
6. 在右侧指定至少一位审查者 (Reviewer)。
7. 点击 "Create pull request"。

### 第 5 步：代码审查与合并

- 审查者会检查你的代码，并可能提出修改意见。
- 你需要根据意见在本地功能分支上进行修改，然后 git push 更新你的 Pull Request。
- 当审查通过后，PR 的创建者或拥有权限的成员点击 "Merge pull request" 按钮将代码合并到 master。
- **合并后，立即点击 "Delete branch" 按钮删除远程的那个功能分支**，保持仓库整洁。

### 第 6 步：清理本地环境

代码合并后，你的本地还残留着已完成任务的功能分支，需要清理。

Generated bash

```
# 1. 切换回 master 分支
git checkout master

# 2. 拉取远程更新 (包含了你刚刚合并的代码)
git pull origin master

# 3. 删除已经合并的本地功能分支
git branch -d feature/user-registration
```

Use code [with caution](https://support.google.com/legal/answer/13505487).Bash

至此，一个完整的开发周期结束。你可以从第 1 步开始下一个任务。

------



## 5. Commit Message 规范

我们采用 **Conventional Commits** 规范。一个好的提交信息能极大地提高代码历史的可读性。

**格式**:

Generated code

```
<type>: <subject>

[optional body]

[optional footer]
```

Use code [with caution](https://support.google.com/legal/answer/13505487).

#### 5.1. type (提交类型) - 必须

- feat: 新功能 (feature)
- fix: 修复 Bug
- docs: 文档变更
- style: 代码格式（不影响代码运行的变动，如空格、分号等）
- refactor: 重构（既不是新增功能，也不是修改 bug 的代码变动）
- test: 增加或修改测试
- chore: 构建过程或辅助工具的变动（如修改 .gitignore）
- perf: 提升性能的代码变动

#### 5.2. subject (主题) - 必须

- 简短描述，动词开头，不超过 50 个字符。
- 首字母无需大写，结尾不加句号。

#### 5.3. 示例

**好的例子 👍:**

Generated code

```
feat: add user login endpoint
fix: correct calculation of order total price
docs: update installation guide
refactor: simplify user service logic
```

Use code [with caution](https://support.google.com/legal/answer/13505487).

**坏的例子 👎:**

Generated code

```
fix bug
update
提交代码
完成了登录功能
```

Use code [with caution](https://support.google.com/legal/answer/13505487).

## 6. 其他重要规则

- **禁止 force push**：任何情况下都禁止对 master 分支进行 git push --force。对个人功能分支的 force push 也应谨慎，并确保不会影响到他人。

- **保持更新**：如果你的功能分支开发周期较长，应定期将 master 分支的更新合并到你的分支，以避免最后产生巨大的合并冲突。

  Generated bash

  ```
  # 在你的功能分支上
  git pull origin master
  ```

  Use code [with caution](https://support.google.com/legal/answer/13505487).Bash

- **解决冲突**：在 pull 或 merge 时遇到冲突，必须在本地解决并测试通过后，才能提交。请联系相关代码的负责人共同解决冲突。

------