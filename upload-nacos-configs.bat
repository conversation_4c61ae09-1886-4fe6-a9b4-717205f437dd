@echo off
chcp 65001 >nul 2>&1
echo.
echo ==========================================
echo    Bilibili Nacos Config Upload Tool
echo ==========================================
echo.
echo Uploading configuration files to Nacos server...
echo.

set NACOS_SERVER=8.156.72.38:8848

echo [1/11] Uploading bilibili-auth-service.yml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=bilibili-auth-service.yml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/bilibili-auth-service.yml"

echo.
echo [2/11] Uploading bilibili-user-service.yml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=bilibili-user-service.yml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/bilibili-user-service.yml"

echo.
echo [3/11] Uploading shared-spring.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-spring.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-spring.yaml"

echo.
echo [4/11] Uploading shared-mybatis.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-mybatis.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-mybatis.yaml"

echo.
echo [5/11] Uploading shared-redis.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-redis.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-redis.yaml"

echo.
echo [6/11] Uploading shared-mq.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-mq.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-mq.yaml"

echo.
echo [7/11] Uploading shared-feign.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-feign.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-feign.yaml"

echo.
echo [8/11] Uploading shared-sms.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-sms.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-sms.yaml"

echo.
echo [9/11] Uploading shared-security.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-security.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-security.yaml"

echo.
echo [10/11] Uploading shared-monitoring.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-monitoring.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-monitoring.yaml"

echo.
echo [11/11] Uploading shared-swagger.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-swagger.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-swagger.yaml"

echo.
echo [12/11] Uploading shared-elasticsearch.yaml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=shared-elasticsearch.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/shared-elasticsearch.yaml"

echo.
echo [13/11] Uploading bilibili-gateway-service.yml...
curl -X POST "http://%NACOS_SERVER%/nacos/v1/cs/configs" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "dataId=bilibili-gateway-service.yml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "type=yaml" ^
  --data-urlencode "content@nacos-configs/bilibili-gateway-service.yml"

echo.
echo ==========================================
echo    All Configuration Files Uploaded!
echo ==========================================
echo.
echo Service-specific configs:
echo   - bilibili-auth-service.yml
echo   - bilibili-user-service.yml
echo   - bilibili-gateway-service.yml
echo.
echo Shared infrastructure configs:
echo   - shared-spring.yaml
echo   - shared-mybatis.yaml
echo   - shared-redis.yaml
echo   - shared-mq.yaml
echo   - shared-feign.yaml
echo   - shared-sms.yaml
echo   - shared-elasticsearch.yaml
echo.
echo Shared feature configs:
echo   - shared-security.yaml
echo   - shared-monitoring.yaml
echo   - shared-swagger.yaml
echo.
echo ==========================================
echo.
echo Nacos Admin: http://%NACOS_SERVER%/nacos
echo.
echo Service URLs:
echo   Gateway:  http://localhost:8080
echo   API Docs: http://localhost:8080/doc.html
echo   Auth:     http://localhost:8090
echo   User:     http://localhost:8091
echo.
echo Next Steps:
echo   1. Check Nacos admin panel
echo   2. Restart services to load new configs
echo   3. Test the APIs
echo.
pause
