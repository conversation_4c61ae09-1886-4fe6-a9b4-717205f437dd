package com.bilibili.auth.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.bilibili.auth.entity.User;
import com.bilibili.auth.security.CustomUserDetails;
import com.bilibili.auth.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * UserController 测试类
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@ExtendWith(MockitoExtension.class)
@WebMvcTest(UserController.class)
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext context;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;
    private CustomUserDetails userDetails;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();

        // 创建测试用户
        testUser = User.builder()
                .id(1L)
                .uid(100000001L)
                .username("testuser")
                .email("<EMAIL>")
                .phone("13812345678")
                .nickname("测试用户")
                .signature("这是测试签名")
                .gender(1)
                .birthday(LocalDate.of(1990, 1, 1))
                .location("北京")
                .level(5)
                .exp(1000)
                .coins(100.0)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        userDetails = new CustomUserDetails(testUser);
    }

    @Test
    @WithMockUser(roles = {"USER"})
    void getCurrentUser_Success() throws Exception {
        // Mock 服务层返回
        when(userService.getUserByUsername("testuser")).thenReturn(testUser);

        mockMvc.perform(get("/user/me")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取用户信息成功"))
                .andExpect(jsonPath("$.data.username").value("testuser"))
                .andExpect(jsonPath("$.data.nickname").value("测试用户"))
                .andExpect(jsonPath("$.data.uid").value(100000001L));

        // 验证服务层调用
        verify(userService, times(1)).getUserByUsername("testuser");
    }

    @Test
    @WithMockUser(roles = {"USER"})
    void getCurrentUser_UserNotFound() throws Exception {
        // Mock 服务层返回 null
        when(userService.getUserByUsername("testuser")).thenReturn(null);

        mockMvc.perform(get("/user/me")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("用户不存在"));

        verify(userService, times(1)).getUserByUsername("testuser");
    }

    @Test
    void getCurrentUser_Unauthorized() throws Exception {
        // 没有认证的请求
        mockMvc.perform(get("/user/me")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        // 不应该调用服务层
        verify(userService, never()).getUserByUsername(any());
    }

    @Test
    @WithMockUser(roles = {"ADMIN"}) // 错误的角色
    void getCurrentUser_Forbidden() throws Exception {
        mockMvc.perform(get("/user/me")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(userService, never()).getUserByUsername(any());
    }

    @Test
    void getUserByUid_Success() throws Exception {
        // Mock 服务层返回
        when(userService.getUserByUid(100000001L)).thenReturn(testUser);

        mockMvc.perform(get("/user/100000001")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取用户信息成功"))
                .andExpect(jsonPath("$.data.uid").value(100000001L))
                .andExpect(jsonPath("$.data.username").value("testuser"))
                .andExpect(jsonPath("$.data.nickname").value("测试用户"))
                // 验证敏感信息已被隐藏
                .andExpect(jsonPath("$.data.passwordHash").doesNotExist())
                .andExpect(jsonPath("$.data.salt").doesNotExist())
                .andExpect(jsonPath("$.data.email").doesNotExist())
                .andExpect(jsonPath("$.data.phone").doesNotExist());

        verify(userService, times(1)).getUserByUid(100000001L);
    }

    @Test
    void getUserByUid_UserNotFound() throws Exception {
        // Mock 服务层返回 null
        when(userService.getUserByUid(999999L)).thenReturn(null);

        mockMvc.perform(get("/user/999999")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("用户不存在"));

        verify(userService, times(1)).getUserByUid(999999L);
    }

    @Test
    @WithMockUser(roles = {"USER"})
    void updateUserInfo_Success() throws Exception {
        // 准备更新请求
        UserController.UpdateUserRequest request = new UserController.UpdateUserRequest();
        request.setNickname("新昵称");
        request.setSignature("新签名");
        request.setGender(2);
        request.setBirthday(LocalDate.of(1995, 5, 15));
        request.setLocation("上海");

        // Mock 服务层返回
        when(userService.getUserByUsername("testuser")).thenReturn(testUser);
        when(userService.updateUserInfo(any(User.class))).thenReturn(true);

        mockMvc.perform(put("/user/me")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("用户信息更新成功"))
                .andExpect(jsonPath("$.data.nickname").value("新昵称"))
                .andExpect(jsonPath("$.data.signature").value("新签名"));

        verify(userService, times(1)).getUserByUsername("testuser");
        verify(userService, times(1)).updateUserInfo(any(User.class));
    }

    @Test
    @WithMockUser(roles = {"USER"})
    void updateUserInfo_UserNotFound() throws Exception {
        UserController.UpdateUserRequest request = new UserController.UpdateUserRequest();
        request.setNickname("新昵称");

        // Mock 服务层返回 null
        when(userService.getUserByUsername("testuser")).thenReturn(null);

        mockMvc.perform(put("/user/me")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("用户不存在"));

        verify(userService, times(1)).getUserByUsername("testuser");
        verify(userService, never()).updateUserInfo(any(User.class));
    }

    @Test
    @WithMockUser(roles = {"USER"})
    void updateUserInfo_UpdateFailed() throws Exception {
        UserController.UpdateUserRequest request = new UserController.UpdateUserRequest();
        request.setNickname("新昵称");

        // Mock 服务层返回
        when(userService.getUserByUsername("testuser")).thenReturn(testUser);
        when(userService.updateUserInfo(any(User.class))).thenReturn(false);

        mockMvc.perform(put("/user/me")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("更新失败"));

        verify(userService, times(1)).getUserByUsername("testuser");
        verify(userService, times(1)).updateUserInfo(any(User.class));
    }

    @Test
    void getUserStats_Success() throws Exception {
        // 创建模拟统计数据 - 使用 Map 代替 UserStats
        java.util.Map<String, Object> mockStats = new java.util.HashMap<>();
        mockStats.put("uid", 100000001L);
        mockStats.put("followersCount", 100L);
        mockStats.put("followingCount", 50L);
        mockStats.put("videosCount", 20L);
        mockStats.put("likesCount", 500L);

        //when(userService.getUserStats(100000001L)).thenReturn(mockStats);

        mockMvc.perform(get("/user/100000001/stats")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取用户统计信息成功"))
                .andExpect(jsonPath("$.data").exists());

        verify(userService, times(1)).getUserStats(100000001L);
    }

    @Test
    void getUserStats_NotFound() throws Exception {
        when(userService.getUserStats(999999L)).thenReturn(null);

        mockMvc.perform(get("/user/999999/stats")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("用户统计信息不存在"));

        verify(userService, times(1)).getUserStats(999999L);
    }

    @Test
    @WithMockUser(roles = {"USER"})
    void updateUserInfo_PartialUpdate() throws Exception {
        // 只更新部分字段
        UserController.UpdateUserRequest request = new UserController.UpdateUserRequest();
        request.setNickname("新昵称");
        // 其他字段保持 null，应该不被更新

        when(userService.getUserByUsername("testuser")).thenReturn(testUser);
        when(userService.updateUserInfo(any(User.class))).thenReturn(true);

        mockMvc.perform(put("/user/me")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.nickname").value("新昵称"))
                // 验证其他字段保持原值
                .andExpect(jsonPath("$.data.signature").value("这是测试签名"))
                .andExpect(jsonPath("$.data.location").value("北京"));

        verify(userService, times(1)).updateUserInfo(argThat(user ->
            "新昵称".equals(user.getNickname()) &&
            "这是测试签名".equals(user.getSignature()) &&
            "北京".equals(user.getLocation())
        ));
    }
}
