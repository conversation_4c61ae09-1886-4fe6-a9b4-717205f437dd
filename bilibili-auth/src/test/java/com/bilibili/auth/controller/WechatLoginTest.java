package com.bilibili.auth.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.bilibili.auth.dto.SessionRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 微信登录功能测试
 */
@SpringBootTest
public class WechatLoginTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testWechatLogin() throws Exception {
        System.out.println("[DEBUG_LOG] 开始测试微信登录功能");

        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 创建微信登录请求
        SessionRequest request = new SessionRequest();
        request.setType("wechat");
        request.setCode("wx_test_auth_code_123");
        request.setState("test_random_state_456");

        // 设置设备信息
        SessionRequest.DeviceInfo deviceInfo = new SessionRequest.DeviceInfo();
        deviceInfo.setDeviceType("WEB");
        deviceInfo.setDeviceId("test_device_123");
        deviceInfo.setIpAddress("*************");
        deviceInfo.setUserAgent("Mozilla/5.0 (Test Browser)");
        request.setDeviceInfo(deviceInfo);

        String requestJson = objectMapper.writeValueAsString(request);
        System.out.println("[DEBUG_LOG] 请求JSON: " + requestJson);

        // 发送请求
        mockMvc.perform(post("/api/v1/auth/sessions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("登录成功"))
                .andExpect(jsonPath("$.data.access_token").exists())
                .andExpect(jsonPath("$.data.refresh_token").exists())
                .andExpect(jsonPath("$.data.token_type").value("Bearer"))
                .andExpect(jsonPath("$.data.user.username").exists())
                .andExpect(jsonPath("$.data.user.nickname").exists())
                .andExpect(jsonPath("$.data.user.is_new_user").value(true))
                .andDo(result -> {
                    System.out.println("[DEBUG_LOG] 响应状态: " + result.getResponse().getStatus());
                    System.out.println("[DEBUG_LOG] 响应内容: " + result.getResponse().getContentAsString());
                });

        System.out.println("[DEBUG_LOG] 微信登录测试完成");
    }

    @Test
    public void testWechatLoginWithMissingCode() throws Exception {
        System.out.println("[DEBUG_LOG] 开始测试微信登录缺少授权码的情况");

        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 创建缺少授权码的请求
        SessionRequest request = new SessionRequest();
        request.setType("wechat");
        request.setState("test_random_state_456");

        String requestJson = objectMapper.writeValueAsString(request);
        System.out.println("[DEBUG_LOG] 请求JSON: " + requestJson);

        // 发送请求，期望返回错误
        mockMvc.perform(post("/api/v1/auth/sessions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andDo(result -> {
                    System.out.println("[DEBUG_LOG] 响应状态: " + result.getResponse().getStatus());
                    System.out.println("[DEBUG_LOG] 响应内容: " + result.getResponse().getContentAsString());
                });

        System.out.println("[DEBUG_LOG] 微信登录缺少授权码测试完成");
    }

    @Test
    public void testWechatLoginExistingUser() throws Exception {
        System.out.println("[DEBUG_LOG] 开始测试微信登录已存在用户的情况");

        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 创建微信登录请求（使用相同的code，应该找到已存在的用户）
        SessionRequest request = new SessionRequest();
        request.setType("wechat");
        request.setCode("wx_test_auth_code_123"); // 使用相同的code
        request.setState("test_random_state_456");

        String requestJson = objectMapper.writeValueAsString(request);

        // 第二次登录，应该找到已存在的用户
        mockMvc.perform(post("/api/v1/auth/sessions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.user.is_new_user").value(false))
                .andDo(result -> {
                    System.out.println("[DEBUG_LOG] 第二次登录响应: " + result.getResponse().getContentAsString());
                });

        System.out.println("[DEBUG_LOG] 微信登录已存在用户测试完成");
    }
}
