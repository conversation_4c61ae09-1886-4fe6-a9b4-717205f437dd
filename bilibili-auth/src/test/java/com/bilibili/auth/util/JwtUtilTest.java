package com.bilibili.auth.util;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JWT工具类测试
 * 验证JWT密钥长度是否满足HS512要求
 */
@SpringBootTest
public class JwtUtilTest {

    @Autowired
    private JwtUtil jwtUtil;

    @Test
    public void testJwtTokenGeneration() {
        System.out.println("[DEBUG_LOG] 开始测试JWT令牌生成");
        
        try {
            // 测试生成访问令牌
            String accessToken = jwtUtil.generateAccessToken(123456L, "testuser");
            assertNotNull(accessToken, "访问令牌不应为空");
            assertTrue(accessToken.length() > 0, "访问令牌应有内容");
            System.out.println("[DEBUG_LOG] 访问令牌生成成功: " + accessToken.substring(0, Math.min(50, accessToken.length())) + "...");
            
            // 测试生成刷新令牌
            String refreshToken = jwtUtil.generateRefreshToken(123456L);
            assertNotNull(refreshToken, "刷新令牌不应为空");
            assertTrue(refreshToken.length() > 0, "刷新令牌应有内容");
            System.out.println("[DEBUG_LOG] 刷新令牌生成成功: " + refreshToken.substring(0, Math.min(50, refreshToken.length())) + "...");
            
            // 测试令牌验证
            assertTrue(jwtUtil.validateToken(accessToken), "访问令牌应该有效");
            assertTrue(jwtUtil.validateToken(refreshToken), "刷新令牌应该有效");
            
            // 测试从令牌中提取信息
            Long uid = jwtUtil.getUidFromToken(accessToken);
            assertEquals(123456L, uid, "从令牌中提取的UID应该正确");
            
            String username = jwtUtil.getUsernameFromToken(accessToken);
            assertEquals("testuser", username, "从令牌中提取的用户名应该正确");
            
            System.out.println("[DEBUG_LOG] JWT令牌生成和验证测试通过");
            
        } catch (Exception e) {
            System.err.println("[DEBUG_LOG] JWT令牌生成失败: " + e.getMessage());
            e.printStackTrace();
            fail("JWT令牌生成不应该失败: " + e.getMessage());
        }
    }
}