package com.bilibili.auth.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.bilibili.auth.dto.LoginResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Jackson配置测试
 * 验证JSON序列化是否使用snake_case命名
 */
@SpringBootTest
public class JacksonConfigTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testSnakeCaseNaming() throws Exception {
        // 创建测试数据
        LoginResponse.UserInfo userInfo = LoginResponse.UserInfo.builder()
                .uid(123456L)
                .username("testuser")
                .nickname("测试用户")
                .avatar("https://example.com/avatar.jpg")
                .level(5)
                .exp(12500)
                .coins(1000.0)
                .vipType(2)
                .vipStatus(1)
                .vipDueDate(LocalDateTime.now())
                .isVerified(true)
                .verifiedType(1)
                .phone("13800138000")
                .email("<EMAIL>")
                .build();

        LoginResponse response = LoginResponse.builder()
                .accessToken("test-access-token")
                .refreshToken("test-refresh-token")
                .tokenType("Bearer")
                .expiresIn(86400L)
                .userId(123456L)
                .username("testuser")
                .user(userInfo)
                .build();

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(response);
        
        System.out.println("[DEBUG_LOG] Generated JSON: " + json);

        // 验证snake_case命名
        assertTrue(json.contains("access_token"), "应该包含 access_token");
        assertTrue(json.contains("refresh_token"), "应该包含 refresh_token");
        assertTrue(json.contains("token_type"), "应该包含 token_type");
        assertTrue(json.contains("expires_in"), "应该包含 expires_in");
        assertTrue(json.contains("user_id"), "应该包含 user_id");
        assertTrue(json.contains("vip_type"), "应该包含 vip_type");
        assertTrue(json.contains("vip_status"), "应该包含 vip_status");
        assertTrue(json.contains("vip_due_date"), "应该包含 vip_due_date");
        assertTrue(json.contains("is_verified"), "应该包含 is_verified");
        assertTrue(json.contains("verified_type"), "应该包含 verified_type");

        // 验证不包含camelCase命名
        assertTrue(!json.contains("accessToken"), "不应该包含 accessToken");
        assertTrue(!json.contains("refreshToken"), "不应该包含 refreshToken");
        assertTrue(!json.contains("tokenType"), "不应该包含 tokenType");
        assertTrue(!json.contains("expiresIn"), "不应该包含 expiresIn");
        assertTrue(!json.contains("userId"), "不应该包含 userId");
        assertTrue(!json.contains("vipType"), "不应该包含 vipType");
        assertTrue(!json.contains("vipStatus"), "不应该包含 vipStatus");
        assertTrue(!json.contains("vipDueDate"), "不应该包含 vipDueDate");
        assertTrue(!json.contains("isVerified"), "不应该包含 isVerified");
        assertTrue(!json.contains("verifiedType"), "不应该包含 verifiedType");
    }
}