package com.bilibili.auth.security;

import com.bilibili.auth.entity.User;
import com.bilibili.auth.service.AuthService;
import com.bilibili.auth.service.UserLoginDeviceService;
import com.bilibili.auth.util.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT认证过滤器
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;
    private final AuthService authService;
    private final UserLoginDeviceService userLoginDeviceService;

    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain) throws ServletException, IOException {

        try {
            String token = getTokenFromRequest(request);

            if (StringUtils.hasText(token) && jwtUtil.validateToken(token)) {
                // 验证访问令牌
                User user = authService.validateAccessToken(token);

                if (user != null) {
                    // 更新设备最后访问时间
                    String tokenId = jwtUtil.getTokenIdFromToken(token);
                    if (tokenId != null) {
                        userLoginDeviceService.updateDeviceLastAccess(tokenId);
                    }

                    CustomUserDetails userDetails = new CustomUserDetails(user);
                    UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(
                            userDetails,
                            null,
                            userDetails.getAuthorities()
                        );

                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authentication);

                    log.debug("用户认证成功: uid={}, username={}, tokenId={}",
                        user.getUid(), user.getUsername(), tokenId);
                }
            }
        } catch (Exception e) {
            log.error("JWT认证失败", e);
            SecurityContextHolder.clearContext();
        }

        filterChain.doFilter(request, response);
    }

    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();

        // 跳过不需要认证的路径
        return path.startsWith("/auth/") ||
               path.startsWith("/health") ||
               path.startsWith("/swagger-ui") ||
               path.startsWith("/v3/api-docs") ||
               path.startsWith("/actuator") ||
               path.startsWith("/api/v1") ||
               path.equals("/") ||
               // Knife4j 文档相关路径
               path.equals("/doc.html") ||
               path.startsWith("/swagger-resources") ||
               path.startsWith("/webjars");
    }
}
