package com.bilibili.auth.annotation;

import java.lang.annotation.*;

/**
 * VIP权限要求注解
 * 用于标记需要VIP权限的方法或类
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireVip {
    
    /**
     * 是否需要有效的VIP（检查到期时间）
     */
    boolean requireValid() default true;
    
    /**
     * 错误消息
     */
    String message() default "此功能需要大会员权限";
}
