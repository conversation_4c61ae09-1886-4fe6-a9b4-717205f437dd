package com.bilibili.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.auth.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户信息 Mapper 接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted_at IS NULL")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted_at IS NULL")
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     */
    @Select("SELECT * FROM users WHERE phone = #{phone} AND deleted_at IS NULL")
    User findByPhone(@Param("phone") String phone);

    /**
     * 根据UID查询用户
     */
    @Select("SELECT * FROM users WHERE uid = #{uid} AND deleted_at IS NULL")
    User findByUid(@Param("uid") Long uid);

    /**
     * 根据微信OpenId查询用户
     */
    @Select("SELECT * FROM users WHERE wechat_open_id = #{wechatOpenId} AND deleted_at IS NULL")
    User findByWechatOpenId(@Param("wechatOpenId") String wechatOpenId);

    /**
     * 更新用户最后登录信息
     */
    @Update("UPDATE users SET last_login_time = NOW(), last_login_ip = #{ip} WHERE uid = #{uid}")
    int updateLastLoginInfo(@Param("uid") Long uid, @Param("ip") String ip);

    /**
     * 增加用户硬币
     */
    @Update("UPDATE users SET coins = coins + #{amount} WHERE uid = #{uid}")
    int addCoins(@Param("uid") Long uid, @Param("amount") Double amount);

    /**
     * 扣除用户硬币
     */
    @Update("UPDATE users SET coins = coins - #{amount} WHERE uid = #{uid} AND coins >= #{amount}")
    int deductCoins(@Param("uid") Long uid, @Param("amount") Double amount);

    /**
     * 更新用户经验值和等级
     */
    @Update("UPDATE users SET exp = #{exp}, level = #{level} WHERE uid = #{uid}")
    int updateExpAndLevel(@Param("uid") Long uid, @Param("exp") Integer exp, @Param("level") Integer level);

    /**
     * 获取用户数量（按状态）
     */
    @Select("SELECT COUNT(*) FROM users WHERE status = #{status} AND deleted_at IS NULL")
    Long countByStatus(@Param("status") Integer status);

    /**
     * 根据身份证号查询用户
     */
    @Select("SELECT * FROM users WHERE id_card = #{idCard} AND deleted_at IS NULL")
    User findByIdCard(@Param("idCard") String idCard);

    /**
     * 根据真实姓名查询用户
     */
    @Select("SELECT * FROM users WHERE real_name = #{realName} AND deleted_at IS NULL")
    User findByRealName(@Param("realName") String realName);

    /**
     * 更新用户实名认证状态
     */
    @Update("UPDATE users SET real_name_verified = #{verified}, real_name = #{realName}, id_card = #{idCard} WHERE uid = #{uid}")
    int updateRealNameVerification(@Param("uid") Long uid,
                                   @Param("verified") Integer verified,
                                   @Param("realName") String realName,
                                   @Param("idCard") String idCard);

    /**
     * 更新用户学校信息
     */
    @Update("UPDATE users SET school = #{school} WHERE uid = #{uid}")
    int updateSchool(@Param("uid") Long uid, @Param("school") String school);

    /**
     * 更新用户个人标签
     */
    @Update("UPDATE users SET personal_tags = #{personalTags} WHERE uid = #{uid}")
    int updatePersonalTags(@Param("uid") Long uid, @Param("personalTags") String personalTags);

    /**
     * 绑定微信账号
     */
    @Update("UPDATE users SET wechat_open_id = #{wechatOpenId}, wechat_union_id = #{wechatUnionId} WHERE uid = #{uid}")
    int bindWechat(@Param("uid") Long uid,
                   @Param("wechatOpenId") String wechatOpenId,
                   @Param("wechatUnionId") String wechatUnionId);

    /**
     * 解绑微信账号
     */
    @Update("UPDATE users SET wechat_open_id = NULL, wechat_union_id = NULL WHERE uid = #{uid}")
    int unbindWechat(@Param("uid") Long uid);

    /**
     * 统计已实名认证的用户数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE real_name_verified = 1 AND deleted_at IS NULL")
    Long countRealNameVerifiedUsers();

    /**
     * 统计已绑定微信的用户数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE wechat_open_id IS NOT NULL AND deleted_at IS NULL")
    Long countWechatBoundUsers();

    /**
     * 查询信息完整度高的用户（用于推荐等功能）
     */
    @Select("SELECT * FROM users WHERE " +
            "avatar IS NOT NULL AND signature IS NOT NULL AND location IS NOT NULL AND " +
            "birthday IS NOT NULL AND gender IS NOT NULL AND gender != 0 AND " +
            "deleted_at IS NULL ORDER BY created_at DESC LIMIT #{limit}")
    List<User> findUsersWithCompleteProfile(@Param("limit") Integer limit);
}
