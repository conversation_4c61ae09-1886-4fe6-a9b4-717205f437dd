package com.bilibili.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.auth.entity.UserLevel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户等级配置 Mapper 接口
 */
@Mapper
public interface UserLevelMapper extends BaseMapper<UserLevel> {

    /**
     * 根据经验值获取对应等级
     */
    @Select("SELECT * FROM user_levels WHERE min_exp <= #{exp} AND max_exp >= #{exp} LIMIT 1")
    UserLevel findByExp(@Param("exp") Integer exp);

    /**
     * 获取所有等级配置（按等级排序）
     */
    @Select("SELECT * FROM user_levels ORDER BY level ASC")
    List<UserLevel> findAllOrderByLevel();

    /**
     * 根据等级获取配置
     */
    @Select("SELECT * FROM user_levels WHERE level = #{level}")
    UserLevel findByLevel(@Param("level") Integer level);

    /**
     * 获取下一个等级配置
     */
    @Select("SELECT * FROM user_levels WHERE level = #{level} + 1")
    UserLevel findNextLevel(@Param("level") Integer level);

    /**
     * 获取等级范围内的配置
     */
    @Select("SELECT * FROM user_levels WHERE level BETWEEN #{minLevel} AND #{maxLevel} ORDER BY level ASC")
    List<UserLevel> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);
}
