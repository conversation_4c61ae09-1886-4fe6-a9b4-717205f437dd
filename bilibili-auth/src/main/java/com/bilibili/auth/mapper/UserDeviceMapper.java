package com.bilibili.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.auth.entity.UserDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户设备信息 Mapper 接口
 */
@Mapper
public interface UserDeviceMapper extends BaseMapper<UserDevice> {

    /**
     * 根据用户ID和设备ID查询设备
     */
    @Select("SELECT * FROM user_devices WHERE uid = #{uid} AND device_id = #{deviceId}")
    UserDevice findByUidAndDeviceId(@Param("uid") Long uid, @Param("deviceId") String deviceId);

    /**
     * 获取用户的所有设备
     */
    @Select("SELECT * FROM user_devices WHERE uid = #{uid} AND status = 1 ORDER BY last_login_time DESC")
    List<UserDevice> findByUid(@Param("uid") Long uid);

    /**
     * 获取用户的信任设备
     */
    @Select("SELECT * FROM user_devices WHERE uid = #{uid} AND is_trusted = 1 AND status = 1")
    List<UserDevice> findTrustedDevicesByUid(@Param("uid") Long uid);

    /**
     * 更新设备最后登录信息
     */
    @Update("UPDATE user_devices SET last_login_time = NOW(), last_login_ip = #{ip}, login_count = login_count + 1 WHERE uid = #{uid} AND device_id = #{deviceId}")
    int updateLastLoginInfo(@Param("uid") Long uid, @Param("deviceId") String deviceId, @Param("ip") String ip);

    /**
     * 设置设备为信任设备
     */
    @Update("UPDATE user_devices SET is_trusted = #{trusted} WHERE uid = #{uid} AND device_id = #{deviceId}")
    int updateTrustedStatus(@Param("uid") Long uid, @Param("deviceId") String deviceId, @Param("trusted") Boolean trusted);

    /**
     * 禁用设备
     */
    @Update("UPDATE user_devices SET status = 0 WHERE uid = #{uid} AND device_id = #{deviceId}")
    int disableDevice(@Param("uid") Long uid, @Param("deviceId") String deviceId);

    /**
     * 删除用户的所有设备（注销时使用）
     */
    @Update("UPDATE user_devices SET status = 0 WHERE uid = #{uid}")
    int disableAllDevices(@Param("uid") Long uid);
}
