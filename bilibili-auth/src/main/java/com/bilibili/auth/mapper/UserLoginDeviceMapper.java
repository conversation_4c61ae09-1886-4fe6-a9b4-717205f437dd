package com.bilibili.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.auth.entity.UserLoginDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户登录设备Mapper接口
 */
@Mapper
public interface UserLoginDeviceMapper extends BaseMapper<UserLoginDevice> {

    /**
     * 根据用户ID和设备指纹查询设备
     */
    @Select("SELECT * FROM user_login_devices WHERE uid = #{uid} AND device_fingerprint = #{deviceFingerprint} AND status = 1")
    UserLoginDevice findByUidAndDeviceFingerprint(@Param("uid") Long uid, @Param("deviceFingerprint") String deviceFingerprint);

    /**
     * 查询用户的所有在线设备，按最后访问时间排序
     */
    @Select("SELECT * FROM user_login_devices WHERE uid = #{uid} AND status = 1 ORDER BY last_access_time DESC")
    List<UserLoginDevice> findOnlineDevicesByUid(@Param("uid") Long uid);

    /**
     * 查询用户的在线设备数量
     */
    @Select("SELECT COUNT(*) FROM user_login_devices WHERE uid = #{uid} AND status = 1")
    int countOnlineDevicesByUid(@Param("uid") Long uid);

    /**
     * 查询用户最久未使用的设备（LRU）
     */
    @Select("SELECT * FROM user_login_devices WHERE uid = #{uid} AND status = 1 ORDER BY last_access_time ASC LIMIT #{limit}")
    List<UserLoginDevice> findLeastRecentlyUsedDevices(@Param("uid") Long uid, @Param("limit") int limit);

    /**
     * 批量下线设备
     */
    @Update("UPDATE user_login_devices SET status = 0, is_online = false, updated_at = NOW() WHERE id IN (${deviceIds})")
    int batchOfflineDevices(@Param("deviceIds") String deviceIds);

    /**
     * 根据Token ID查询设备
     */
    @Select("SELECT * FROM user_login_devices WHERE token_id = #{tokenId} AND status = 1")
    UserLoginDevice findByTokenId(@Param("tokenId") String tokenId);

    /**
     * 更新设备的最后访问时间
     */
    @Update("UPDATE user_login_devices SET last_access_time = #{lastAccessTime}, login_count = login_count + 1, updated_at = NOW() WHERE id = #{id}")
    int updateLastAccessTime(@Param("id") Long id, @Param("lastAccessTime") LocalDateTime lastAccessTime);

    /**
     * 下线指定Token的设备
     */
    @Update("UPDATE user_login_devices SET status = 0, is_online = false, updated_at = NOW() WHERE token_id = #{tokenId}")
    int offlineByTokenId(@Param("tokenId") String tokenId);

    /**
     * 下线用户的所有设备
     */
    @Update("UPDATE user_login_devices SET status = 0, is_online = false, updated_at = NOW() WHERE uid = #{uid}")
    int offlineAllDevicesByUid(@Param("uid") Long uid);
}
