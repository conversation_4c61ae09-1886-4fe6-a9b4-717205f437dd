package com.bilibili.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.auth.entity.UserStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 用户统计信息 Mapper 接口
 */
@Mapper
public interface UserStatsMapper extends BaseMapper<UserStats> {

    /**
     * 根据UID查询用户统计
     */
    @Select("SELECT * FROM user_stats WHERE uid = #{uid}")
    UserStats findByUid(@Param("uid") Long uid);

    /**
     * 增加关注数
     */
    @Update("UPDATE user_stats SET following_count = following_count + 1 WHERE uid = #{uid}")
    int incrementFollowingCount(@Param("uid") Long uid);

    /**
     * 减少关注数
     */
    @Update("UPDATE user_stats SET following_count = following_count - 1 WHERE uid = #{uid} AND following_count > 0")
    int decrementFollowingCount(@Param("uid") Long uid);

    /**
     * 增加粉丝数
     */
    @Update("UPDATE user_stats SET follower_count = follower_count + 1 WHERE uid = #{uid}")
    int incrementFollowerCount(@Param("uid") Long uid);

    /**
     * 减少粉丝数
     */
    @Update("UPDATE user_stats SET follower_count = follower_count - 1 WHERE uid = #{uid} AND follower_count > 0")
    int decrementFollowerCount(@Param("uid") Long uid);

    /**
     * 增加视频数
     */
    @Update("UPDATE user_stats SET video_count = video_count + 1 WHERE uid = #{uid}")
    int incrementVideoCount(@Param("uid") Long uid);

    /**
     * 增加播放数
     */
    @Update("UPDATE user_stats SET view_count = view_count + #{count} WHERE uid = #{uid}")
    int addViewCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加点赞数
     */
    @Update("UPDATE user_stats SET like_count = like_count + #{count} WHERE uid = #{uid}")
    int addLikeCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加投币数
     */
    @Update("UPDATE user_stats SET coin_count = coin_count + #{count} WHERE uid = #{uid}")
    int addCoinCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加收藏数
     */
    @Update("UPDATE user_stats SET favorite_count = favorite_count + #{count} WHERE uid = #{uid}")
    int addFavoriteCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加分享数
     */
    @Update("UPDATE user_stats SET share_count = share_count + #{count} WHERE uid = #{uid}")
    int addShareCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加评论数
     */
    @Update("UPDATE user_stats SET comment_count = comment_count + #{count} WHERE uid = #{uid}")
    int addCommentCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加弹幕数
     */
    @Update("UPDATE user_stats SET danmu_count = danmu_count + #{count} WHERE uid = #{uid}")
    int addDanmuCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加直播时长
     */
    @Update("UPDATE user_stats SET live_time = live_time + #{seconds} WHERE uid = #{uid}")
    int addLiveTime(@Param("uid") Long uid, @Param("seconds") Long seconds);
}
