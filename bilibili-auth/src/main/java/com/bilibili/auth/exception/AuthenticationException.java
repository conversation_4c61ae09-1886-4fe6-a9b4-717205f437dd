package com.bilibili.auth.exception;

/**
 * 认证异常类
 */
public class AuthenticationException extends RuntimeException {

    private final Integer code;

    public AuthenticationException(String message) {
        this(40001, message);
    }

    public AuthenticationException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public AuthenticationException(String message, Throwable cause) {
        this(40001, message, cause);
    }

    public AuthenticationException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }
}
