package com.bilibili.auth.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户设备信息实体类
 * 用于多端登录管理和设备识别
 */
@Data
@Entity
@Table(name = "user_devices", indexes = {
    @Index(name = "idx_uid", columnList = "uid"),
    @Index(name = "idx_device_id", columnList = "deviceId")
})
@TableName("user_devices")
@Schema(description = "用户设备信息")
public class UserDevice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    @Schema(description = "记录ID")
    private Long id;

    @Column(nullable = false)
    @Schema(description = "用户UID", example = "123456789")
    private Long uid;

    @Column(nullable = false, length = 100)
    @Schema(description = "设备ID", example = "device_abc123")
    private String deviceId;

    @Schema(description = "设备类型", example = "1", allowableValues = {"1", "2", "3", "4"})
    private Integer deviceType = 0; // 1手机 2平板 3电脑 4TV

    @Column(length = 100)
    @Schema(description = "设备名称", example = "iPhone 15")
    private String deviceName;

    @Column(length = 50)
    @Schema(description = "设备品牌", example = "Apple")
    private String deviceBrand;

    @Column(length = 100)
    @Schema(description = "设备型号", example = "iPhone15,2")
    private String deviceModel;

    @Schema(description = "系统类型", example = "1", allowableValues = {"1", "2", "3", "4", "5"})
    private Integer osType = 0; // 1iOS 2Android 3Windows 4macOS 5Linux

    @Column(length = 50)
    @Schema(description = "系统版本", example = "iOS 17.0")
    private String osVersion;

    @Column(length = 50)
    @Schema(description = "应用版本", example = "1.0.0")
    private String appVersion;

    @Column(columnDefinition = "TEXT")
    @Schema(description = "用户代理")
    private String userAgent;

    @Column(length = 20)
    @Schema(description = "屏幕分辨率", example = "1920x1080")
    private String screenResolution;

    @Schema(description = "网络类型", example = "1", allowableValues = {"1", "2", "3"})
    private Integer networkType = 0; // 1WiFi 2移动网络 3有线

    @Schema(description = "是否信任设备", example = "false")
    private Boolean isTrusted = false;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Column(length = 45)
    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "登录次数", example = "10")
    private Integer loginCount = 0;

    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status = 1; // 0禁用 1正常

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
