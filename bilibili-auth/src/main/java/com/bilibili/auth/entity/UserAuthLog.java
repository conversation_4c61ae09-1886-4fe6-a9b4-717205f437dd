package com.bilibili.auth.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户认证记录实体类
 * 用于登录日志和安全审计
 */
@Data
@Entity
@Table(name = "user_auth_logs", indexes = {
    @Index(name = "idx_uid", columnList = "uid"),
    @Index(name = "idx_created_at", columnList = "createdAt")
})
@TableName("user_auth_logs")
@Schema(description = "用户认证记录")
public class UserAuthLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    @Schema(description = "记录ID")
    private Long id;

    @Column(nullable = false)
    @Schema(description = "用户UID", example = "123456789")
    private Long uid;

    @Schema(description = "认证类型", example = "1", allowableValues = {"1", "2", "3", "4"})
    private Integer authType; // 1密码 2短信 3邮箱 4二维码

    @Column(columnDefinition = "TEXT")
    @Schema(description = "设备信息(JSON格式)")
    private String deviceInfo;

    @Column(nullable = false, length = 45)
    @Schema(description = "IP地址", example = "***********")
    private String ipAddress;

    @Column(columnDefinition = "TEXT")
    @Schema(description = "用户代理")
    private String userAgent;

    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status; // 0失败 1成功

    @Column(columnDefinition = "TEXT")
    @Schema(description = "失败原因")
    private String failureReason;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
