package com.bilibili.auth.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户登录设备管理实体类
 * 用于管理用户的登录设备，支持LRU机制限制最大设备数量
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_login_devices", indexes = {
    @Index(name = "idx_uid", columnList = "uid"),
    @Index(name = "idx_device_fingerprint", columnList = "deviceFingerprint"),
    @Index(name = "idx_last_access_time", columnList = "lastAccessTime")
})
@TableName("user_login_devices")
@Schema(description = "用户登录设备管理")
public class UserLoginDevice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    @Schema(description = "记录ID")
    private Long id;

    @Column(nullable = false)
    @Schema(description = "用户UID", example = "123456789")
    private Long uid;

    @Column(nullable = false, length = 128)
    @Schema(description = "设备指纹", example = "device_fingerprint_abc123")
    private String deviceFingerprint;

    @Column(length = 100)
    @Schema(description = "设备名称", example = "Chrome浏览器 - Windows")
    private String deviceName;

    @Schema(description = "设备类型", example = "1", allowableValues = {"1", "2", "3", "4", "5"})
    @Builder.Default
    private Integer deviceType = 1; // 1浏览器 2手机APP 3平板APP 4桌面应用 5其他

    @Column(length = 50)
    @Schema(description = "操作系统", example = "Windows 10")
    private String operatingSystem;

    @Column(length = 100)
    @Schema(description = "浏览器/应用", example = "Chrome 120.0")
    private String browserOrApp;

    @Column(length = 45)
    @Schema(description = "IP地址")
    private String ipAddress;

    @Column(length = 100)
    @Schema(description = "地理位置", example = "北京市朝阳区")
    private String location;

    @Column(columnDefinition = "TEXT")
    @Schema(description = "User Agent")
    private String userAgent;

    @Column(length = 64)
    @Schema(description = "当前JWT Token的唯一标识")
    private String tokenId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "首次登录时间")
    private LocalDateTime firstLoginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后访问时间")
    private LocalDateTime lastAccessTime;

    @Schema(description = "登录次数", example = "10")
    @Builder.Default
    private Integer loginCount = 1;

    @Schema(description = "是否在线", example = "true")
    @Builder.Default
    private Boolean isOnline = true;

    @Schema(description = "是否信任设备", example = "false")
    @Builder.Default
    private Boolean isTrusted = false;

    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    @Builder.Default
    private Integer status = 1; // 0已下线 1在线

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    /**
     * 更新最后访问时间
     */
    public void updateLastAccessTime() {
        this.lastAccessTime = LocalDateTime.now();
        this.loginCount++;
    }

    /**
     * 设置设备下线
     */
    public void setOffline() {
        this.isOnline = false;
        this.status = 0;
    }

    /**
     * 设置设备在线
     */
    public void setOnline() {
        this.isOnline = true;
        this.status = 1;
        this.lastAccessTime = LocalDateTime.now();
    }
}
