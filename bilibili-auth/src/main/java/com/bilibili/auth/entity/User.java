package com.bilibili.auth.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.bilibili.auth.enums.UserPermission;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 用户基础信息实体类
 * 实现Spring Security的UserDetails接口用于认证
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_uid", columnList = "uid"),
    @Index(name = "idx_username", columnList = "username"),
    @Index(name = "idx_email", columnList = "email"),
    @Index(name = "idx_phone", columnList = "phone")
})
@TableName("users")
@Schema(description = "用户信息")
public class User implements UserDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    @Schema(description = "用户ID")
    private Long id;

    @Column(unique = true, nullable = false)
    @Schema(description = "用户唯一标识", example = "123456789")
    private Long uid;

    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_]{4,20}$", message = "用户名只能包含字母、数字和下划线，长度4-20位")
    @Column(unique = true, nullable = false, length = 50)
    @Schema(description = "用户名", example = "user123")
    private String username;

    @Email(message = "邮箱格式不正确")
    @Column(unique = true, length = 100)
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Column(unique = true, length = 20)
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @JsonIgnore
    @Column(nullable = false)
    @TableField("password_hash")
    @Schema(description = "密码哈希", hidden = true)
    private String passwordHash;

    @JsonIgnore
    @Column(nullable = false, length = 32)
    @TableField("salt")
    @Schema(description = "密码盐值", hidden = true)
    private String salt;

    @NotBlank(message = "昵称不能为空")
    @Column(nullable = false, length = 50)
    @Schema(description = "昵称", example = "小明")
    private String nickname;

    @Column(length = 255)
    @Schema(description = "头像URL")
    private String avatar;

    @Column(length = 255)
    @Schema(description = "横幅URL")
    private String banner;

    @Schema(description = "性别", example = "1", allowableValues = {"0", "1", "2"})
    @Builder.Default
    private Integer gender = 0; // 0未知 1男 2女

    @Schema(description = "生日")
    private LocalDate birthday;

    @Column(length = 255)
    @Schema(description = "个人签名")
    private String signature;

    @Column(length = 100)
    @Schema(description = "位置")
    private String location;

    @Schema(description = "用户等级", example = "5")
    @Builder.Default
    private Integer level = 0;

    @Schema(description = "经验值", example = "12500")
    @Builder.Default
    private Integer exp = 0;

    @Schema(description = "硬币数量", example = "1000.0")
    @Builder.Default
    private Double coins = 0.0;

    @Schema(description = "VIP类型", example = "2", allowableValues = {"0", "1", "2"})
    @Builder.Default
    private Integer vipType = 0; // 0普通 1月会员 2年会员

    @Schema(description = "VIP状态", example = "1", allowableValues = {"0", "1"})
    @Builder.Default
    private Integer vipStatus = 0; // 0无效 1有效

    @Schema(description = "VIP到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime vipDueDate;

    @Schema(description = "状态", example = "1", allowableValues = {"0", "1", "2"})
    @Builder.Default
    private Integer status = 1; // 0禁用 1正常 2封禁

    @Schema(description = "是否认证", example = "false")
    @Builder.Default
    private Boolean isVerified = false;

    @Schema(description = "认证类型", example = "1", allowableValues = {"0", "1", "2"})
    @Builder.Default
    private Integer verifiedType = 0; // 0无 1个人 2机构

    @Column(length = 255)
    @Schema(description = "认证信息")
    private String verifiedInfo;

    // 微信登录相关字段
    @Column(unique = true, length = 100)
    @TableField("wechat_open_id")
    @Schema(description = "微信OpenId")
    private String wechatOpenId;

    @Column(length = 100)
    @TableField("wechat_union_id")
    @Schema(description = "微信UnionId")
    private String wechatUnionId;

    @Schema(description = "是否已绑定手机号", example = "false")
    @TableField("phone_bound")
    @Builder.Default
    private Boolean phoneBound = false;

    @Column(length = 100)
    @Schema(description = "学校信息", example = "清华大学")
    private String school;

    @TableField("personal_tags")
    @Schema(description = "个人标签", example = "[\"技术宅\", \"游戏爱好者\"]")
    private String personalTags;

    @Column(length = 50)
    @TableField("real_name")
    @Schema(description = "真实姓名", example = "张三")
    private String realName;

    @Column(length = 18)
    @TableField("id_card")
    @Schema(description = "身份证号", example = "110101199001011234")
    private String idCard;

    @Schema(description = "实名认证状态", example = "0", allowableValues = {"0", "1"})
    @TableField("real_name_verified")
    @Builder.Default
    private Integer realNameVerified = 0; // 0未认证 1已认证

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Column(length = 45)
    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "删除时间")
    private LocalDateTime deletedAt;

    @Schema(description = "逻辑删除标记")
    @Builder.Default
    private Integer deleted = 0;

    // Spring Security UserDetails 接口实现
    @Override
    @JsonIgnore
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 基于用户权限分配角色
        UserPermission permission = getUserPermission();
        return List.of(
            new SimpleGrantedAuthority("ROLE_USER"),
            new SimpleGrantedAuthority("ROLE_" + permission.getCode()),
            new SimpleGrantedAuthority("LEVEL_" + level)
        );
    }

    @Override
    @JsonIgnore
    public String getPassword() {
        return passwordHash;
    }

    @Override
    @JsonIgnore
    public boolean isAccountNonExpired() {
        return status != 0;
    }

    @Override
    @JsonIgnore
    public boolean isAccountNonLocked() {
        return status != 2;
    }

    @Override
    @JsonIgnore
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    @JsonIgnore
    public boolean isEnabled() {
        return status == 1;
    }

    /**
     * 检查是否为VIP用户
     */
    public boolean isVip() {
        return vipStatus == 1 && vipDueDate != null && vipDueDate.isAfter(LocalDateTime.now());
    }

    /**
     * 检查是否为认证用户
     */
    public boolean isVerifiedUser() {
        return isVerified && verifiedType > 0;
    }

    /**
     * 获取用户显示名称
     */
    public String getDisplayName() {
        return nickname != null ? nickname : username;
    }

    /**
     * 获取用户权限
     * 简化权限管理，只分为大会员和非大会员两种
     */
    public UserPermission getUserPermission() {
        return UserPermission.fromVipStatus(vipStatus, vipType);
    }

    /**
     * 检查是否为大会员
     */
    public boolean isVipUser() {
        return getUserPermission().isVip();
    }

    /**
     * 检查是否为普通用户
     */
    public boolean isNormalUser() {
        return getUserPermission().isNormal();
    }

    /**
     * 检查VIP是否有效（包含到期时间检查）
     */
    public boolean isVipValid() {
        return isVip() && vipDueDate != null && vipDueDate.isAfter(LocalDateTime.now());
    }

    /**
     * 检查是否已绑定微信
     */
    public boolean isWechatBound() {
        return wechatOpenId != null && !wechatOpenId.trim().isEmpty();
    }

    /**
     * 检查是否已实名认证
     */
    public boolean isRealNameVerified() {
        return realNameVerified != null && realNameVerified == 1;
    }

    /**
     * 检查是否有学校信息
     */
    public boolean hasSchoolInfo() {
        return school != null && !school.trim().isEmpty();
    }

    /**
     * 检查是否有个人标签
     */
    public boolean hasPersonalTags() {
        return personalTags != null && !personalTags.trim().isEmpty();
    }

    /**
     * 获取脱敏后的身份证号
     */
    public String getMaskedIdCard() {
        if (idCard == null || idCard.length() != 18) {
            return idCard;
        }
        return idCard.substring(0, 6) + "********" + idCard.substring(14);
    }

    /**
     * 获取脱敏后的真实姓名
     */
    public String getMaskedRealName() {
        if (realName == null || realName.length() <= 1) {
            return realName;
        }
        if (realName.length() == 2) {
            return realName.charAt(0) + "*";
        }
        return realName.charAt(0) + "*".repeat(realName.length() - 2) + realName.charAt(realName.length() - 1);
    }

    /**
     * 检查用户信息完整度
     */
    public double getProfileCompleteness() {
        int totalFields = 10; // 总共检查的字段数
        int completedFields = 0;

        if (avatar != null && !avatar.trim().isEmpty()) completedFields++;
        if (signature != null && !signature.trim().isEmpty()) completedFields++;
        if (location != null && !location.trim().isEmpty()) completedFields++;
        if (birthday != null) completedFields++;
        if (gender != null && gender != 0) completedFields++;
        if (isWechatBound()) completedFields++;
        if (phoneBound != null && phoneBound) completedFields++;
        if (hasSchoolInfo()) completedFields++;
        if (hasPersonalTags()) completedFields++;
        if (isRealNameVerified()) completedFields++;

        return (double) completedFields / totalFields * 100;
    }
}
