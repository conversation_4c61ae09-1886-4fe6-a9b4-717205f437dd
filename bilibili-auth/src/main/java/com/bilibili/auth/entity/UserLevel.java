package com.bilibili.auth.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户等级实体类
 * 用于用户权限和等级管理
 */
@Data
@Entity
@Table(name = "user_levels", indexes = {
    @Index(name = "idx_uid", columnList = "uid"),
    @Index(name = "idx_level", columnList = "level")
})
@TableName("user_levels")
@Schema(description = "用户等级信息")
public class UserLevel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    @Schema(description = "记录ID")
    private Long id;

    @Column(nullable = false)
    @Schema(description = "用户UID", example = "123456789")
    private Long uid;

    @Column(nullable = false)
    @Schema(description = "用户等级", example = "1")
    private Integer level;

    @Column(nullable = false)
    @Schema(description = "等级名称", example = "普通用户")
    private String levelName;

    @Column(nullable = false)
    @Schema(description = "经验值", example = "100")
    private Integer exp;

    @Column(nullable = false)
    @Schema(description = "下一级所需经验值", example = "200")
    private Integer nextLevelExp;

    @Column(columnDefinition = "TEXT")
    @Schema(description = "权限列表(JSON格式)", example = "[\"read\", \"write\"]")
    private String privileges;

    @Column(columnDefinition = "TEXT")
    @Schema(description = "等级描述")
    private String description;

    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status; // 0停用 1启用

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
