package com.bilibili.auth.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户统计信息实体类
 */
@Data
@Entity
@Table(name = "user_stats")
@TableName("user_stats")
@Schema(description = "用户统计信息")
public class UserStats {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    @Schema(description = "记录ID")
    private Long id;

    @Column(nullable = false, unique = true)
    @Schema(description = "用户UID", example = "123456789")
    private Long uid;

    @Schema(description = "关注数", example = "100")
    private Integer followingCount = 0;

    @Schema(description = "粉丝数", example = "5000")
    private Integer followerCount = 0;

    @Schema(description = "视频数", example = "50")
    private Integer videoCount = 0;

    @Schema(description = "总播放数", example = "100000")
    private Long viewCount = 0L;

    @Schema(description = "总点赞数", example = "25000")
    private Long likeCount = 0L;

    @Schema(description = "总投币数", example = "15000")
    private Long coinCount = 0L;

    @Schema(description = "总收藏数", example = "20000")
    private Long favoriteCount = 0L;

    @Schema(description = "总分享数", example = "5000")
    private Long shareCount = 0L;

    @Schema(description = "总评论数", example = "10000")
    private Long commentCount = 0L;

    @Schema(description = "总弹幕数", example = "50000")
    private Long danmuCount = 0L;

    @Schema(description = "直播时长(秒)", example = "3600")
    private Long liveTime = 0L;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
