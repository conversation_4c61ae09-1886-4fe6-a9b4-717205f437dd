package com.bilibili.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 用户登录请求DTO
 */
@Data
@Schema(description = "用户登录请求")
public class LoginRequest {

    @NotBlank(message = "登录账号不能为空")
    @Schema(description = "登录账号", example = "<EMAIL>")
    private String account;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度为6-20个字符")
    @Schema(description = "密码", example = "password123")
    private String password;

    @Schema(description = "是否记住登录", example = "true")
    private Boolean remember = false;

    @Schema(description = "设备信息")
    private DeviceInfo deviceInfo;

    @Data
    @Schema(description = "设备信息")
    public static class DeviceInfo {
        @Schema(description = "平台", example = "web")
        private String platform;

        @Schema(description = "设备ID", example = "device_abc123")
        private String deviceId;

        @Schema(description = "用户代理", example = "Mozilla/5.0...")
        private String userAgent;

        @Schema(description = "设备类型", example = "browser", allowableValues = {"browser", "mobile", "tablet", "desktop", "other"})
        private String deviceType;

        @Schema(description = "设备名称", example = "Chrome Browser")
        private String deviceName;

        @Schema(description = "设备品牌", example = "Google")
        private String deviceBrand;

        @Schema(description = "设备型号", example = "Chrome/120.0.0.0")
        private String deviceModel;

        @Schema(description = "系统类型", example = "windows", allowableValues = {"windows", "macos", "linux", "android", "ios", "other"})
        private String osType;

        @Schema(description = "系统版本", example = "Windows 11")
        private String osVersion;

        @Schema(description = "应用版本", example = "1.0.0")
        private String appVersion;

        @Schema(description = "屏幕分辨率", example = "1920x1080")
        private String screenResolution;

        @Schema(description = "网络类型", example = "wifi", allowableValues = {"wifi", "mobile", "ethernet", "other"})
        private String networkType;
    }
}
