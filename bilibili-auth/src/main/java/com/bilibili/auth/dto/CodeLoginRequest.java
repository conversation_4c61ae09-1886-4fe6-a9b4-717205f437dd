package com.bilibili.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 验证码登录请求DTO
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@Schema(description = "验证码登录请求")
public class CodeLoginRequest {

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @NotBlank(message = "验证码不能为空")
    @Size(min = 4, max = 8, message = "验证码长度必须在4-8位之间")
    @Schema(description = "验证码", example = "123456")
    private String code;

    @Schema(description = "设备信息")
    private DeviceInfo deviceInfo;

    @Data
    @Schema(description = "设备信息")
    public static class DeviceInfo {
        @Schema(description = "设备类型", example = "WEB")
        private String deviceType;

        @Schema(description = "设备标识", example = "Chrome-Win10")
        private String deviceId;

        @Schema(description = "IP地址", example = "*************")
        private String ipAddress;

        @Schema(description = "用户代理", example = "Mozilla/5.0...")
        private String userAgent;
    }
}
