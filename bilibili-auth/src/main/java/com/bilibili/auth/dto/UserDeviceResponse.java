package com.bilibili.auth.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户设备信息响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户设备信息响应")
public class UserDeviceResponse {

    @Schema(description = "设备ID")
    private Long id;

    @Schema(description = "设备指纹")
    private String deviceFingerprint;

    @Schema(description = "设备名称", example = "Chrome浏览器 - Windows")
    private String deviceName;

    @Schema(description = "设备类型", example = "1", allowableValues = {"1", "2", "3", "4", "5"})
    private Integer deviceType;

    @Schema(description = "设备类型名称")
    private String deviceTypeName;

    @Schema(description = "操作系统", example = "Windows 10")
    private String operatingSystem;

    @Schema(description = "浏览器/应用", example = "Chrome 120.0")
    private String browserOrApp;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "地理位置", example = "北京市朝阳区")
    private String location;

    @Schema(description = "Token ID")
    private String tokenId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "首次登录时间")
    private LocalDateTime firstLoginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后访问时间")
    private LocalDateTime lastAccessTime;

    @Schema(description = "登录次数", example = "10")
    private Integer loginCount;

    @Schema(description = "是否在线", example = "true")
    private Boolean isOnline;

    @Schema(description = "是否信任设备", example = "false")
    private Boolean isTrusted;

    @Schema(description = "是否为当前设备", example = "true")
    private Boolean isCurrent;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 获取设备类型名称
     */
    public String getDeviceTypeName() {
        if (deviceType == null) {
            return "未知设备";
        }
        
        return switch (deviceType) {
            case 1 -> "浏览器";
            case 2 -> "手机APP";
            case 3 -> "平板APP";
            case 4 -> "桌面应用";
            case 5 -> "其他";
            default -> "未知设备";
        };
    }

    /**
     * 获取脱敏的IP地址
     */
    public String getMaskedIpAddress() {
        if (ipAddress == null || ipAddress.isEmpty()) {
            return "未知";
        }
        
        String[] parts = ipAddress.split("\\.");
        if (parts.length == 4) {
            return parts[0] + "." + parts[1] + ".***." + parts[3];
        }
        
        return ipAddress.substring(0, Math.min(ipAddress.length(), 8)) + "***";
    }
}
