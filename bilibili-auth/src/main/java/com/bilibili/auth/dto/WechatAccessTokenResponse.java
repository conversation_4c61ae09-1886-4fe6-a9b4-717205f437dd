package com.bilibili.auth.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 微信访问令牌响应DTO
 *
 * <AUTHOR>
 * @since 2024-07-14
 */
@Data
public class WechatAccessTokenResponse {

    /**
     * 访问令牌
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * 访问令牌过期时间（秒）
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;

    /**
     * 刷新令牌
     */
    @JsonProperty("refresh_token")
    private String refreshToken;

    /**
     * 用户唯一标识
     */
    @JsonProperty("openid")
    private String openid;

    /**
     * 授权作用域
     */
    private String scope;

    /**
     * 用户统一标识（针对一个微信开放平台帐号下的应用）
     */
    @JsonProperty("unionid")
    private String unionid;

    /**
     * 错误码
     */
    @JsonProperty("errcode")
    private Integer errcode;

    /**
     * 错误信息
     */
    @JsonProperty("errmsg")
    private String errmsg;

    /**
     * 检查响应是否成功
     */
    public boolean isSuccess() {
        return errcode == null || errcode == 0;
    }
}