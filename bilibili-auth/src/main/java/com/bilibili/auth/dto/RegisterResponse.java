package com.bilibili.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 用户注册响应DTO
 */
@Data
@Builder
@Schema(description = "用户注册响应")
public class RegisterResponse {

    @Schema(description = "注册是否成功", example = "true")
    private Boolean success;

    @Schema(description = "响应消息", example = "注册成功")
    private String message;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "错误代码", example = "USER_EXISTS")
    private String errorCode;
}
