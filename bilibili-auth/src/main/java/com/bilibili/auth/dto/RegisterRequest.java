package com.bilibili.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 用户注册请求DTO
 */
@Data
@Schema(description = "用户注册请求")
public class RegisterRequest {

    @NotBlank(message = "注册类型不能为空")
    @Schema(description = "注册类型", example = "phone", allowableValues = {"phone", "email"})
    private String type;

    @Schema(description = "用户名", example = "user123")
    private String username;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @NotBlank(message = "验证码不能为空")
    @Size(min = 4, max = 6, message = "验证码长度为4-6位")
    @Schema(description = "验证码", example = "123456")
    private String code;

    @NotBlank(message = "昵称不能为空")
    @Size(min = 2, max = 20, message = "昵称长度为2-20个字符")
    @Schema(description = "昵称", example = "小明")
    private String nickname;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度为6-20个字符")
    @Schema(description = "密码", example = "password123")
    private String password;

    @Schema(description = "设备信息")
    private DeviceInfo deviceInfo;

    @Data
    @Schema(description = "设备信息")
    public static class DeviceInfo {
        @Schema(description = "平台", example = "web")
        private String platform;

        @Schema(description = "设备ID", example = "device_abc123")
        private String deviceId;

        @Schema(description = "用户代理", example = "Mozilla/5.0...")
        private String userAgent;

        @Schema(description = "设备类型", example = "1")
        private Integer deviceType;

        @Schema(description = "设备名称", example = "Chrome Browser")
        private String deviceName;

        @Schema(description = "设备品牌", example = "Google")
        private String deviceBrand;

        @Schema(description = "设备型号", example = "Chrome/120.0.0.0")
        private String deviceModel;

        @Schema(description = "系统类型", example = "3")
        private Integer osType;

        @Schema(description = "系统版本", example = "Windows 11")
        private String osVersion;

        @Schema(description = "应用版本", example = "1.0.0")
        private String appVersion;

        @Schema(description = "屏幕分辨率", example = "1920x1080")
        private String screenResolution;

        @Schema(description = "网络类型", example = "1")
        private Integer networkType;
    }
}
