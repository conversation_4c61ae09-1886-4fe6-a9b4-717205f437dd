package com.bilibili.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 会话创建请求DTO (统一登录接口)
 *
 * <AUTHOR>
 * @since 2024-07-14
 */
@Data
@Schema(description = "会话创建请求")
public class SessionRequest {

    @NotBlank(message = "登录类型不能为空")
    @Schema(description = "登录类型", example = "wechat", allowableValues = {"phone", "wechat", "email"})
    private String type;

    // 微信登录字段
    @Schema(description = "微信授权码 (type=wechat时必填)", example = "wx_auth_code")
    private String code;

    @Schema(description = "状态参数 (type=wechat时必填)", example = "random_state")
    private String state;

    // 手机号登录字段
    @Schema(description = "手机号 (type=phone时必填)", example = "13800138000")
    private String phone;

    @Schema(description = "验证码 (type=phone时必填)", example = "123456")
    private String verificationCode;

    // 邮箱登录字段
    @Schema(description = "邮箱地址 (type=email时必填)", example = "<EMAIL>")
    private String email;

    @Schema(description = "设备信息")
    private DeviceInfo deviceInfo;

    @Data
    @Schema(description = "设备信息")
    public static class DeviceInfo {
        @Schema(description = "设备类型", example = "WEB")
        private String deviceType;

        @Schema(description = "设备标识", example = "Chrome-Win10")
        private String deviceId;

        @Schema(description = "IP地址", example = "*************")
        private String ipAddress;

        @Schema(description = "用户代理", example = "Mozilla/5.0...")
        private String userAgent;
    }
}