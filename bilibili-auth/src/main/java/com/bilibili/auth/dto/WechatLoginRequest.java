package com.bilibili.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 微信登录请求DTO
 *
 * <AUTHOR>
 * @since 2024-07-14
 */
@Data
@Schema(description = "微信登录请求")
public class WechatLoginRequest {

    @NotBlank(message = "登录类型不能为空")
    @Schema(description = "登录类型", example = "wechat")
    private String type;

    @NotBlank(message = "微信授权码不能为空")
    @Schema(description = "微信授权码", example = "wx_auth_code")
    private String code;

    @NotBlank(message = "状态参数不能为空")
    @Schema(description = "状态参数", example = "random_state")
    private String state;

    @Schema(description = "设备信息")
    private DeviceInfo deviceInfo;

    @Data
    @Schema(description = "设备信息")
    public static class DeviceInfo {
        @Schema(description = "设备类型", example = "WEB")
        private String deviceType;

        @Schema(description = "设备标识", example = "Chrome-Win10")
        private String deviceId;

        @Schema(description = "IP地址", example = "*************")
        private String ipAddress;

        @Schema(description = "用户代理", example = "Mozilla/5.0...")
        private String userAgent;
    }
}