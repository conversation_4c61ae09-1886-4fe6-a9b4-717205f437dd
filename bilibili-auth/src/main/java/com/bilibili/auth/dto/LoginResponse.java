package com.bilibili.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 登录响应DTO
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "登录响应")
public class LoginResponse {

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String refreshToken;

    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType;

    @Schema(description = "过期时间(秒)", example = "86400")
    private Long expiresIn;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户���", example = "user123")
    private String username;

    @Schema(description = "登录状态", example = "success", allowableValues = {"success", "require_phone_bind"})
    private String status;

    @Schema(description = "临时令牌（仅在需要绑定手机号时返回）", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String tempToken;

    @Schema(description = "微信用户信息（仅在需要绑定手机号时返回）")
    private WxInfo wxInfo;

    @Schema(description = "用户信息")
    private UserInfo user;

    /**
     * 用户信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户信息")
    public static class UserInfo {
        @Schema(description = "用户UID", example = "123456789")
        private Long uid;

        @Schema(description = "用户名", example = "user123")
        private String username;

        @Schema(description = "昵称", example = "小明")
        private String nickname;

        @Schema(description = "头像URL", example = "https://i0.hdslb.com/bfs/face/default.jpg")
        private String avatar;

        @Schema(description = "用户等级", example = "5")
        private Integer level;

        @Schema(description = "经验值", example = "12500")
        private Integer exp;

        @Schema(description = "硬币数量", example = "1000.00")
        private Double coins;

        @Schema(description = "VIP类型", example = "2")
        private Integer vipType;

        @Schema(description = "VIP状态", example = "1")
        private Integer vipStatus;

        @Schema(description = "VIP���期时间")
        private LocalDateTime vipDueDate;

        @Schema(description = "是否认证", example = "true")
        private Boolean isVerified;

        @Schema(description = "认证类型", example = "1")
        private Integer verifiedType;

        @Schema(description = "手机号", example = "13800138000")
        private String phone;

        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;

        @Schema(description = "是否为新用户", example = "false")
        private Boolean isNewUser;

        @Schema(description = "是否需要绑定手机号", example = "true")
        private Boolean needPhoneBind;

        @Schema(description = "是否已绑定手机号", example = "false")
        private Boolean phoneBound;
    }

    /**
     * 微信用户信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "微信用户信息")
    public static class WxInfo {
        @Schema(description = "微信昵称", example = "微信用户")
        private String nickname;

        @Schema(description = "微信头像", example = "https://wx.qlogo.cn/mmopen/...")
        private String avatar;

        @Schema(description = "微信OpenId", example = "o3_SC5-pVcjuGi8McFpkdKP2yUGo")
        private String openId;

        @Schema(description = "微信UnionId", example = "oWgGz1PV6_43ClHjBHj5ghh7b95Q")
        private String unionId;

        @Schema(description = "性别", example = "0", allowableValues = {"0", "1", "2"})
        private Integer gender;
    }
}
