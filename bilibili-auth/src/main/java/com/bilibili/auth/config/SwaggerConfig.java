package com.bilibili.auth.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger3/OpenAPI3 + Knife4j配置
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Configuration
public class SwaggerConfig {

    private static final String SECURITY_SCHEME_NAME = "BearerAuth";

    @Value("${server.port}")
    private int serverPort;

    @Value("${server.servlet.context-path:/api/v1}")
    private String contextPath;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                // 配置服务器地址，优先使用网关地址进行API测试
                .addServersItem(new Server().url("http://localhost:8080/api/v1")
                        .description("🌉 网关访问（推荐）- 统一入口"))
                .addServersItem(new Server().url("http://localhost:" + serverPort)
                        .description("🔗 直接访问认证服务 - 仅开发调试"))
                .info(new Info()
                        .title("🎬 Bilibili 认证服务API文档")
                        .description("""
                                ## 📖 API接口说明
                                
                                本文档提供Bilibili认证服务的完整API接口文档，包括：
                                
                                ### 🔐 认证功能
                                - 用户名/邮箱/手机号密码登录
                                - 手机号验证码登录（支持新用户自动注册）
                                - 微信扫码登录
                                - JWT令牌刷新
                                - 用户注销
                                
                                ### 🌐 API访问方式
                                
                                **推荐通过网关访问（端口8080）**：
                                - 所有API路径：`/api/v1/auth/**`
                                - 示例：`POST /api/v1/auth/login-by-password`
                                
                                **直接访问认证服务（端口8081）**：
                                - 仅用于开发调试
                                - API路径：`/auth/**`
                                - 示例：`POST /auth/login-by-password`
                                
                                ### 🔐 认证说明
                                - 大部分接口需要JWT Token认证
                                - 登录相关接口无需认证
                                - 在右上角"Authorize"按钮中输入：`Bearer {your_token}`
                                
                                ### 📝 测试建议
                                1. 使用"🌉 网关访问"服务器进行测试
                                2. 先调用登录接口获取Token
                                3. 配置Token后测试其他需要认证的接口
                                """)
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("Sunset Team")
                                .email("<EMAIL>")
                                .url("https://github.com/sunset-team/bilibili"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT"))
                        .termsOfService("https://www.bilibili.com/protocol/terms"))
                .addServersItem(new Server()
                        .url("http://localhost:8080/api/v1")
                        .description("本��开发环境"))
                .addServersItem(new Server()
                        .url("https://api.bilibili.com/api/v1")
                        .description("生产环境"))
                .addSecurityItem(new SecurityRequirement().addList(SECURITY_SCHEME_NAME))
                .components(new Components()
                        .addSecuritySchemes(SECURITY_SCHEME_NAME,
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请输入JWT令牌，格式：Bearer <token>")));
    }

    /**
     * 认证相关API分组
     */
    @Bean
    public GroupedOpenApi authApi() {
        return GroupedOpenApi.builder()
                .group("🔐 认证服务")
                .pathsToMatch("/auth/**")
                .build();
    }

    /**
     * 用户相关API分组
     */
    @Bean
    public GroupedOpenApi userApi() {
        return GroupedOpenApi.builder()
                .group("👤 用户管理")
                .pathsToMatch("/user/**", "/users/**")
                .build();
    }

    /**
     * 系统相关API分组
     */
    @Bean
    public GroupedOpenApi systemApi() {
        return GroupedOpenApi.builder()
                .group("⚙️ 系统管理")
                .pathsToMatch("/health/**", "/actuator/**", "/system/**")
                .build();
    }

    /**
     * 全部API分组
     */
    @Bean
    public GroupedOpenApi allApi() {
        return GroupedOpenApi.builder()
                .group("📋 全部接口")
                .pathsToMatch("/**")
                .build();
    }
}
