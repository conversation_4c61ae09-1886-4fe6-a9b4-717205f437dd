package com.bilibili.auth.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Knife4j增强配置
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Configuration
@EnableKnife4j
@Profile({"dev", "test", "prod"}) // 添加prod环境支持，便于服务器测试API
public class Knife4jConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置knife4j静态资源映射
        registry.addResourceHandler("doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

        registry.addResourceHandler("/favicon.ico")
                .addResourceLocations("classpath:/META-INF/resources/");

        // 添加更多静态资源支持
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/");

        // 添加knife4j相关资源
        registry.addResourceHandler("/webjars/knife4j/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }
}
