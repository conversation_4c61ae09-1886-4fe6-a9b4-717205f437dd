package com.bilibili.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 设备管理配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bilibili.device")
public class DeviceManagementConfig {

    /**
     * 最大设备数量限制
     */
    private int maxDeviceCount = 5;

    /**
     * 普通用户最大设备数量
     */
    private int normalUserMaxDevices = 3;

    /**
     * VIP用户最大设备数量
     */
    private int vipUserMaxDevices = 5;

    /**
     * 设备信任有效期（天）
     */
    private int trustDeviceDays = 30;

    /**
     * 设备离线超时时间（分钟）
     */
    private int deviceOfflineTimeoutMinutes = 30;

    /**
     * 是否启用设备地理位置检测
     */
    private boolean enableLocationDetection = true;

    /**
     * 是否启用可疑设备检测
     */
    private boolean enableSuspiciousDeviceDetection = true;

    /**
     * 新设备登录是否需要验证
     */
    private boolean requireNewDeviceVerification = false;
}
