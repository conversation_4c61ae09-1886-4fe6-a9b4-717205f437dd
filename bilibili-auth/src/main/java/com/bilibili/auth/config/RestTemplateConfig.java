package com.bilibili.auth.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * RestTemplate配置类
 *
 * <AUTHOR>
 * @since 2024-07-14
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 创建RestTemplate Bean
     * 配置支持多种内容类型的消息转换器
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();

        // 获取现有的消息转换器
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();

        // 添加字符串转换器，支持text/plain类型
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringConverter.setSupportedMediaTypes(Arrays.asList(
            MediaType.TEXT_PLAIN,
            MediaType.TEXT_HTML,
            MediaType.APPLICATION_JSON
        ));
        messageConverters.add(stringConverter);

        // 添加JSON转换器，支持application/json和text/plain类型
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
        jsonConverter.setSupportedMediaTypes(Arrays.asList(
            MediaType.APPLICATION_JSON,
            MediaType.APPLICATION_JSON_UTF8,
            new MediaType("application", "json", StandardCharsets.UTF_8),
            new MediaType("text", "json", StandardCharsets.UTF_8),
            new MediaType("text", "plain", StandardCharsets.UTF_8)
        ));
        messageConverters.add(jsonConverter);

        // 设置消息转换器
        restTemplate.setMessageConverters(messageConverters);

        return restTemplate;
    }
}
