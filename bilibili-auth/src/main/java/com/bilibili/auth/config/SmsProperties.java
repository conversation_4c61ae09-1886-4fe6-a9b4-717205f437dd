package com.bilibili.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 短信服务配置
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@Component
@ConfigurationProperties(prefix = "sms")
public class SmsProperties {
    
    /**
     * 阿里云短信配置
     */
    private Aliyun aliyun;

    @Data
    public static class Aliyun {
        /**
         * AccessKey ID
         */
        private String accessKeyId;

        /**
         * AccessKey Secret
         */
        private String accessKeySecret;

        /**
         * 端点
         */
        private String endpoint;

        /**
         * 区域
         */
        private String region;

        /**
         * 签名名称
         */
        private String signName;

        /**
         * 模板配置
         */
        private Templates templates;
    }

    @Data
    public static class Templates {
        /**
         * 登录验证码模板ID
         */
        private String login;
    }
}
