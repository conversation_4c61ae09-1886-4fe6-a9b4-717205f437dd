package com.bilibili.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信登录配置属性
 *
 * <AUTHOR>
 * @since 2024-07-14
 */
@Data
@Component
@ConfigurationProperties(prefix = "wechat")
public class WechatProperties {

    /**
     * 微信应用ID
     */
    private String appid;

    /**
     * 微信应用密钥
     */
    private String secret;

    /**
     * 重定向URI
     */
    private String redirectUri;

    /**
     * 授权作用域
     */
    private String scope = "snsapi_userinfo";

    /**
     * API配置
     */
    private Api api = new Api();

    @Data
    public static class Api {
        /**
         * 获取访问令牌URL
         */
        private String accessTokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token";

        /**
         * 获取用户信息URL
         */
        private String userInfoUrl = "https://api.weixin.qq.com/sns/userinfo";

        /**
         * 刷新令牌URL
         */
        private String refreshTokenUrl = "https://api.weixin.qq.com/sns/oauth2/refresh_token";
    }
}