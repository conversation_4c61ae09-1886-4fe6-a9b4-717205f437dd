package com.bilibili.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * JWT配置属性
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.jwt")
public class JwtProperties {

    /**
     * JWT密钥 - 必须至少64字符(512位)以满足HS512算法要求
     */
    private String secret = "bilibiliAuthSecretKey2024SunsetTeamVeryLongSecretKeyForJWTTokenHS512";

    /**
     * 访问令牌过期时间（毫秒）
     */
    private Long accessTokenExpiration = 86400000L; // 24小时

    /**
     * 刷新令牌过期时间（毫秒）
     */
    private Long refreshTokenExpiration = 604800000L; // 7天

    /**
     * JWT发行者
     */
    private String issuer = "bilibili-auth";

    /**
     * JWT受众
     */
    private String audience = "bilibili-users";
}
