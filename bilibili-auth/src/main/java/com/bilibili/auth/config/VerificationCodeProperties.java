package com.bilibili.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 验证码配置属性
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@Component
@ConfigurationProperties(prefix = "verification.code")
public class VerificationCodeProperties {

    /**
     * 验证码长度，默认6位
     */
    private int length = 6;

    /**
     * 验证码类型，默认纯数字
     */
    private CodeType type = CodeType.NUMERIC;

    /**
     * 验证码过期时间（分钟），默认10分钟
     */
    private int expireMinutes = 10;

    /**
     * 发送间隔（秒），默认60秒
     */
    private int sendIntervalSeconds = 60;

    /**
     * 每日发送次数限制，默认10次
     */
    private int dailyLimit = 10;

    /**
     * 验证码类型枚举
     */
    public enum CodeType {
        NUMERIC,    // 纯数字
        ALPHANUMERIC // 数字字母混合
    }
}
