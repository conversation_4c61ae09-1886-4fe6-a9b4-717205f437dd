package com.bilibili.auth.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bilibili.auth.config.DeviceManagementConfig;
import com.bilibili.auth.dto.LoginRequest;
import com.bilibili.auth.entity.User;
import com.bilibili.auth.entity.UserLoginDevice;
import com.bilibili.auth.mapper.UserLoginDeviceMapper;
import com.bilibili.auth.mapper.UserMapper;
import com.bilibili.auth.service.UserLoginDeviceService;
import com.bilibili.auth.util.DeviceFingerprintUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户登录设备管理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserLoginDeviceServiceImpl implements UserLoginDeviceService {

    private final UserLoginDeviceMapper userLoginDeviceMapper;
    private final UserMapper userMapper;
    private final DeviceManagementConfig deviceConfig;

    @Override
    @Transactional
    public UserLoginDevice recordLoginDevice(Long uid, String userAgent, String ipAddress, 
                                           LoginRequest.DeviceInfo deviceInfo, String tokenId) {
        log.info("记录用户登录设备: uid={}, userAgent={}, ip={}", uid, userAgent, ipAddress);

        // 生成设备指纹
        String deviceFingerprint = DeviceFingerprintUtil.generateFingerprint(userAgent, ipAddress, deviceInfo);
        
        // 查询是否已存在该设备
        UserLoginDevice existingDevice = userLoginDeviceMapper.findByUidAndDeviceFingerprint(uid, deviceFingerprint);
        
        if (existingDevice != null) {
            // 更新现有设备信息
            existingDevice.setTokenId(tokenId);
            existingDevice.setIpAddress(ipAddress);
            existingDevice.setOnline();
            userLoginDeviceMapper.updateById(existingDevice);
            log.info("更新现有设备: deviceId={}", existingDevice.getId());
            return existingDevice;
        }

        // 检查设备数量是否超过限制
        int onlineDeviceCount = userLoginDeviceMapper.countOnlineDevicesByUid(uid);
        int maxDeviceCount = getMaxDeviceCountForUser(uid);

        if (onlineDeviceCount >= maxDeviceCount) {
            log.info("用户设备数量超过限制，执行LRU策略: uid={}, count={}, limit={}", uid, onlineDeviceCount, maxDeviceCount);
            manageLRUDevicesForUser(uid, maxDeviceCount);
        }

        // 创建新设备记录
        UserLoginDevice newDevice = UserLoginDevice.builder()
                .uid(uid)
                .deviceFingerprint(deviceFingerprint)
                .deviceName(DeviceFingerprintUtil.parseDeviceName(userAgent))
                .deviceType(DeviceFingerprintUtil.parseDeviceType(userAgent))
                .operatingSystem(DeviceFingerprintUtil.parseOperatingSystem(userAgent))
                .browserOrApp(DeviceFingerprintUtil.parseBrowser(userAgent))
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .tokenId(tokenId)
                .firstLoginTime(LocalDateTime.now())
                .lastAccessTime(LocalDateTime.now())
                .loginCount(1)
                .isOnline(true)
                .isTrusted(false)
                .status(1)
                .build();

        userLoginDeviceMapper.insert(newDevice);
        log.info("创建新设备记录: deviceId={}", newDevice.getId());
        return newDevice;
    }

    @Override
    public void updateDeviceLastAccess(String tokenId) {
        if (!StringUtils.hasText(tokenId)) {
            return;
        }

        UserLoginDevice device = userLoginDeviceMapper.findByTokenId(tokenId);
        if (device != null) {
            userLoginDeviceMapper.updateLastAccessTime(device.getId(), LocalDateTime.now());
        }
    }

    @Override
    @Transactional
    public void offlineDevice(String tokenId) {
        if (!StringUtils.hasText(tokenId)) {
            return;
        }

        int result = userLoginDeviceMapper.offlineByTokenId(tokenId);
        if (result > 0) {
            log.info("设备已下线: tokenId={}", tokenId);
        }
    }

    @Override
    @Transactional
    public void offlineAllDevices(Long uid) {
        int result = userLoginDeviceMapper.offlineAllDevicesByUid(uid);
        log.info("用户所有设备已下线: uid={}, count={}", uid, result);
    }

    @Override
    public List<UserLoginDevice> getUserOnlineDevices(Long uid) {
        return userLoginDeviceMapper.findOnlineDevicesByUid(uid);
    }

    @Override
    public boolean isDeviceOnline(String tokenId) {
        if (!StringUtils.hasText(tokenId)) {
            return false;
        }

        UserLoginDevice device = userLoginDeviceMapper.findByTokenId(tokenId);
        return device != null && device.getIsOnline() && device.getStatus() == 1;
    }

    @Override
    public UserLoginDevice getDeviceByTokenId(String tokenId) {
        if (!StringUtils.hasText(tokenId)) {
            return null;
        }
        return userLoginDeviceMapper.findByTokenId(tokenId);
    }

    @Override
    @Transactional
    public void manageLRUDevices(Long uid) {
        int maxDeviceCount = getMaxDeviceCountForUser(uid);
        manageLRUDevicesForUser(uid, maxDeviceCount);
    }

    /**
     * 为指定用户执行LRU设备管理
     */
    @Transactional
    public void manageLRUDevicesForUser(Long uid, int maxDeviceCount) {
        // 获取需要移除的设备数量
        int onlineCount = userLoginDeviceMapper.countOnlineDevicesByUid(uid);
        int removeCount = onlineCount - maxDeviceCount + 1;

        if (removeCount > 0) {
            // 获取最久未使用的设备
            List<UserLoginDevice> lruDevices = userLoginDeviceMapper.findLeastRecentlyUsedDevices(uid, removeCount);

            if (!lruDevices.isEmpty()) {
                String deviceIds = lruDevices.stream()
                        .map(device -> device.getId().toString())
                        .collect(Collectors.joining(","));

                userLoginDeviceMapper.batchOfflineDevices(deviceIds);
                log.info("LRU策略移除设备: uid={}, removedCount={}, deviceIds={}, maxLimit={}",
                    uid, lruDevices.size(), deviceIds, maxDeviceCount);
            }
        }
    }

    /**
     * 根据用户权限获取最大设备数量
     */
    private int getMaxDeviceCountForUser(Long uid) {
        try {
            User user = userMapper.selectById(uid);
            if (user != null && user.isVipUser() && user.isVipValid()) {
                return deviceConfig.getVipUserMaxDevices();
            }
            return deviceConfig.getNormalUserMaxDevices();
        } catch (Exception e) {
            log.warn("获取用户权限失败，使用默认设备限制: uid={}", uid, e);
            return deviceConfig.getNormalUserMaxDevices();
        }
    }

    @Override
    public void trustDevice(String tokenId) {
        if (!StringUtils.hasText(tokenId)) {
            return;
        }

        LambdaUpdateWrapper<UserLoginDevice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserLoginDevice::getTokenId, tokenId)
                .set(UserLoginDevice::getIsTrusted, true);
        
        userLoginDeviceMapper.update(null, updateWrapper);
        log.info("设备已设为信任: tokenId={}", tokenId);
    }

    @Override
    public void untrustDevice(String tokenId) {
        if (!StringUtils.hasText(tokenId)) {
            return;
        }

        LambdaUpdateWrapper<UserLoginDevice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserLoginDevice::getTokenId, tokenId)
                .set(UserLoginDevice::getIsTrusted, false);
        
        userLoginDeviceMapper.update(null, updateWrapper);
        log.info("设备已取消信任: tokenId={}", tokenId);
    }
}
