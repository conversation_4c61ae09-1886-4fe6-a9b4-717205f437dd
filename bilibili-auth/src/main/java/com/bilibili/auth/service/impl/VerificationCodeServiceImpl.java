package com.bilibili.auth.service.impl;

import com.bilibili.auth.config.VerificationCodeProperties;
import com.bilibili.auth.service.SmsService;
import com.bilibili.auth.service.VerificationCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Slf4j
@Service
public class VerificationCodeServiceImpl implements VerificationCodeService {

    private final VerificationCodeProperties codeProperties;
    private final SmsService smsService;
    private final StringRedisTemplate redisTemplate;
    private final SecureRandom random = new SecureRandom();

    // Redis Key 前缀
    private static final String CODE_KEY_PREFIX = "verification:code:";
    private static final String SEND_TIME_KEY_PREFIX = "verification:send_time:";
    private static final String DAILY_COUNT_KEY_PREFIX = "verification:daily_count:";

    @Autowired
    public VerificationCodeServiceImpl(VerificationCodeProperties codeProperties,
                                     SmsService smsService,
                                     StringRedisTemplate redisTemplate) {
        this.codeProperties = codeProperties;
        this.smsService = smsService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public boolean sendCode(String phone, String type) {
        // 1. 检查发送频率限制
        if (!canSendCode(phone)) {
            log.warn("发送验证码频率限制 - 手机号: {}", phone);
            return false;
        }

        // 2. 检查每日发送次数限制
        if (!checkDailyLimit(phone)) {
            log.warn("发送验证码每日次数限制 - 手机号: {}", phone);
            return false;
        }

        // 3. 生成验证码
        String code = generateCode();

        // 4. 发送短信
        boolean success = false;
        switch (type) {
            case "login":
                success = smsService.sendLoginCode(phone, code);
                break;
            case "register":
                success = smsService.sendRegisterCode(phone, code);
                break;
            default:
                log.error("不支持的验证码类型: {}", type);
                return false;
        }

        if (success) {
            // 5. 保存验证码到Redis
            String codeKey = CODE_KEY_PREFIX + type + ":" + phone;
            redisTemplate.opsForValue().set(codeKey, code, codeProperties.getExpireMinutes(), TimeUnit.MINUTES);

            // 6. 记录发送时间
            String sendTimeKey = SEND_TIME_KEY_PREFIX + phone;
            redisTemplate.opsForValue().set(sendTimeKey, String.valueOf(System.currentTimeMillis()),
                codeProperties.getSendIntervalSeconds(), TimeUnit.SECONDS);

            // 7. 增加每日发送次数
            incrementDailyCount(phone);

            log.info("验证码发送成功 - 手机号: {}, 类型: {}", phone, type);
        }

        return success;
    }

    @Override
    public boolean verifyCode(String phone, String code, String type) {
        String codeKey = CODE_KEY_PREFIX + type + ":" + phone;
        String storedCode = redisTemplate.opsForValue().get(codeKey);

        if (storedCode == null) {
            log.warn("验证码已过期或不存在 - 手机号: {}, 类型: {}", phone, type);
            return false;
        }

        boolean isValid = storedCode.equals(code);

        if (isValid) {
            // 验证成功后删除验证码
            redisTemplate.delete(codeKey);
            log.info("验证码验证成功 - 手机号: {}, 类型: {}", phone, type);
        } else {
            log.warn("验证码验证失败 - 手机号: {}, 类型: {}, 输入码: {}", phone, type, code);
        }

        return isValid;
    }

    @Override
    public String generateCode() {
        if (codeProperties.getType() == VerificationCodeProperties.CodeType.NUMERIC) {
            // 生成纯数字验证码
            StringBuilder code = new StringBuilder();
            for (int i = 0; i < codeProperties.getLength(); i++) {
                code.append(random.nextInt(10));
            }
            return code.toString();
        } else {
            // 生成数字字母混合验证码
            String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            StringBuilder code = new StringBuilder();
            for (int i = 0; i < codeProperties.getLength(); i++) {
                code.append(chars.charAt(random.nextInt(chars.length())));
            }
            return code.toString();
        }
    }

    @Override
    public boolean canSendCode(String phone) {
        String sendTimeKey = SEND_TIME_KEY_PREFIX + phone;
        return !redisTemplate.hasKey(sendTimeKey);
    }

    @Override
    public long getRemainingSendInterval(String phone) {
        String sendTimeKey = SEND_TIME_KEY_PREFIX + phone;
        Long expire = redisTemplate.getExpire(sendTimeKey, TimeUnit.SECONDS);
        return expire != null && expire > 0 ? expire : 0;
    }

    /**
     * 检查每日发送次数限制
     */
    private boolean checkDailyLimit(String phone) {
        String dailyCountKey = DAILY_COUNT_KEY_PREFIX + phone + ":" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String countStr = redisTemplate.opsForValue().get(dailyCountKey);
        int count = countStr != null ? Integer.parseInt(countStr) : 0;
        return count < codeProperties.getDailyLimit();
    }

    /**
     * 增加每日发送次数
     */
    private void incrementDailyCount(String phone) {
        String dailyCountKey = DAILY_COUNT_KEY_PREFIX + phone + ":" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        Long count = redisTemplate.opsForValue().increment(dailyCountKey);
        if (count == 1) {
            // 第一次发送，设置过期时间为第二天凌晨
            redisTemplate.expire(dailyCountKey, 1, TimeUnit.DAYS);
        }
    }
}
