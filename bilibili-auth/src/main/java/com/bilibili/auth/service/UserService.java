package com.bilibili.auth.service;

import com.bilibili.auth.entity.User;
import com.bilibili.auth.entity.UserDevice;
import com.bilibili.auth.entity.UserStats;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 根据UID获取用户信息
     */
    User getUserByUid(Long uid);

    /**
     * 根据用户名获取用户信息
     */
    User getUserByUsername(String username);

    /**
     * 根据邮箱获取用户信息
     */
    User getUserByEmail(String email);

    /**
     * 根据手机号获取用户信息
     */
    User getUserByPhone(String phone);

    /**
     * 保存用户
     */
    User save(User user);

    /**
     * 根据ID查找用户
     */
    User findById(Long id);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 根据手机号查找用户
     */
    User findByPhone(String phone);

    /**
     * 根���用户名查找用户
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    User findByEmail(String email);

    /**
     * 根据微信OpenId查找用户
     */
    User findByWechatOpenId(String wechatOpenId);

    /**
     * 获取用户统计信息
     */
    UserStats getUserStats(Long userId);

    /**
     * 初始化用户统计信息
     */
    Boolean initUserStats(Long uid);

    /**
     * 获取用户设备列表
     */
    List<UserDevice> getUserDevices(Long uid);

    /**
     * 添加或更新用户设备
     */
    Boolean saveOrUpdateUserDevice(UserDevice device);

    /**
     * 设置设备信任状态
     */
    Boolean setDeviceTrusted(Long uid, String deviceId, Boolean trusted);

    /**
     * 禁用用户设备
     */
    Boolean disableUserDevice(Long uid, String deviceId);

    /**
     * 更新用户最后登录信息
     */
    Boolean updateLastLoginInfo(Long uid, String ip);

    /**
     * 更新用户最后登录时间
     */
    void updateLastLoginTime(Long userId);

    /**
     * 增加用户硬币
     */
    Boolean addUserCoins(Long uid, Double amount, String reason);

    /**
     * 扣除用户硬币
     */
    Boolean deductUserCoins(Long uid, Double amount, String reason);

    /**
     * 检查用户名是否可用
     */
    boolean isUsernameAvailable(String username);

    /**
     * 检查邮箱是否可用
     */
    boolean isEmailAvailable(String email);

    /**
     * 检查手机号是否可用
     */
    boolean isPhoneAvailable(String phone);

    /**
     * 生成唯一UID
     */
    Long generateUniqueUid();

    /**
     * 创建用户
     */
    User createUser(User user);

    /**
     * 删除用户（软删除）
     */
    Boolean deleteUser(Long uid);

    /**
     * 更新用户基础信息
     */
    Boolean updateUserInfo(User user);
}
