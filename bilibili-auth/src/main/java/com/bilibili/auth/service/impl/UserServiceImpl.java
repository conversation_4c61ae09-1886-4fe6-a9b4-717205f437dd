package com.bilibili.auth.service.impl;

import com.bilibili.auth.entity.User;
import com.bilibili.auth.entity.UserDevice;
import com.bilibili.auth.entity.UserStats;
import com.bilibili.auth.mapper.UserDeviceMapper;
import com.bilibili.auth.mapper.UserMapper;
import com.bilibili.auth.mapper.UserStatsMapper;
import com.bilibili.auth.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final UserStatsMapper userStatsMapper;
    private final UserDeviceMapper userDeviceMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String USER_CACHE_KEY = "user:info:";
    private static final String USER_STATS_CACHE_KEY = "user:stats:";
    private static final long UID_START = 100000000L;

    @Override
    public User getUserByUid(Long uid) {
        if (uid == null) {
            return null;
        }
        return userMapper.findByUid(uid);
    }

    @Override
    public User getUserByUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return null;
        }
        return userMapper.findByUsername(username);
    }

    @Override
    public User getUserByEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return null;
        }
        return userMapper.findByEmail(email);
    }

    @Override
    public User getUserByPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }
        return userMapper.findByPhone(phone);
    }

    @Override
    public User findByPhone(String phone) {
        return getUserByPhone(phone);
    }

    @Override
    public User findByUsername(String username) {
        return getUserByUsername(username);
    }

    @Override
    public User findByEmail(String email) {
        return getUserByEmail(email);
    }

    @Override
    public User findByWechatOpenId(String wechatOpenId) {
        if (wechatOpenId == null || wechatOpenId.trim().isEmpty()) {
            return null;
        }
        return userMapper.findByWechatOpenId(wechatOpenId);
    }

    @Override
    public User findById(Long id) {
        if (id == null) {
            return null;
        }
        return userMapper.selectById(id);
    }

    @Override
    public User save(User user) {
        if (user == null) {
            return null;
        }

        try {
            if (user.getId() == null) {
                // 新增用户
                if (user.getUid() == null) {
                    user.setUid(generateUniqueUid());
                }
                user.setCreatedAt(LocalDateTime.now());
                user.setUpdatedAt(LocalDateTime.now());
                userMapper.insert(user);
            } else {
                // 更新用户
                user.setUpdatedAt(LocalDateTime.now());
                userMapper.updateById(user);
            }

            return user;
        } catch (Exception e) {
            log.error("保存用户失败: {}", user, e);
            throw new RuntimeException("保存用户失败", e);
        }
    }

    @Override
    public boolean existsByUsername(String username) {
        return !isUsernameAvailable(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return !isEmailAvailable(email);
    }

    @Override
    public boolean existsByPhone(String phone) {
        return !isPhoneAvailable(phone);
    }

    @Override
    public boolean isUsernameAvailable(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        return userMapper.findByUsername(username) == null;
    }

    @Override
    public boolean isEmailAvailable(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return userMapper.findByEmail(email) == null;
    }

    @Override
    public boolean isPhoneAvailable(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        return userMapper.findByPhone(phone) == null;
    }

    @Override
    public Long generateUniqueUid() {
        Random random = new Random();
        Long uid;
        int attempts = 0;
        int maxAttempts = 100;

        do {
            uid = UID_START + random.nextInt(900000000);
            attempts++;

            if (attempts > maxAttempts) {
                uid = System.currentTimeMillis() % 1000000000L + UID_START;
                break;
            }
        } while (getUserByUid(uid) != null);

        return uid;
    }

    @Override
    public void updateLastLoginTime(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            User user = new User();
            user.setId(userId);
            user.setLastLoginTime(LocalDateTime.now());
            userMapper.updateById(user);

            log.info("更新用户最后登录时间成功: userId={}", userId);
        } catch (Exception e) {
            log.error("更新用户最后登录时间失败: userId={}", userId, e);
        }
    }

    @Override
    public UserStats getUserStats(Long userId) {
        if (userId == null) {
            return null;
        }
        return userStatsMapper.findByUid(userId);
    }

    @Override
    public List<UserDevice> getUserDevices(Long userId) {
        if (userId == null) {
            return null;
        }
        return userDeviceMapper.findByUid(userId);
    }

    @Override
    public Boolean initUserStats(Long uid) {
        try {
            UserStats existingStats = userStatsMapper.findByUid(uid);
            if (existingStats != null) {
                return true;
            }

            UserStats stats = new UserStats();
            stats.setUid(uid);
            stats.setUpdatedAt(LocalDateTime.now());

            int inserted = userStatsMapper.insert(stats);
            return inserted > 0;

        } catch (Exception e) {
            log.error("初始化用户统计信息失败: uid={}", uid, e);
            return false;
        }
    }

    @Override
    public Boolean saveOrUpdateUserDevice(UserDevice device) {
        try {
            if (device == null || device.getUid() == null || device.getDeviceId() == null) {
                return false;
            }

            UserDevice existingDevice = userDeviceMapper.findByUidAndDeviceId(
                device.getUid(), device.getDeviceId());

            if (existingDevice != null) {
                device.setId(existingDevice.getId());
                device.setUpdatedAt(LocalDateTime.now());
                int updated = userDeviceMapper.updateById(device);
                return updated > 0;
            } else {
                device.setCreatedAt(LocalDateTime.now());
                device.setUpdatedAt(LocalDateTime.now());
                int inserted = userDeviceMapper.insert(device);
                return inserted > 0;
            }
        } catch (Exception e) {
            log.error("保存用户设备失败: uid={}, deviceId={}",
                device.getUid(), device.getDeviceId(), e);
            return false;
        }
    }

    @Override
    public Boolean setDeviceTrusted(Long uid, String deviceId, Boolean trusted) {
        try {
            int updated = userDeviceMapper.updateTrustedStatus(uid, deviceId, trusted);
            return updated > 0;
        } catch (Exception e) {
            log.error("设置设备信任状态失败: uid={}, deviceId={}, trusted={}",
                uid, deviceId, trusted, e);
            return false;
        }
    }

    @Override
    public Boolean disableUserDevice(Long uid, String deviceId) {
        try {
            int updated = userDeviceMapper.disableDevice(uid, deviceId);
            return updated > 0;
        } catch (Exception e) {
            log.error("禁用用户设备失败: uid={}, deviceId={}", uid, deviceId, e);
            return false;
        }
    }

    @Override
    public Boolean updateLastLoginInfo(Long uid, String ip) {
        try {
            int updated = userMapper.updateLastLoginInfo(uid, ip);
            return updated > 0;
        } catch (Exception e) {
            log.error("更新用户最后登录信息失败: uid={}, ip={}", uid, ip, e);
            return false;
        }
    }

    @Override
    public Boolean addUserCoins(Long uid, Double amount, String reason) {
        try {
            if (amount <= 0) {
                return false;
            }

            int updated = userMapper.addCoins(uid, amount);
            if (updated > 0) {
                log.info("用户硬币增加: uid={}, amount={}, reason={}", uid, amount, reason);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("增加用户硬币失败: uid={}, amount={}", uid, amount, e);
            return false;
        }
    }

    @Override
    public Boolean deductUserCoins(Long uid, Double amount, String reason) {
        try {
            if (amount <= 0) {
                return false;
            }

            int updated = userMapper.deductCoins(uid, amount);
            if (updated > 0) {
                log.info("用户硬币扣除: uid={}, amount={}, reason={}", uid, amount, reason);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("扣除用户硬币失败: uid={}, amount={}", uid, amount, e);
            return false;
        }
    }

    @Override
    public User createUser(User user) {
        if (user.getUid() == null) {
            user.setUid(generateUniqueUid());
        }
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        userMapper.insert(user);
        return user;
    }

    @Override
    public Boolean deleteUser(Long uid) {
        try {
            User user = getUserByUid(uid);
            if (user == null) {
                return false;
            }

            // 修复：使用正确的字段进行逻辑删除
            user.setStatus(0); // 0表示禁用
            user.setDeletedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            // 清除缓存
            String cacheKey = USER_CACHE_KEY + uid;
            redisTemplate.delete(cacheKey);

            return true;
        } catch (Exception e) {
            log.error("删除用户失败: uid={}", uid, e);
            return false;
        }
    }

    @Override
    public Boolean updateUserInfo(User user) {
        try {
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            return true;
        } catch (Exception e) {
            log.error("更新用户信息失败: uid={}", user.getUid(), e);
            return false;
        }
    }
}
