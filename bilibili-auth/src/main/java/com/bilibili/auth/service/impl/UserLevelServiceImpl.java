package com.bilibili.auth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bilibili.auth.entity.User;
import com.bilibili.auth.entity.UserLevel;
import com.bilibili.auth.mapper.UserLevelMapper;
import com.bilibili.auth.mapper.UserMapper;
import com.bilibili.auth.service.UserLevelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * 用户等级服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserLevelServiceImpl implements UserLevelService {

    private final UserLevelMapper userLevelMapper;
    private final UserMapper userMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ApplicationEventPublisher eventPublisher;

    private static final String LEVEL_CACHE_KEY = "user:level:config:";
    private static final String USER_EXP_CACHE_KEY = "user:exp:";

    @Override
    public UserLevel calculateLevel(Integer exp) {
        // 从缓存中获取等级配置
        String cacheKey = LEVEL_CACHE_KEY + "all";

        QueryWrapper<UserLevel> queryWrapper = new QueryWrapper<>();
        queryWrapper.le("min_exp", exp)
                   .orderByDesc("level")
                   .last("LIMIT 1");

        UserLevel userLevel = userLevelMapper.selectOne(queryWrapper);

        if (userLevel != null) {
            // 缓存等级配置
            redisTemplate.opsForValue().set(
                LEVEL_CACHE_KEY + userLevel.getLevel(),
                userLevel,
                1,
                TimeUnit.HOURS
            );
        }

        return userLevel;
    }

    @Override
    @Transactional
    public void updateUserExp(Long uid, Integer expChange, String reason) {
        try {
            // 获取用户当前信息
            User user = userMapper.selectOne(
                new QueryWrapper<User>().eq("uid", uid)
            );

            if (user == null) {
                log.warn("用户不存在: {}", uid);
                return;
            }

            Integer currentExp = user.getExp();
            Integer newExp = currentExp + expChange;

            // 检查是否升级
            boolean levelUp = checkLevelUp(uid, newExp);

            // 更新用户经验值
            user.setExp(newExp);

            if (levelUp) {
                UserLevel newLevel = calculateLevel(newExp);
                if (newLevel != null) {
                    user.setLevel(newLevel.getLevel());
                    handleLevelUp(uid, newLevel);
                }
            }

            userMapper.updateById(user);

            // 更新缓存
            redisTemplate.opsForValue().set(
                USER_EXP_CACHE_KEY + uid,
                newExp,
                30,
                TimeUnit.MINUTES
            );

            log.info("用户 {} 经验值变化: {} -> {}, 变化量: {}, 原因: {}",
                    uid, currentExp, newExp, expChange, reason);

        } catch (Exception e) {
            log.error("更新用户经验值失败: uid={}, expChange={}", uid, expChange, e);
            throw e;
        }
    }

    @Override
    public UserLevel getLevelConfig(Integer level) {
        String cacheKey = LEVEL_CACHE_KEY + level;

        // 先从缓存获取
        UserLevel cached = (UserLevel) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }

        // 从数据库获取
        UserLevel userLevel = userLevelMapper.selectOne(
            new QueryWrapper<UserLevel>().eq("level", level)
        );

        if (userLevel != null) {
            // 缓存配置
            redisTemplate.opsForValue().set(cacheKey, userLevel, 1, TimeUnit.HOURS);
        }

        return userLevel;
    }

    @Override
    public boolean checkLevelUp(Long uid, Integer newExp) {
        User user = userMapper.selectOne(
            new QueryWrapper<User>().eq("uid", uid)
        );

        if (user == null) {
            return false;
        }

        UserLevel currentLevel = getLevelConfig(user.getLevel());
        UserLevel newLevel = calculateLevel(newExp);

        return newLevel != null && currentLevel != null &&
               newLevel.getLevel() > currentLevel.getLevel();
    }

    @Override
    @Transactional
    public void handleLevelUp(Long uid, UserLevel newLevel) {
        try {
            log.info("用户 {} 升级到等级 {}", uid, newLevel.getLevel());

            // 发送升级事件
            LevelUpEvent event = new LevelUpEvent(uid, newLevel);
            eventPublisher.publishEvent(event);

            // 可以在这里添加升级奖励逻辑
            grantLevelUpRewards(uid, newLevel);

        } catch (Exception e) {
            log.error("处理用户升级失败: uid={}, level={}", uid, newLevel.getLevel(), e);
        }
    }

    /**
     * 发放升级奖励
     */
    private void grantLevelUpRewards(Long uid, UserLevel newLevel) {
        // 根据等级发放不同奖励
        switch (newLevel.getLevel()) {
            case 1 -> grantCoins(uid, 5.0, "升级到LV1奖励");
            case 2 -> grantCoins(uid, 10.0, "升级到LV2奖励");
            case 3 -> grantCoins(uid, 20.0, "升级到LV3奖励");
            case 4 -> grantCoins(uid, 50.0, "升级到LV4奖励");
            case 5 -> grantCoins(uid, 100.0, "升级到LV5奖励");
            case 6 -> grantCoins(uid, 200.0, "升级到LV6奖励");
            default -> {
                if (newLevel.getLevel() >= 7) {
                    grantCoins(uid, 500.0, "升级到LV" + newLevel.getLevel() + "奖励");
                }
            }
        }
    }

    /**
     * 发放硬币奖励
     */
    private void grantCoins(Long uid, Double amount, String reason) {
        // 这里应该调用用户服务的硬币增加接口
        log.info("用户 {} 获得硬币奖励: {} 个, 原因: {}", uid, amount, reason);
    }

    /**
     * 升级事件类
     */
    public static class LevelUpEvent {
        private final Long uid;
        private final UserLevel newLevel;

        public LevelUpEvent(Long uid, UserLevel newLevel) {
            this.uid = uid;
            this.newLevel = newLevel;
        }

        public Long getUid() { return uid; }
        public UserLevel getNewLevel() { return newLevel; }
    }
}
