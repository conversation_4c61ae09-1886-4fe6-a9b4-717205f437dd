package com.bilibili.auth.service;

import com.bilibili.auth.dto.LoginRequest;
import com.bilibili.auth.entity.UserLoginDevice;

import java.util.List;

/**
 * 用户登录设备管理服务接口
 */
public interface UserLoginDeviceService {

    /**
     * 最大设备数量限制
     */
    int MAX_DEVICE_COUNT = 5;

    /**
     * 记录用户登录设备
     * 如果设备数量超过限制，会使用LRU策略移除最久未使用的设备
     */
    UserLoginDevice recordLoginDevice(Long uid, String userAgent, String ipAddress, 
                                    LoginRequest.DeviceInfo deviceInfo, String tokenId);

    /**
     * 更新设备的最后访问时间
     */
    void updateDeviceLastAccess(String tokenId);

    /**
     * 下线指定设备
     */
    void offlineDevice(String tokenId);

    /**
     * 下线用户的所有设备
     */
    void offlineAllDevices(Long uid);

    /**
     * 获取用户的在线设备列表
     */
    List<UserLoginDevice> getUserOnlineDevices(Long uid);

    /**
     * 检查设备是否在线
     */
    boolean isDeviceOnline(String tokenId);

    /**
     * 根据Token ID获取设备信息
     */
    UserLoginDevice getDeviceByTokenId(String tokenId);

    /**
     * 使用LRU策略管理设备数量
     * 当设备数量超过限制时，移除最久未使用的设备
     */
    void manageLRUDevices(Long uid);

    /**
     * 信任设备
     */
    void trustDevice(String tokenId);

    /**
     * 取消信任设备
     */
    void untrustDevice(String tokenId);
}
