package com.bilibili.auth.service.impl;

import com.bilibili.auth.dto.*;
import com.bilibili.auth.entity.User;
import com.bilibili.auth.entity.UserLoginDevice;
import com.bilibili.common.exception.ServiceException;
import com.bilibili.auth.exception.AuthenticationException;
import com.bilibili.auth.security.CustomUserDetails;
import com.bilibili.auth.service.AuthService;
import com.bilibili.auth.service.UserService;
import com.bilibili.auth.service.UserLoginDeviceService;
import com.bilibili.auth.service.VerificationCodeService;
import com.bilibili.auth.util.JwtUtil;
import com.bilibili.auth.util.VipInfo;
import com.bilibili.auth.config.WechatProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.RestClientException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import jakarta.servlet.http.HttpServletRequest;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 用户认证服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final AuthenticationManager authenticationManager;
    private final UserDetailsService userDetailsService;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;
    private final RedisTemplate<String, Object> redisTemplate;
    private final UserService userService;
    private final VerificationCodeService verificationCodeService;
    private final UserLoginDeviceService userLoginDeviceService;
    private final WechatProperties wechatProperties;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public LoginResponse login(LoginRequest request) {
        try {
            log.info("用户登录请求: {}", request.getAccount());

            // 1. 验证用户凭据
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    request.getAccount(),
                    request.getPassword()
                )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 2. 获取用户详情
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            Long uid = getUserIdFromUserDetails(userDetails);

            // 3. 生成Token ID
            String tokenId = jwtUtil.generateTokenId();

            // 4. 获取VIP信息
            VipInfo vipInfo = getVipInfo(uid);

            // 5. 生成JWT令牌（包含Token ID和VIP信息）
            String accessToken = jwtUtil.generateAccessToken(uid, userDetails.getUsername(), tokenId, vipInfo);
            String refreshToken = jwtUtil.generateRefreshToken(uid);

            // 5. 记录登录设备
            String userAgent = getCurrentUserAgent();
            String ipAddress = getCurrentIpAddress();
            UserLoginDevice device = userLoginDeviceService.recordLoginDevice(
                uid, userAgent, ipAddress, request.getDeviceInfo(), tokenId);

            // 6. 缓存令牌
            cacheTokens(accessToken, refreshToken, uid);

            // 7. 记录登录日志
            recordLoginLog(uid, request);

            // 8. 构建响应
            LoginResponse response = LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtUtil.getRemainingTime(accessToken) / 1000)
                .user(buildUserInfo(userDetails))
                .build();

            log.info("用户登录成功: {}, 设备: {}", request.getAccount(), device.getDeviceName());
            return response;

        } catch (Exception e) {
            log.error("用户登录失败: {}", request.getAccount(), e);
            throw new AuthenticationException("用户名或密码错误");
        }
    }

    @Override
    public LoginResponse smsLogin(String phone, String code, LoginRequest.DeviceInfo deviceInfo) {
        try {
            log.info("用户短信登录请求: {}", phone);

            // 1. 验证验证码
            if (!verificationCodeService.verifyCode(phone, code, "login")) {
                throw new AuthenticationException("验证码错误或已过期");
            }

            // 2. 查找或创建用户
            User user = userService.findByPhone(phone);
            if (user == null) {
                // 如果用户不存在，创建新用户
                user = createUserWithPhone(phone);
            }

            // 3. 获取VIP信息
            VipInfo vipInfo = getVipInfo(user.getId());

            // 4. 生成JWT令牌（包含VIP信息）
            String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername(), null, vipInfo);
            String refreshToken = jwtUtil.generateRefreshToken(user.getId());

            // 4. 缓存令牌
            cacheTokens(accessToken, refreshToken, user.getId());

            // 5. 记录登录日志
            recordSmsLoginLog(user.getId(), phone, deviceInfo);

            // 6. 构建响应
            LoginResponse response = LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtUtil.getRemainingTime(accessToken) / 1000)
                .user(buildUserInfo(user))
                .build();

            log.info("用户短信登录成功: {}", phone);
            return response;

        } catch (Exception e) {
            log.error("用户短信登录失败: {}", phone, e);
            throw new AuthenticationException("短信登录失败");
        }
    }

    @Override
    public LoginResponse codeLogin(CodeLoginRequest request) {
        try {
            log.info("用户验证码登录请求: {}", request.getPhone());

            // 1. 验证验证码
            if (!verificationCodeService.verifyCode(request.getPhone(), request.getCode(), "login")) {
                throw new AuthenticationException("验证码错误或已过期");
            }

            // 2. 查找或创建用户
            User user = userService.findByPhone(request.getPhone());
            if (user == null) {
                // 如果用户不存在，创建新用户
                user = createUserWithPhone(request.getPhone());
            }

            // 3. 获取VIP信息
            VipInfo vipInfo = getVipInfo(user.getId());

            // 4. 生成JWT令牌（包含VIP信息）
            String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername(), null, vipInfo);
            String refreshToken = jwtUtil.generateRefreshToken(user.getId());

            // 4. 缓存令牌
            cacheTokens(accessToken, refreshToken, user.getId());

            // 5. 记录登录日志
            recordCodeLoginLog(user.getId(), request);

            // 6. 构建响应
            LoginResponse response = LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtUtil.getRemainingTime(accessToken) / 1000)
                .user(buildUserInfo(user))
                .build();

            log.info("用户验证码登录成功: {}", request.getPhone());
            return response;

        } catch (Exception e) {
            log.error("用户验证码登录失败: {}", request.getPhone(), e);
            throw new AuthenticationException("验证码登录失败");
        }
    }

    @Override
    public void sendCode(SendCodeRequest request) {
        try {
            log.info("发送验证码请求: phone={}", request.getPhone());

            // 1. 检查发送频率限制
            if (!verificationCodeService.canSendCode(request.getPhone())) {
                long remainingTime = verificationCodeService.getRemainingSendInterval(request.getPhone());
                throw new ServiceException("发送过于频繁，请" + remainingTime + "秒后再试");
            }

            // 2. 发送验证码（统一使用login类型，因为不再区分登录和注册）
            boolean success = verificationCodeService.sendCode(request.getPhone(), "login");
            if (!success) {
                throw new ServiceException("验证码发送失败，请稍后重试");
            }

            log.info("验证码发送成功: phone={}", request.getPhone());

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证码发送失败: phone={}", request.getPhone(), e);
            throw new ServiceException("验证码发送失败");
        }
    }





    @Override
    public void resetPassword(String account, String code, String newPassword) {
        // TODO: 实现密码重置
        throw new ServiceException("密码重置功能暂未实现");
    }

    @Override
    public User validateAccessToken(String token) {
        try {
            if (!jwtUtil.validateToken(token)) {
                return null;
            }

            // 检查黑名单
            if (redisTemplate.hasKey("blacklist:token:" + token)) {
                return null;
            }

            Long userId = jwtUtil.getUidFromToken(token);
            return userService.findById(userId);
        } catch (Exception e) {
            log.error("验证访问令牌失败", e);
            return null;
        }
    }

    @Override
    public RegisterResponse register(RegisterRequest request) {
        try {
            log.info("用户注册请求: {}", request.getUsername());

            // 检查用户是否已存在
            if (userService.existsByUsername(request.getUsername()) ||
                userService.existsByEmail(request.getEmail()) ||
                userService.existsByPhone(request.getPhone())) {
                throw new ServiceException("用户已存在");
            }

            // 创建用户
            User user = User.builder()
                .username(request.getUsername())
                .email(request.getEmail())
                .phone(request.getPhone())
                .passwordHash(passwordEncoder.encode(request.getPassword()))
                .nickname(request.getNickname())
                .build();

            userService.save(user);

            log.info("用户注册成功: {}", user.getUsername());

            return RegisterResponse.builder()
                .success(true)
                .message("注册成功")
                .userId(user.getId())
                .build();

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户注册失败", e);
            throw new ServiceException("注册失败");
        }
    }

    @Override
    public LoginResponse refreshToken(String refreshToken) {
        try {
            // 1. 验证刷新令牌
            if (!jwtUtil.validateToken(refreshToken) || !jwtUtil.isRefreshToken(refreshToken)) {
                throw new AuthenticationException("无效的刷新令牌");
            }

            // 2. 获取用户信息
            Long uid = jwtUtil.getUidFromToken(refreshToken);
            User user = userService.getUserByUid(uid);
            if (user == null) {
                throw new AuthenticationException("用户不存在");
            }

            // 3. 获取VIP信息
            VipInfo vipInfo = getVipInfo(uid);

            // 4. 生成新的访问令牌（包含VIP信息）
            String newAccessToken = jwtUtil.generateAccessToken(uid, user.getUsername(), null, vipInfo);

            // 4. 缓存新令牌
            cacheTokens(newAccessToken, refreshToken, uid);

            // 5. 构建响应
            LoginResponse response = LoginResponse.builder()
                .accessToken(newAccessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtUtil.getRemainingTime(newAccessToken) / 1000)
                .userId(user.getId())
                .username(user.getUsername())
                .build();

            log.info("刷新令牌成功: uid={}", uid);
            return response;

        } catch (AuthenticationException e) {
            throw e;
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            throw new AuthenticationException("令牌刷新失败");
        }
    }

    @Override
    public void logout(String token) {
        try {
            // 1. 验证令牌
            if (!jwtUtil.validateToken(token)) {
                return;
            }

            // 2. 获取Token ID并下线设备
            String tokenId = jwtUtil.getTokenIdFromToken(token);
            if (tokenId != null) {
                userLoginDeviceService.offlineDevice(tokenId);
            }

            // 3. 将令牌加入黑名单
            Long uid = jwtUtil.getUidFromToken(token);
            long remainingTime = jwtUtil.getRemainingTime(token);

            redisTemplate.opsForValue().set(
                "blacklist:token:" + token,
                uid,
                remainingTime,
                TimeUnit.MILLISECONDS
            );

            // 4. 清除用户会话
            redisTemplate.delete("user:session:" + uid);

            log.info("用户登出成功: uid={}, tokenId={}", uid, tokenId);

        } catch (Exception e) {
            log.error("用户登出失败", e);
        }
    }

    @Override
    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            return userDetails.getUser();
        }
        return null;
    }

    @Override
    public Long getUserIdFromToken(String token) {
        try {
            if (!jwtUtil.validateToken(token)) {
                return null;
            }
            return jwtUtil.getUidFromToken(token);
        } catch (Exception e) {
            log.error("从token获取用户ID失败", e);
            return null;
        }
    }

    /**
     * 根据手机号创建新用户
     */
    private User createUserWithPhone(String phone) {
        // 生成默认密码和盐值
        String salt = generateSalt();
        String defaultPassword = "default123"; // 默认密码，用户可以后续修改
        String passwordHash = passwordEncoder.encode(defaultPassword + salt);

        User user = User.builder()
            .phone(phone)
            .username(generateUsernameFromPhone(phone))
            .nickname(generateRandomNickname())
            .avatar(generateDefaultAvatar())
            .passwordHash(passwordHash)
            .salt(salt)
            .gender(0) // 默认未知
            .level(0) // 默认等级0
            .exp(0) // 默认经验值0
            .coins(0.0) // 默认硬币0
            .vipType(0) // 默认非VIP
            .vipStatus(0) // 默认非VIP状态
            .status(1) // 默认正常状态
            .isVerified(false) // 默认未认证
            .verifiedType(0) // 默认无认证类型
            .phoneBound(true) // 手机号注册的用户默认已绑定手机号
            .realNameVerified(0) // 默认未实名认证
            .build();

        userService.save(user);
        log.info("创建新用户: phone={}, username={}, uid={}", phone, user.getUsername(), user.getUid());
        return user;
    }

    /**
     * 从手机号生成用户名
     */
    private String generateUsernameFromPhone(String phone) {
        return "user_" + phone.substring(phone.length() - 4) + "_" + System.currentTimeMillis();
    }

    /**
     * 生成随机昵称
     */
    private String generateRandomNickname() {
        String[] adjectives = {"可爱的", "聪明的", "勇敢的", "温柔的", "开朗的", "活泼的", "优雅的", "神秘的"};
        String[] nouns = {"小猫", "小狗", "小鸟", "小熊", "小兔", "小鹿", "小鱼", "小虎"};

        int adjIndex = (int)(Math.random() * adjectives.length);
        int nounIndex = (int)(Math.random() * nouns.length);

        return adjectives[adjIndex] + nouns[nounIndex] + (int)(Math.random() * 1000);
    }

    /**
     * 生成默认头像URL
     */
    private String generateDefaultAvatar() {
        // 使用头像生成服务或默认头像
        return "https://api.dicebear.com/6.x/adventurer/svg?seed=" + UUID.randomUUID().toString();
    }

    /**
     * 生成随机盐值
     */
    private String generateSalt() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 32);
    }

    /**
     * 记录短信登录日志
     */
    private void recordSmsLoginLog(Long userId, String phone, LoginRequest.DeviceInfo deviceInfo) {
        // 这里可以记录登录日志到数据库
        log.info("短信登录记录: userId={}, phone={}, device={}", userId, phone, deviceInfo);
    }

    /**
     * 记录验证码登录日志
     */
    private void recordCodeLoginLog(Long userId, CodeLoginRequest request) {
        // 这里可以记录登录日志到数据库
        log.info("验证码登录记录: userId={}, phone={}, device={}", userId, request.getPhone(), request.getDeviceInfo());
    }

    /**
     * 构建用户信息
     */
    private LoginResponse.UserInfo buildUserInfo(User user) {
        return LoginResponse.UserInfo.builder()
            .uid(user.getId())
            .username(user.getUsername())
            .nickname(user.getNickname())
            .avatar(user.getAvatar())
            .phone(user.getPhone())
            .email(user.getEmail())
            .build();
    }

    /**
     * 构建用户信息（从UserDetails）
     */
    private LoginResponse.UserInfo buildUserInfo(UserDetails userDetails) {
        if (userDetails instanceof CustomUserDetails) {
            CustomUserDetails customUserDetails = (CustomUserDetails) userDetails;
            User user = customUserDetails.getUser();
            return buildUserInfo(user);
        }

        // 如果不是CustomUserDetails，返回基本信息
        return LoginResponse.UserInfo.builder()
            .username(userDetails.getUsername())
            .build();
    }


    private void cacheTokens(String accessToken, String refreshToken, Long userId) {
        // 缓存用户会话
        redisTemplate.opsForValue().set(
            "user:session:" + userId,
            accessToken,
            jwtUtil.getRemainingTime(accessToken),
            TimeUnit.MILLISECONDS
        );
    }

    private Long getUserIdFromUserDetails(UserDetails userDetails) {
        if (userDetails instanceof CustomUserDetails) {
            return ((CustomUserDetails) userDetails).getUser().getId();
        }
        // 或者从用户名查询用户ID
        User user = userService.findByUsername(userDetails.getUsername());
        return user != null ? user.getId() : null;
    }

    private void recordLoginLog(Long userId, LoginRequest request) {
        // 记录普通登录日志
        log.info("记录登录日志: userId={}", userId);
    }

    @Override
    public LoginResponse wechatLogin(String code, String state, SessionRequest.DeviceInfo deviceInfo) {
        try {
            log.info("用户微信登录请求: code={}, state={}", code, state);

            // 1. 验证微信授权码和状态参数
            if (code == null || code.trim().isEmpty()) {
                throw new AuthenticationException("微信授权码不能为空");
            }
            if (state == null || state.trim().isEmpty()) {
                throw new AuthenticationException("状态参数不能为空");
            }

            // 2. 调用微信API获取用户信息
            WechatUserInfo wechatUserInfo = getWechatUserInfo(code, state);
            if (wechatUserInfo == null) {
                throw new AuthenticationException("微信授权失败");
            }

            // 3. 实现混合模式登录逻辑
            User user = findUserByWechatOpenId(wechatUserInfo.getOpenId());

            if (user != null) {
                // 场景一：找到用户 (微信已绑定，直接登录)
                if (user.getPhoneBound() != null && user.getPhoneBound()) {
                    log.info("微信用户已绑定手机号，直接登录成功: openId={}, phone={}", 
                        wechatUserInfo.getOpenId(), user.getPhone());

                    // 获取VIP信息
                    VipInfo vipInfo = getVipInfo(user.getId());

                    // 生成正式的JWT令牌（包含VIP信息）
                    String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername(), null, vipInfo);
                    String refreshToken = jwtUtil.generateRefreshToken(user.getId());

                    // 缓存令牌
                    cacheTokens(accessToken, refreshToken, user.getId());

                    // 记录登录日志
                    recordWechatLoginLog(user.getId(), code, deviceInfo);

                    // 构建成功响应
                    LoginResponse.UserInfo userInfo = buildUserInfo(user);
                    userInfo.setIsNewUser(false);
                    userInfo.setNeedPhoneBind(false);
                    userInfo.setPhoneBound(true);

                    LoginResponse response = LoginResponse.builder()
                        .accessToken(accessToken)
                        .refreshToken(refreshToken)
                        .tokenType("Bearer")
                        .expiresIn(jwtUtil.getRemainingTime(accessToken) / 1000)
                        .status("success")
                        .user(userInfo)
                        .build();

                    log.info("用户微信登录成功: openId={}, 已绑定手机号", wechatUserInfo.getOpenId());
                    return response;
                } else {
                    // 用户存在但未绑定手机号，这种情况不应该存在，但为了安全起见，要求绑定手机号
                    log.warn("微信用户存在但未绑定手机号，要求绑定: openId={}", wechatUserInfo.getOpenId());
                    return createPhoneBindRequiredResponse(wechatUserInfo, user);
                }
            } else {
                // 场景二：未找到用户 (新微信，必须绑定手机号)
                log.info("首次微信登录，需要绑定手机号: openId={}", wechatUserInfo.getOpenId());

                // 创建临时用户但不保存到数据库，等待手机号绑定后再保存
                User tempUser = createTempUserWithWechat(wechatUserInfo);
                return createPhoneBindRequiredResponse(wechatUserInfo, tempUser);
            }

        } catch (Exception e) {
            log.error("用户微信登录失败: code={}", code, e);
            throw new AuthenticationException("微信登录失败");
        }
    }

    @Override
    public LoginResponse createSession(SessionRequest request) {
        try {
            log.info("创建会话请求: type={}", request.getType());

            switch (request.getType().toLowerCase()) {
                case "phone":
                    // 手机号登录
                    if (request.getPhone() == null || request.getVerificationCode() == null) {
                        throw new ServiceException("手机号和验证码不能为空");
                    }
                    CodeLoginRequest codeRequest = new CodeLoginRequest();
                    codeRequest.setPhone(request.getPhone());
                    codeRequest.setCode(request.getVerificationCode());
                    codeRequest.setDeviceInfo(convertDeviceInfo(request.getDeviceInfo()));
                    return codeLogin(codeRequest);

                case "wechat":
                    // 微信登录
                    if (request.getCode() == null || request.getState() == null) {
                        throw new ServiceException("微信授权码和状态参数不能为空");
                    }
                    return wechatLogin(request.getCode(), request.getState(), request.getDeviceInfo());



                default:
                    throw new ServiceException("不支持的登录类型: " + request.getType());
            }

        } catch (ServiceException | AuthenticationException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建会话失败: type={}", request.getType(), e);
            throw new ServiceException("创建会话失败");
        }
    }

    @Override
    public LoginResponse wechatBindPhone(String tempToken, String phone, String code) {
        try {
            log.info("微信绑定手机号: phone={}", phone);

            // 1. 验证临时JWT令牌
            String actualToken = tempToken.startsWith("Bearer ") ? tempToken.substring(7) : tempToken;
            if (!jwtUtil.validateToken(actualToken)) {
                throw new ServiceException("临时令牌无效或已过期");
            }

            // 2. 验证手机验证码
            if (!verificationCodeService.verifyCode(phone, code, "login")) {
                throw new ServiceException("验证码错误或已过期");
            }

            // 3. 检查手机号是否已被其他用户使用
            if (userService.existsByPhone(phone)) {
                throw new ServiceException("该手机号已被其他用户绑定");
            }

            // 4. 从临时令牌中获取用户信息
            Long tempUserId = jwtUtil.getUidFromToken(actualToken);
            User tempUser = userService.getUserByUid(tempUserId);
            if (tempUser == null) {
                throw new ServiceException("临时用户信息不存在");
            }

            // 5. 更新用户信息，绑定手机号
            tempUser.setPhone(phone);
            tempUser.setPhoneBound(true);
            userService.updateUserInfo(tempUser);

            // 6. 生成正式的JWT令牌
            String accessToken = jwtUtil.generateAccessToken(tempUser.getId(), tempUser.getUsername());
            String refreshToken = jwtUtil.generateRefreshToken(tempUser.getId());

            // 7. 缓存令牌
            cacheTokens(accessToken, refreshToken, tempUser.getId());

            // 8. 构建响应
            LoginResponse.UserInfo userInfo = buildUserInfo(tempUser);
            userInfo.setIsNewUser(true);
            userInfo.setNeedPhoneBind(false);
            userInfo.setPhoneBound(true);

            LoginResponse response = LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtUtil.getRemainingTime(accessToken) / 1000)
                .status("success")
                .user(userInfo)
                .build();

            log.info("微信绑定手机号成功: phone={}, userId={}", phone, tempUser.getId());
            return response;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("微信绑定手机号失败: phone={}, error={}", phone, e.getMessage(), e);
            throw new ServiceException("绑定失败: " + e.getMessage());
        }
    }

    /**
     * 根据微信信息创建新用户
     */
    private User createUserWithWechat(WechatUserInfo wechatUserInfo) {
        // 为微信用户生成默认密码哈希和盐值（微信用户不使用密码登录）
        String defaultPassword = "WECHAT_USER_NO_PASSWORD";
        String salt = generateSalt();
        String passwordHash = passwordEncoder.encode(defaultPassword + salt);

        User user = User.builder()
            .username(generateUsernameFromWechat(wechatUserInfo.getOpenId()))
            .nickname(wechatUserInfo.getNickname() != null ? wechatUserInfo.getNickname() : generateRandomNickname())
            .avatar(wechatUserInfo.getAvatar() != null ? wechatUserInfo.getAvatar() : generateDefaultAvatar())
            .gender(wechatUserInfo.getGender() != null ? wechatUserInfo.getGender() : 0)
            .passwordHash(passwordHash)
            .salt(salt)
            .wechatOpenId(wechatUserInfo.getOpenId())
            .wechatUnionId(wechatUserInfo.getUnionId())
            .level(0) // 默认等级0
            .exp(0) // 默认经验值0
            .coins(0.0) // 默认硬币0
            .vipType(0) // 默认非VIP
            .vipStatus(0) // 默认非VIP状态
            .status(1) // 默认正常状态
            .isVerified(false) // 默认未认证
            .verifiedType(0) // 默认无认证类型
            .phoneBound(false) // 新用户默认未绑定手机号
            .realNameVerified(0) // 默认未实名认证
            .build();

        userService.save(user);
        log.info("创建新微信用户: openId={}, username={}, nickname={}", 
            wechatUserInfo.getOpenId(), user.getUsername(), user.getNickname());
        return user;
    }

    /**
     * 从微信OpenId生成用户名（确定性生成，不包含时间戳）
     */
    private String generateUsernameFromWechat(String openId) {
        // 使用OpenId的哈希值生成确定性的用户名
        return "wx_" + Math.abs(openId.hashCode());
    }

    /**
     * 根据微信OpenId查找用户
     */
    private User findUserByWechatOpenId(String openId) {
        if (!StringUtils.hasText(openId)) {
            return null;
        }
        return userService.findByWechatOpenId(openId);
    }

    /**
     * 获取微信用户信息（真实API实现）
     */
    private WechatUserInfo getWechatUserInfo(String code, String state) {
        log.info("调用微信API获取用户信息: code={}, state={}", code, state);

        try {
            // 1. 通过code获取access_token
            WechatAccessTokenResponse tokenResponse = getWechatAccessToken(code);
            if (!tokenResponse.isSuccess()) {
                log.error("获取微信访问令牌失败: {}", tokenResponse.getErrmsg());
                throw new ServiceException("微信登录失败：" + tokenResponse.getErrmsg());
            }

            // 2. 通过access_token获取用户信息
            WechatUserInfoResponse userInfoResponse = getWechatUserInfoByToken(
                tokenResponse.getAccessToken(), tokenResponse.getOpenid());
            if (!userInfoResponse.isSuccess()) {
                log.error("获取微信用户信息失败: {}", userInfoResponse.getErrmsg());
                throw new ServiceException("获取微信用户信息失败：" + userInfoResponse.getErrmsg());
            }

            // 3. 转换为内部用户信息格式
            WechatUserInfo userInfo = new WechatUserInfo();
            userInfo.setOpenId(userInfoResponse.getOpenid());
            userInfo.setUnionId(userInfoResponse.getUnionid());
            userInfo.setNickname(userInfoResponse.getNickname());
            userInfo.setAvatar(userInfoResponse.getHeadimgurl());
            userInfo.setGender(userInfoResponse.getFormattedGender());

            log.info("成功获取微信用户信息: openId={}, nickname={}", 
                userInfo.getOpenId(), userInfo.getNickname());
            return userInfo;

        } catch (Exception e) {
            log.error("调用微信API异常", e);
            throw new ServiceException("微信登录失败，请重试");
        }
    }

    /**
     * 获取微信访问令牌
     */
    private WechatAccessTokenResponse getWechatAccessToken(String code) {
        String url = wechatProperties.getApi().getAccessTokenUrl() + 
            "?appid=" + wechatProperties.getAppid() +
            "&secret=" + wechatProperties.getSecret() +
            "&code=" + code +
            "&grant_type=authorization_code";

        log.debug("请求微信访问令牌: {}", url);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.GET, null, String.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                String responseBody = response.getBody();
                log.debug("微信访问令牌响应: {}", responseBody);

                try {
                    // 尝试解析为JSON
                    return objectMapper.readValue(responseBody, WechatAccessTokenResponse.class);
                } catch (Exception e) {
                    log.error("解析微信访问令牌响应失败: {}", responseBody, e);
                    // 如果解析失败，创建一个错误响应
                    WechatAccessTokenResponse errorResponse = new WechatAccessTokenResponse();
                    errorResponse.setErrcode(-1);
                    errorResponse.setErrmsg("响应格式错误: " + responseBody);
                    return errorResponse;
                }
            } else {
                log.error("微信访问令牌请求失败: status={}, body={}", 
                    response.getStatusCode(), response.getBody());
                WechatAccessTokenResponse errorResponse = new WechatAccessTokenResponse();
                errorResponse.setErrcode(-1);
                errorResponse.setErrmsg("HTTP请求失败: " + response.getStatusCode());
                return errorResponse;
            }
        } catch (RestClientException e) {
            log.error("调用微信访问令牌API异常: {}", url, e);
            WechatAccessTokenResponse errorResponse = new WechatAccessTokenResponse();
            errorResponse.setErrcode(-1);
            errorResponse.setErrmsg("网络请求异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 通过访问令牌获取微信用户信息
     */
    private WechatUserInfoResponse getWechatUserInfoByToken(String accessToken, String openId) {
        String url = wechatProperties.getApi().getUserInfoUrl() + 
            "?access_token=" + accessToken +
            "&openid=" + openId +
            "&lang=zh_CN";

        log.debug("请求微信用户信息: {}", url);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.GET, null, String.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                String responseBody = response.getBody();
                log.debug("微信用户信息响应: {}", responseBody);

                try {
                    // 尝试解析为JSON
                    WechatUserInfoResponse userInfoResponse = objectMapper.readValue(responseBody, WechatUserInfoResponse.class);
                    log.debug("成功解析微信用户信息响应: openid={}, nickname={}", 
                        userInfoResponse.getOpenid(), userInfoResponse.getNickname());
                    return userInfoResponse;
                } catch (Exception e) {
                    log.error("解析微信用户信息响应失败: {}", responseBody, e);

                    // 检查响应是否包含错误信息（如果包含errcode字段，说明是微信API错误）
                    if (responseBody.contains("errcode")) {
                        // 这是微信API返回的错误响应，尝试解析错误信息
                        WechatUserInfoResponse errorResponse = new WechatUserInfoResponse();
                        try {
                            // 尝试从JSON中提取错误码和错误信息
                            if (responseBody.contains("\"errcode\"")) {
                                errorResponse.setErrcode(-1);
                                errorResponse.setErrmsg("微信API错误: " + responseBody);
                            }
                        } catch (Exception ex) {
                            errorResponse.setErrcode(-1);
                            errorResponse.setErrmsg("解析微信错误响应失败: " + responseBody);
                        }
                        return errorResponse;
                    } else {
                        // 响应看起来像是成功的用户信息，但解析失败了
                        // 这可能是由于JSON格式问题或DTO映射问题
                        log.warn("微信返回了看似成功的用户信息，但JSON解析失败。尝试宽松解析...");

                        // 创建一个成功的响应，手动提取关键信息
                        WechatUserInfoResponse successResponse = new WechatUserInfoResponse();
                        try {
                            // 使用简单的字符串解析提取关键信息
                            if (responseBody.contains("\"openid\"")) {
                                String openidValue = extractJsonValue(responseBody, "openid");
                                successResponse.setOpenid(openidValue);
                            }
                            if (responseBody.contains("\"nickname\"")) {
                                String nicknameValue = extractJsonValue(responseBody, "nickname");
                                successResponse.setNickname(nicknameValue);
                            }
                            if (responseBody.contains("\"headimgurl\"")) {
                                String avatarValue = extractJsonValue(responseBody, "headimgurl");
                                successResponse.setHeadimgurl(avatarValue);
                            }
                            if (responseBody.contains("\"unionid\"")) {
                                String unionidValue = extractJsonValue(responseBody, "unionid");
                                successResponse.setUnionid(unionidValue);
                            }
                            if (responseBody.contains("\"sex\"")) {
                                String sexValue = extractJsonValue(responseBody, "sex");
                                try {
                                    successResponse.setSex(Integer.parseInt(sexValue));
                                } catch (NumberFormatException nfe) {
                                    successResponse.setSex(0); // 默认未知
                                }
                            }

                            log.info("手动解析微信用户信息成功: openid={}, nickname={}", 
                                successResponse.getOpenid(), successResponse.getNickname());
                            return successResponse;
                        } catch (Exception ex) {
                            log.error("手动解析微信用户信息也失败了", ex);
                            WechatUserInfoResponse errorResponse = new WechatUserInfoResponse();
                            errorResponse.setErrcode(-1);
                            errorResponse.setErrmsg("响应格式错误: " + responseBody);
                            return errorResponse;
                        }
                    }
                }
            } else {
                log.error("微信用户信息请求失败: status={}, body={}", 
                    response.getStatusCode(), response.getBody());
                WechatUserInfoResponse errorResponse = new WechatUserInfoResponse();
                errorResponse.setErrcode(-1);
                errorResponse.setErrmsg("HTTP请求失败: " + response.getStatusCode());
                return errorResponse;
            }
        } catch (RestClientException e) {
            log.error("调用微信用户信息API异常: {}", url, e);
            WechatUserInfoResponse errorResponse = new WechatUserInfoResponse();
            errorResponse.setErrcode(-1);
            errorResponse.setErrmsg("网络请求异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 从JSON字符串中提取指定字段的值
     */
    private String extractJsonValue(String json, String fieldName) {
        try {
            String pattern = "\"" + fieldName + "\"\\s*:\\s*\"([^\"]*?)\"";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(json);
            if (m.find()) {
                return m.group(1);
            }

            // 尝试数字类型的字段
            String numberPattern = "\"" + fieldName + "\"\\s*:\\s*(\\d+)";
            java.util.regex.Pattern np = java.util.regex.Pattern.compile(numberPattern);
            java.util.regex.Matcher nm = np.matcher(json);
            if (nm.find()) {
                return nm.group(1);
            }
        } catch (Exception e) {
            log.warn("提取JSON字段 {} 失败: {}", fieldName, e.getMessage());
        }
        return null;
    }

    /**
     * 记录微信登录日志
     */
    private void recordWechatLoginLog(Long userId, String code, SessionRequest.DeviceInfo deviceInfo) {
        log.info("微信登录记录: userId={}, code={}, device={}", userId, code, deviceInfo);
    }

    /**
     * 创建需要绑定手机号的响应
     */
    private LoginResponse createPhoneBindRequiredResponse(WechatUserInfo wechatUserInfo, User user) {
        // 生成临时Token，包含微信信息，设置scope为wx_bind_phone
        String tempToken = generateTempToken(wechatUserInfo, user);

        // 构建微信用户信息
        LoginResponse.WxInfo wxInfo = LoginResponse.WxInfo.builder()
            .nickname(wechatUserInfo.getNickname())
            .avatar(wechatUserInfo.getAvatar())
            .openId(wechatUserInfo.getOpenId())
            .unionId(wechatUserInfo.getUnionId())
            .gender(wechatUserInfo.getGender())
            .build();

        // 构建用户信息（用于前端显示）
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setNickname(wechatUserInfo.getNickname());
        userInfo.setAvatar(wechatUserInfo.getAvatar());
        userInfo.setIsNewUser(user.getId() == null); // 如果没有ID说明是新用户
        userInfo.setNeedPhoneBind(true);
        userInfo.setPhoneBound(false);

        // 构建需要绑定手机号的响应
        LoginResponse response = LoginResponse.builder()
            .status("require_phone_bind")
            .tempToken(tempToken)
            .wxInfo(wxInfo)
            .tokenType("TempBearer")
            .expiresIn(1800L) // 30分钟有效期
            .user(userInfo)
            .build();

        log.info("返回需要绑定手机号响应: openId={}, nickname={}, status=require_phone_bind", 
            wechatUserInfo.getOpenId(), wechatUserInfo.getNickname());
        return response;
    }

    /**
     * 创建临时用户对象（不保存到数据库）
     */
    private User createTempUserWithWechat(WechatUserInfo wechatUserInfo) {
        // 为微信用户生成默认密码哈希和盐值
        String defaultPassword = "WECHAT_USER_NO_PASSWORD";
        String salt = generateSalt();
        String passwordHash = passwordEncoder.encode(defaultPassword + salt);

        User tempUser = User.builder()
            .username(generateUsernameFromWechat(wechatUserInfo.getOpenId()))
            .nickname(wechatUserInfo.getNickname() != null ? wechatUserInfo.getNickname() : generateRandomNickname())
            .avatar(wechatUserInfo.getAvatar() != null ? wechatUserInfo.getAvatar() : generateDefaultAvatar())
            .gender(wechatUserInfo.getGender() != null ? wechatUserInfo.getGender() : 0)
            .passwordHash(passwordHash)
            .salt(salt)
            .wechatOpenId(wechatUserInfo.getOpenId())
            .wechatUnionId(wechatUserInfo.getUnionId())
            .level(0) // 默认等级0
            .exp(0) // 默认经验值0
            .coins(0.0) // 默认硬币0
            .vipType(0) // 默认非VIP
            .vipStatus(0) // 默认非VIP状态
            .status(1) // 默认正常状态
            .isVerified(false) // 默认未认证
            .verifiedType(0) // 默认无认证类型
            .phoneBound(false) // 临时用户未绑定手机号
            .realNameVerified(0) // 默认未实名认证
            .build();

        log.info("创建临时微信用户对象: openId={}, username={}, nickname={}", 
            wechatUserInfo.getOpenId(), tempUser.getUsername(), tempUser.getNickname());
        return tempUser;
    }

    /**
     * 生成临时Token，用于微信绑定手机号流程
     */
    private String generateTempToken(WechatUserInfo wechatUserInfo, User user) {
        try {
            // 创建临时token的payload
            String payload = objectMapper.writeValueAsString(Map.of(
                "scope", "wx_bind_phone",
                "openid", wechatUserInfo.getOpenId(),
                "unionid", wechatUserInfo.getUnionId() != null ? wechatUserInfo.getUnionId() : "",
                "nickname", wechatUserInfo.getNickname() != null ? wechatUserInfo.getNickname() : "",
                "avatar", wechatUserInfo.getAvatar() != null ? wechatUserInfo.getAvatar() : "",
                "gender", wechatUserInfo.getGender() != null ? wechatUserInfo.getGender() : 0,
                "username", user.getUsername(),
                "exp", System.currentTimeMillis() + 1800000, // 30分钟过期
                "iat", System.currentTimeMillis()
            ));

            // 使用JWT工具生成临时token（这里简化处理，实际应该用专门的临时token生成方法）
            return jwtUtil.generateAccessToken(-1L, "temp_" + wechatUserInfo.getOpenId());
        } catch (Exception e) {
            log.error("生成临时Token失败", e);
            throw new ServiceException("生成临时Token失败");
        }
    }

    /**
     * 转换设备信息格式
     */
    private CodeLoginRequest.DeviceInfo convertDeviceInfo(SessionRequest.DeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            return null;
        }
        CodeLoginRequest.DeviceInfo result = new CodeLoginRequest.DeviceInfo();
        result.setDeviceType(deviceInfo.getDeviceType());
        result.setDeviceId(deviceInfo.getDeviceId());
        result.setIpAddress(deviceInfo.getIpAddress());
        result.setUserAgent(deviceInfo.getUserAgent());
        return result;
    }

    /**
     * 转换设备信息格式到LoginRequest.DeviceInfo
     */
    private LoginRequest.DeviceInfo convertToLoginDeviceInfo(SessionRequest.DeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            return null;
        }
        LoginRequest.DeviceInfo result = new LoginRequest.DeviceInfo();
        // 映射相应的字段
        result.setDeviceId(deviceInfo.getDeviceId());
        result.setUserAgent(deviceInfo.getUserAgent());
        // 设置默认值或转换类型
        result.setDeviceType("browser"); // 默认设备类型
        result.setPlatform("web"); // 默认平台
        return result;
    }

    /**
     * 微信用户信息内部类
     */
    private static class WechatUserInfo {
        private String openId;
        private String unionId;
        private String nickname;
        private String avatar;
        private Integer gender;

        public String getOpenId() { return openId; }
        public void setOpenId(String openId) { this.openId = openId; }

        public String getUnionId() { return unionId; }
        public void setUnionId(String unionId) { this.unionId = unionId; }

        public String getNickname() { return nickname; }
        public void setNickname(String nickname) { this.nickname = nickname; }

        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }

        public Integer getGender() { return gender; }
        public void setGender(Integer gender) { this.gender = gender; }
    }

    /**
     * 获取当前请求的User-Agent
     */
    private String getCurrentUserAgent() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return request.getHeader("User-Agent");
            }
        } catch (Exception e) {
            log.warn("获取User-Agent失败", e);
        }
        return null;
    }

    /**
     * 获取当前请求的IP地址
     */
    private String getCurrentIpAddress() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                // 尝试从各种代理头中获取真实IP
                String ip = request.getHeader("X-Forwarded-For");
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("Proxy-Client-IP");
                }
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("WL-Proxy-Client-IP");
                }
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("HTTP_CLIENT_IP");
                }
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("HTTP_X_FORWARDED_FOR");
                }
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getRemoteAddr();
                }

                // 如果是多个IP，取第一个
                if (ip != null && ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }

                return ip;
            }
        } catch (Exception e) {
            log.warn("获取IP地址失败", e);
        }
        return "unknown";
    }

    /**
     * 获取用户VIP信息
     */
    private VipInfo getVipInfo(Long uid) {
        try {
            // 查询用户VIP信息
            User user = userService.findById(uid);
            if (user == null) {
                return VipInfo.defaultVip();
            }

            // 从用户表中获取VIP相关字段
            Integer vipType = user.getVipType();
            LocalDateTime vipDueDate = user.getVipDueDate();
            Integer vipStatus = user.getVipStatus();

            // 判断VIP是否有效
            boolean isVipValid = vipStatus != null && vipStatus == 1 &&
                               vipDueDate != null && vipDueDate.isAfter(LocalDateTime.now());

            return VipInfo.builder()
                    .isVip(isVipValid)
                    .vipType(vipType != null ? vipType : 0)
                    .vipStatus(vipStatus != null ? vipStatus : 0)
                    .vipEndTime(vipDueDate != null ? vipDueDate.toEpochSecond(java.time.ZoneOffset.UTC) : null)
                    .autoRenew(false) // TODO: 从用户表或VIP表获取自动续费状态
                    .vipLevel(vipType != null ? vipType : 0)
                    .build();
        } catch (Exception e) {
            log.warn("获取用户VIP信息失败: uid={}, error={}", uid, e.getMessage());
            return VipInfo.defaultVip();
        }
    }
}
