package com.bilibili.auth.service;

/**
 * 短信服务接口
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface SmsService {

    /**
     * 发送验证码短信
     *
     * @param phone 手机号
     * @param code 验证码
     * @param templateType 模板类型 (login/register)
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String phone, String code, String templateType);

    /**
     * 发送登录验证码
     *
     * @param phone 手机号
     * @param code 验证码
     * @return 是否发送成功
     */
    boolean sendLoginCode(String phone, String code);

    /**
     * 发送注册验证码
     *
     * @param phone 手机号
     * @param code 验证码
     * @return 是否发送成功
     */
    boolean sendRegisterCode(String phone, String code);
}
