package com.bilibili.auth.service.impl;

import com.bilibili.auth.config.SmsProperties;
import com.bilibili.auth.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;

/**
 * 阿里云短信服务实现 - 使用新版SDK
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class
SmsServiceImpl implements SmsService {

    private final SmsProperties smsProperties;

    /**
     * 创建阿里云短信客户端
     */
    private Client createClient() throws Exception {
        Config config = new Config()
            .setAccessKeyId(smsProperties.getAliyun().getAccessKeyId())
            .setAccessKeySecret(smsProperties.getAliyun().getAccessKeySecret());

        // 设置访问域名
        config.endpoint = smsProperties.getAliyun().getEndpoint();

        return new Client(config);
    }

    @Override
    public boolean sendVerificationCode(String phone, String code, String templateType) {
        try {
            log.info("发送短信验证码: phone={}, code={}, type={}", phone, code, templateType);

            // 创建阿里云客户端
            Client client = createClient();

            // 构建短信请求
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phone)
                .setSignName(smsProperties.getAliyun().getSignName())
                .setTemplateCode(getTemplateCode(templateType))
                .setTemplateParam("{\"code\":\"" + code + "\"}");

            // 发送短信
            SendSmsResponse response = client.sendSmsWithOptions(sendSmsRequest, new RuntimeOptions());

            if ("OK".equals(response.getBody().getCode())) {
                log.info("短信发送成功: phone={}, code={}, bizId={}", phone, code, response.getBody().getBizId());
                return true;
            } else {
                log.error("短信发送失败: phone={}, code={}, code={}, message={}",
                    phone, code, response.getBody().getCode(), response.getBody().getMessage());
                return false;
            }

        } catch (TeaException error) {
            log.error("阿里云短信服务异常: phone={}, code={}, message={}", phone, code, error.getMessage());
            // 打印诊断地址
            if (error.getData() != null && error.getData().get("Recommend") != null) {
                log.error("诊断地址: {}", error.getData().get("Recommend"));
            }
            return false;
        } catch (Exception e) {
            log.error("发送短信验证码异常: phone={}, code={}", phone, code, e);
            return false;
        }
    }

    @Override
    public boolean sendLoginCode(String phone, String code) {
        return sendVerificationCode(phone, code, "login");
    }

    @Override
    public boolean sendRegisterCode(String phone, String code) {
        return sendVerificationCode(phone, code, "register");
    }

    /**
     * 根据模板类型获取模板���码
     */
    private String getTemplateCode(String templateType) {
        switch (templateType) {
            case "login":
            case "register":
                // 登录和注册都使用同一个模板
                return smsProperties.getAliyun().getTemplates().getLogin();
            default:
                log.warn("未知的短信模板类型: {}", templateType);
                return smsProperties.getAliyun().getTemplates().getLogin();
        }
    }
}
