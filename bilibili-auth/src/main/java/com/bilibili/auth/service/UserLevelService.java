package com.bilibili.auth.service;

import com.bilibili.auth.entity.UserLevel;

/**
 * 用户认证服务接口
 */
public interface UserLevelService {

    /**
     * 根据经验值计算用户等级
     */
    UserLevel calculateLevel(Integer exp);

    /**
     * 更新用户经验值
     */
    void updateUserExp(Long uid, Integer exp, String reason);

    /**
     * 获取等级配置
     */
    UserLevel getLevelConfig(Integer level);

    /**
     * 检查用户是否升级
     */
    boolean checkLevelUp(Long uid, Integer newExp);

    /**
     * 处理用户升级
     */
    void handleLevelUp(Long uid, UserLevel newLevel);
}
