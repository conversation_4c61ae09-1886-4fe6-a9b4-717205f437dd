package com.bilibili.auth.service;

import com.bilibili.auth.dto.*;

import com.bilibili.auth.entity.User;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest request);

    /**
     * 短信登录
     */
    LoginResponse smsLogin(String phone, String code, LoginRequest.DeviceInfo deviceInfo);

    /**
     * 验证码登录（统一方法）
     */
    LoginResponse codeLogin(CodeLoginRequest request);



    /**
     * 用户注册
     */
    RegisterResponse register(RegisterRequest request);

    /**
     * 刷新令牌
     */
    LoginResponse refreshToken(String refreshToken);

    /**
     * 用户登出
     */
    void logout(String token);

    /**
     * 发送验证码
     */
    void sendCode(SendCodeRequest request);



    /**
     * 获取当前用户信息
     */
    User getCurrentUser();

    /**
     * 根据token获取用户ID
     */
    Long getUserIdFromToken(String token);



    /**
     * 重置密码
     */
    void resetPassword(String account, String code, String newPassword);

    /**
     * 验证访问令牌
     */
    User validateAccessToken(String token);

    /**
     * 微信登录
     */
    LoginResponse wechatLogin(String code, String state, SessionRequest.DeviceInfo deviceInfo);

    /**
     * 创建会话（统一登录接口）
     */
    LoginResponse createSession(SessionRequest request);

    /**
     * 微信绑定手机号完成注册
     */
    LoginResponse wechatBindPhone(String tempToken, String phone, String code);
}
