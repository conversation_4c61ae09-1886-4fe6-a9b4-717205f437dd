package com.bilibili.auth.service;

/**
 * 验证码服务接口
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface VerificationCodeService {
    
    /**
     * 发送验证码
     *
     * @param phone 手机号
     * @param type 验证码类型（login/register）
     * @return 是否发送成功
     */
    boolean sendCode(String phone, String type);
    
    /**
     * 验证验证码
     *
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型
     * @return 是否验证成功
     */
    boolean verifyCode(String phone, String code, String type);
    
    /**
     * 生成验证码
     *
     * @return 验证码
     */
    String generateCode();
    
    /**
     * 检查是否可以发送验证码（频率限制）
     *
     * @param phone 手机号
     * @return 是否可以发送
     */
    boolean canSendCode(String phone);
    
    /**
     * 获取距离下次可发送的剩余时间（秒）
     *
     * @param phone 手机号
     * @return 剩余时间，0表示可以立即发送
     */
    long getRemainingSendInterval(String phone);
}
