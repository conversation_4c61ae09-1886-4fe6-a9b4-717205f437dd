package com.bilibili.auth.service.impl;

import com.bilibili.auth.entity.User;
import com.bilibili.auth.security.CustomUserDetails;
import com.bilibili.auth.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 自定义用户详情服务
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomUserDetailsService implements UserDetailsService {

    private final UserService userService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("加载用户详情: {}", username);

        User user = null;

        // 尝试不同的查询方式
        if (username.contains("@")) {
            // 邮箱登录
            user = userService.getUserByEmail(username);
        } else if (username.matches("^1[3-9]\\d{9}$")) {
            // 手机号登录
            user = userService.getUserByPhone(username);
        } else {
            // 用户名登录
            user = userService.getUserByUsername(username);
        }

        if (user == null) {
            log.warn("用户不存在: {}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        if (user.getStatus() == 0) {
            log.warn("用户已被禁用: {}", username);
            throw new UsernameNotFoundException("用户已被禁用: " + username);
        }

        if (user.getStatus() == 2) {
            log.warn("用户已被封禁: {}", username);
            throw new UsernameNotFoundException("用户已被封禁: " + username);
        }

        log.debug("成功加载用户详情: uid={}, username={}", user.getUid(), user.getUsername());
        return new CustomUserDetails(user);
    }
}
