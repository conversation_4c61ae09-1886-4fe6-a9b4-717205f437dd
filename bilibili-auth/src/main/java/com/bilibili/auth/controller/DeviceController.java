package com.bilibili.auth.controller;


import com.bilibili.common.response.ApiResponse;
import com.bilibili.auth.dto.UserDeviceResponse;
import com.bilibili.auth.entity.User;
import com.bilibili.auth.entity.UserLoginDevice;
import com.bilibili.auth.service.AuthService;
import com.bilibili.auth.service.UserLoginDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备管理控制器
 * 提供用户登录设备的管理功能
 */
@Slf4j
@RestController
@RequestMapping("/device")
@RequiredArgsConstructor
@Tag(name = "设备管理", description = "用户登录设备管理相关接口")
public class DeviceController {

    private final UserLoginDeviceService userLoginDeviceService;
    private final AuthService authService;

    @GetMapping("/list")
    @Operation(summary = "获取用户在线设备列表", description = "获取当前用户的所有在线设备")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<List<UserDeviceResponse>> getUserDevices() {
        User currentUser = authService.getCurrentUser();
        List<UserLoginDevice> devices = userLoginDeviceService.getUserOnlineDevices(currentUser.getUid());

        // 转换为响应DTO
        List<UserDeviceResponse> deviceResponses = devices.stream()
                .map(this::convertToDeviceResponse)
                .toList();

        return ApiResponse.success(deviceResponses);
    }

    @PostMapping("/trust/{tokenId}")
    @Operation(summary = "信任设备", description = "将指定设备设为信任设备")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<String> trustDevice(
            @Parameter(description = "设备Token ID") @PathVariable String tokenId) {

        // 验证设备是否属于当前用户
        UserLoginDevice device = userLoginDeviceService.getDeviceByTokenId(tokenId);
        User currentUser = authService.getCurrentUser();

        if (device == null || !device.getUid().equals(currentUser.getUid())) {
            return ApiResponse.error(40003, "设备不存在或无权限操作");
        }

        userLoginDeviceService.trustDevice(tokenId);
        return ApiResponse.success("设备已设为信任");
    }

    @PostMapping("/untrust/{tokenId}")
    @Operation(summary = "取消信任设备", description = "取消指定设备的信任状态")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<String> untrustDevice(
            @Parameter(description = "设备Token ID") @PathVariable String tokenId) {

        // 验证设备是否属于当前用户
        UserLoginDevice device = userLoginDeviceService.getDeviceByTokenId(tokenId);
        User currentUser = authService.getCurrentUser();

        if (device == null || !device.getUid().equals(currentUser.getUid())) {
            return ApiResponse.error(40003, "设备不存在或无权限操作");
        }

        userLoginDeviceService.untrustDevice(tokenId);
        return ApiResponse.success("设备信任已取消");
    }

    @PostMapping("/offline/{tokenId}")
    @Operation(summary = "下线设备", description = "强制下线指定设备")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<String> offlineDevice(
            @Parameter(description = "设备Token ID") @PathVariable String tokenId) {

        // 验证设备是否属于当前用户
        UserLoginDevice device = userLoginDeviceService.getDeviceByTokenId(tokenId);
        User currentUser = authService.getCurrentUser();

        if (device == null || !device.getUid().equals(currentUser.getUid())) {
            return ApiResponse.error(40003, "设备不存在或无权限操作");
        }

        userLoginDeviceService.offlineDevice(tokenId);
        return ApiResponse.success("设备已下线");
    }

    @PostMapping("/offline-all")
    @Operation(summary = "下线所有设备", description = "下线当前用户的所有设备（除当前设备外）")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<String> offlineAllDevices(
            @Parameter(description = "当前设备Token ID（可选）") @RequestParam(required = false) String currentTokenId) {

        User currentUser = authService.getCurrentUser();

        if (currentTokenId != null) {
            // 下线除当前设备外的所有设备
            List<UserLoginDevice> devices = userLoginDeviceService.getUserOnlineDevices(currentUser.getUid());
            devices.stream()
                    .filter(device -> !device.getTokenId().equals(currentTokenId))
                    .forEach(device -> userLoginDeviceService.offlineDevice(device.getTokenId()));
        } else {
            // 下线所有设备
            userLoginDeviceService.offlineAllDevices(currentUser.getUid());
        }

        return ApiResponse.success("所有设备已下线");
    }

    @GetMapping("/check/{tokenId}")
    @Operation(summary = "检查设备状态", description = "检查指定设备是否在线")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> checkDeviceStatus(
            @Parameter(description = "设备Token ID") @PathVariable String tokenId) {

        boolean isOnline = userLoginDeviceService.isDeviceOnline(tokenId);
        return ApiResponse.success(isOnline);
    }

    /**
     * 转换设备实体为响应DTO
     */
    private UserDeviceResponse convertToDeviceResponse(UserLoginDevice device) {
        return UserDeviceResponse.builder()
                .id(device.getId())
                .deviceFingerprint(device.getDeviceFingerprint())
                .deviceName(device.getDeviceName())
                .deviceType(device.getDeviceType())
                .operatingSystem(device.getOperatingSystem())
                .browserOrApp(device.getBrowserOrApp())
                .ipAddress(device.getIpAddress())
                .location(device.getLocation())
                .tokenId(device.getTokenId())
                .firstLoginTime(device.getFirstLoginTime())
                .lastAccessTime(device.getLastAccessTime())
                .loginCount(device.getLoginCount())
                .isOnline(device.getIsOnline())
                .isTrusted(device.getIsTrusted())
                .isCurrent(false) // 这里可以根据当前请求的tokenId来判断
                .createdAt(device.getCreatedAt())
                .build();
    }
}
