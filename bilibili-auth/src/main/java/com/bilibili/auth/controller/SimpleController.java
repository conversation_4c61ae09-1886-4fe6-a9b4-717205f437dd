package com.bilibili.auth.controller;

import com.bilibili.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 简单测试控制器
 */
@Tag(name = "系统信息", description = "系统信息相关接口")
@RestController
public class SimpleController {

    @Operation(summary = "获取服务信息", description = "获取认证服务的基本信息")
    @GetMapping("/api/v1/auth/info")
    public ApiResponse<Map<String, Object>> home() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "🎬 Bilibili认证服务启动成功！");
        result.put("service", "bilibili-auth-service");
        result.put("version", "1.0.0");
        result.put("status", "running");
        return ApiResponse.success("服务运行正常", result);
    }

    @Operation(summary = "获取详细信息", description = "获取认证服务的详细信息")
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> info() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "bilibili-auth-service");
        result.put("version", "1.0.0");
        result.put("description", "Bilibili用户认证和授权服务");
        return ApiResponse.success("服务信息获取成功", result);
    }
}
