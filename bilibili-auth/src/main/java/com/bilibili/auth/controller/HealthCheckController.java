package com.bilibili.auth.controller;

import com.bilibili.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Slf4j
@RestController
@RequestMapping("/auth/health")
@RequiredArgsConstructor
@Tag(name = "健康检查", description = "系统健康状态检查接口")
public class HealthCheckController {

    @Operation(summary = "健康检查", description = "检查服务是否正常运行")
    @GetMapping
    public ResponseEntity<ApiResponse<Map<String, Object>>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "bilibili-auth-service");
        health.put("version", "1.0.0");

        return ResponseEntity.ok(ApiResponse.success("服务健康检查通过", health));
    }

    @Operation(summary = "就绪检查", description = "检查服务是否就绪")
    @GetMapping("/ready")
    public ResponseEntity<ApiResponse<Map<String, Object>>> ready() {
        Map<String, Object> ready = new HashMap<>();
        ready.put("status", "READY");
        ready.put("database", "connected");
        ready.put("redis", "connected");

        return ResponseEntity.ok(ApiResponse.success("服务就绪检查通过", ready));
    }

    @Operation(summary = "存活检查", description = "检查服务是否存活")
    @GetMapping("/live")
    public ResponseEntity<ApiResponse<Map<String, Object>>> live() {
        Map<String, Object> live = new HashMap<>();
        live.put("status", "ALIVE");
        live.put("uptime", System.currentTimeMillis());

        return ResponseEntity.ok(ApiResponse.success("服务存活检查通过", live));
    }
}
