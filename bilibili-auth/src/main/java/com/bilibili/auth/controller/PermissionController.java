package com.bilibili.auth.controller;


import com.bilibili.common.response.ApiResponse;
import com.bilibili.auth.entity.User;
import com.bilibili.auth.enums.UserPermission;
import com.bilibili.auth.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 权限管理控制器
 * 提供用户权限查询和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/permission")
@RequiredArgsConstructor
@Tag(name = "权限管理", description = "用户权限管理相关接口")
public class PermissionController {

    private final AuthService authService;

    @GetMapping("/current")
    @Operation(summary = "获取当前用户权限", description = "获取当前登录用户的权限信息")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Map<String, Object>> getCurrentUserPermission() {
        User currentUser = authService.getCurrentUser();
        UserPermission permission = currentUser.getUserPermission();
        
        Map<String, Object> permissionInfo = new HashMap<>();
        permissionInfo.put("permission", permission.getCode());
        permissionInfo.put("permissionName", permission.getName());
        permissionInfo.put("description", permission.getDescription());
        permissionInfo.put("isVip", permission.isVip());
        permissionInfo.put("isNormal", permission.isNormal());
        
        // VIP相关信息
        if (currentUser.getVipStatus() == 1) {
            permissionInfo.put("vipType", currentUser.getVipType());
            permissionInfo.put("vipStatus", currentUser.getVipStatus());
            permissionInfo.put("vipDueDate", currentUser.getVipDueDate());
            permissionInfo.put("vipValid", currentUser.isVipValid());
            
            // 计算VIP剩余天数
            if (currentUser.getVipDueDate() != null) {
                long remainingDays = java.time.Duration.between(
                    LocalDateTime.now(), 
                    currentUser.getVipDueDate()
                ).toDays();
                permissionInfo.put("vipRemainingDays", Math.max(0, remainingDays));
            }
        }
        
        return ApiResponse.success(permissionInfo);
    }

    @GetMapping("/check/vip")
    @Operation(summary = "检查VIP权限", description = "检查当前用户是否为有效VIP")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Map<String, Object>> checkVipPermission() {
        User currentUser = authService.getCurrentUser();
        
        Map<String, Object> vipInfo = new HashMap<>();
        vipInfo.put("isVip", currentUser.isVipUser());
        vipInfo.put("isVipValid", currentUser.isVipValid());
        vipInfo.put("vipType", currentUser.getVipType());
        vipInfo.put("vipStatus", currentUser.getVipStatus());
        
        if (currentUser.getVipDueDate() != null) {
            vipInfo.put("vipDueDate", currentUser.getVipDueDate());
            vipInfo.put("isExpired", currentUser.getVipDueDate().isBefore(LocalDateTime.now()));
        }
        
        return ApiResponse.success(vipInfo);
    }

    @GetMapping("/features")
    @Operation(summary = "获取用户可用功能", description = "根据用户权限获取可用功能列表")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Map<String, Object>> getUserFeatures() {
        User currentUser = authService.getCurrentUser();
        UserPermission permission = currentUser.getUserPermission();
        
        Map<String, Object> features = new HashMap<>();
        
        // 基础功能（所有用户都有）
        features.put("basicVideo", true);
        features.put("basicComment", true);
        features.put("basicLike", true);
        features.put("basicShare", true);
        
        // VIP专享功能
        if (permission.isVip() && currentUser.isVipValid()) {
            features.put("hdVideo", true);           // 高清视频
            features.put("noAds", true);             // 无广告
            features.put("downloadVideo", true);     // 视频下载
            features.put("advancedSearch", true);    // 高级搜索
            features.put("prioritySupport", true);   // 优先客服
            features.put("exclusiveContent", true);  // 专享内容
            features.put("customTheme", true);       // 自定义主题
            features.put("multipleDevices", 5);      // 多设备登录（5台）
        } else {
            features.put("hdVideo", false);
            features.put("noAds", false);
            features.put("downloadVideo", false);
            features.put("advancedSearch", false);
            features.put("prioritySupport", false);
            features.put("exclusiveContent", false);
            features.put("customTheme", false);
            features.put("multipleDevices", 3);      // 普通用户限制3台设备
        }
        
        return ApiResponse.success(features);
    }

    @PostMapping("/require-vip")
    @Operation(summary = "检查VIP权限要求", description = "检查当前用户是否满足VIP权限要求")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Map<String, Object>> requireVipPermission() {
        User currentUser = authService.getCurrentUser();
        
        Map<String, Object> result = new HashMap<>();
        boolean hasVipPermission = currentUser.isVipUser() && currentUser.isVipValid();
        
        result.put("hasPermission", hasVipPermission);
        result.put("permissionType", currentUser.getUserPermission().getCode());
        
        if (!hasVipPermission) {
            result.put("message", "此功能需要大会员权限");
            result.put("upgradeUrl", "/vip/upgrade");
        }
        
        return ApiResponse.success(result);
    }
}
