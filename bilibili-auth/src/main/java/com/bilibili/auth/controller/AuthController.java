package com.bilibili.auth.controller;

import com.bilibili.auth.dto.*;
import com.bilibili.common.response.ApiResponse;
import com.bilibili.auth.entity.User;
import com.bilibili.auth.service.AuthService;
import com.bilibili.auth.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

/**
 * 用户认证控制器
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Slf4j
@RestController
@RequestMapping("/auth")  // 恢复原来的路径，因为网关会进行路径重写
@RequiredArgsConstructor
@Tag(name = "用户认证", description = "用户登录、注册、注销等认证相关接口")
public class AuthController {

    private final AuthService authService;
    private final UserService userService;
    private final PasswordEncoder passwordEncoder;

    @Operation(summary = "验证码登录", description = "使用手机号+验证码登录，如果用户不存在则自动创建")
    @ApiResponses({
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "登录成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "验证码错误或已过期"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "429", description = "请求过于频繁")
    })
    @PostMapping("/login-by-code") // URL变更，更清晰
    public ResponseEntity<ApiResponse<LoginResponse>> codeLogin(@Valid @RequestBody CodeLoginRequest request) {
        log.info("验证码登录请求: {}", request.getPhone());
        LoginResponse response = authService.codeLogin(request);
        return ResponseEntity.ok()
                .header("Authorization", "Bearer " + response.getAccessToken())
                .body(ApiResponse.success("登录成功", response));
    }

    @Operation(summary = "发送验证码", description = "发送手机验证码，用于登录或注册")
    @ApiResponses({
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "验证码发送成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "手机号格式错误"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "429", description = "发送过于频繁")
    })
    @PostMapping("/send-code")
    public ResponseEntity<ApiResponse<Void>> sendCode(@Valid @RequestBody SendCodeRequest request) {
        log.info("发送验证码请求: phone={}", request.getPhone());
        authService.sendCode(request);
        return ResponseEntity.ok(ApiResponse.success("验证码发送成功", null));
    }

    @Operation(summary = "账号密码登录", description = "支持用户名/手机号+密码登录（用户需已设置密码）")
    @ApiResponses({
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "登录成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "用户名或密码错误"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "423", description = "账户被锁定")
    })
    @PostMapping("/login-by-password")
    public ResponseEntity<ApiResponse<LoginResponse>> passwordLogin(@Valid @RequestBody LoginRequest request) {
        log.info("密码登录请求: {}", request.getAccount());
        LoginResponse response = authService.login(request);
        return ResponseEntity.ok()
                .header("Authorization", "Bearer " + response.getAccessToken())
                .body(ApiResponse.success("登录成功", response));
    }

    @Operation(summary = "刷新令牌", description = "使用刷新令牌获取新的访问令牌")
    @PostMapping("/refresh-token")
    public ResponseEntity<ApiResponse<LoginResponse>> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        log.info("刷新令牌请求");
        LoginResponse response = authService.refreshToken(request.getRefreshToken());
        return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", response));
    }

    @Operation(summary = "用户注销", description = "用户退出登录，使当前Token失效")
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Void>> logout(@RequestHeader("Authorization") String authorization) {
        log.info("用户注销请求");
        String token = authorization.substring(7); // 移除 "Bearer " 前缀
        authService.logout(token);
        return ResponseEntity.ok(ApiResponse.success("注销成功", null));
    }







    @Operation(summary = "重置密码", description = "通过验证码重置密码")
    @PostMapping("/password/reset")
    public ResponseEntity<ApiResponse<Void>> resetPassword(
            @RequestParam String account,
            @RequestParam String code,
            @RequestParam String newPassword) {
        log.info("重置密码: {}", account);
        authService.resetPassword(account, code, newPassword);
        return ResponseEntity.ok(ApiResponse.success("密码重置成功", null));
    }

    @Operation(summary = "创建会话", description = "统一登录接口，支持手机���、微信、邮箱等多种登录方式")
    @ApiResponses({
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "登录成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "认证失败")
    })
    @PostMapping("/sessions")
    public ResponseEntity<ApiResponse<LoginResponse>> createSession(@Valid @RequestBody SessionRequest request) {
        log.info("创建会话请求: type={}", request.getType());
        LoginResponse response = authService.createSession(request);
        return ResponseEntity.ok()
                .header("Authorization", "Bearer " + response.getAccessToken())
                .body(ApiResponse.success("登录成功", response));
    }

    @Operation(summary = "微信绑定手机号完成注册", description = "微信用户绑定手机号完成注册流程")
    @PostMapping("/wechat/bind-phone")
    public ResponseEntity<ApiResponse<LoginResponse>> wechatBindPhone(
            @Parameter(description = "临时JWT令牌") @RequestHeader("Authorization") String tempToken,
            @Parameter(description = "手机号") @RequestParam String phone,
            @Parameter(description = "验证码") @RequestParam String code) {
        log.info("微信绑定手机号请求: phone={}", phone);

        LoginResponse response = authService.wechatBindPhone(tempToken, phone, code);

        return ResponseEntity.ok()
                .header("Authorization", "Bearer " + response.getAccessToken())
                .body(ApiResponse.success("绑定成功，注册完成", response));
    }

    @Operation(summary = "验证访问令牌", description = "验证JWT访问令牌的有效性，供其他服务调用")
    @PostMapping("/validate-token")
    public ResponseEntity<ApiResponse<User>> validateToken(@RequestParam String token) {
        log.info("验证令牌请求");

        User user = authService.validateAccessToken(token);

        return ResponseEntity.ok(ApiResponse.success("令牌验证成功", user));
    }

    @Operation(summary = "获取当前用户信息", description = "根据JWT令牌获取当前用户信息")
    @GetMapping("/current-user")
    public ResponseEntity<ApiResponse<User>> getCurrentUser(@RequestHeader("Authorization") String authHeader) {
        log.info("获取当前用户信息");

        String token = authHeader.startsWith("Bearer ") ? authHeader.substring(7) : authHeader;
        User user = authService.validateAccessToken(token);

        return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", user));
    }

}
