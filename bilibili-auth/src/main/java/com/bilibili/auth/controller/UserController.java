package com.bilibili.auth.controller;
import com.bilibili.common.response.ApiResponse;
import com.bilibili.auth.entity.User;
import com.bilibili.auth.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

/**
 * 用户管理控制器
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Tag(name = "用户管理", description = "用户信息管理相关接口")
public class UserController {

    private final UserService userService;

    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/me")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<User>> getCurrentUser(@AuthenticationPrincipal UserDetails userDetails) {
        log.info("获取当前用户信息: {}", userDetails.getUsername());

        User user = userService.getUserByUsername(userDetails.getUsername());
        if (user == null) {
            return ResponseEntity.status(404).body(ApiResponse.error(404, "用户不存在"));
        }

        return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", user));
    }

    @Operation(summary = "根据UID获取用户信息", description = "根据用户UID获取用户公开信息")
    @GetMapping("/{uid}")
    public ResponseEntity<ApiResponse<User>> getUserByUid(
            @Parameter(description = "用户UID", required = true) @PathVariable Long uid) {
        log.info("获取用户信息: uid={}", uid);

        User user = userService.getUserByUid(uid);
        if (user == null) {
            return ResponseEntity.status(404).body(ApiResponse.error(404, "用户不存在"));
        }

        // 只返回公开信息，隐藏敏感字段
        user.setPasswordHash(null);
        user.setSalt(null);
        user.setEmail(null);
        user.setPhone(null);

        return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", user));
    }

    @Operation(summary = "更新用户信息", description = "更新当前用户的基本信息")
    @PutMapping("/me")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<User>> updateUserInfo(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestBody UpdateUserRequest request) {
        log.info("更新用户信息: {}", userDetails.getUsername());

        User user = userService.getUserByUsername(userDetails.getUsername());
        if (user == null) {
            return ResponseEntity.status(404).body(ApiResponse.error(404, "用户不存在"));
        }

        // 更新允许修改的字段
        if (request.getNickname() != null) {
            user.setNickname(request.getNickname());
        }
        if (request.getSignature() != null) {
            user.setSignature(request.getSignature());
        }
        if (request.getGender() != null) {
            user.setGender(request.getGender());
        }
        if (request.getBirthday() != null) {
            user.setBirthday(request.getBirthday());
        }
        if (request.getLocation() != null) {
            user.setLocation(request.getLocation());
        }

        boolean updated = userService.updateUserInfo(user);
        if (updated) {
            return ResponseEntity.ok(ApiResponse.success("用户信息更新成功", user));
        } else {
            return ResponseEntity.status(500).body(ApiResponse.error(500, "更新失败"));
        }
    }

    @Operation(summary = "获取用户统计信息", description = "获取用户的统计数据")
    @GetMapping("/{uid}/stats")
    public ResponseEntity<ApiResponse<Object>> getUserStats(
            @Parameter(description = "用户UID", required = true) @PathVariable Long uid) {
        log.info("获取用户统计信息: uid={}", uid);

        var stats = userService.getUserStats(uid);
        if (stats == null) {
            return ResponseEntity.status(404).body(ApiResponse.error(404, "用户统计信息不存在"));
        }

        return ResponseEntity.ok(ApiResponse.success("获取用户统计信息成功", stats));
    }

    /**
     * 更新用户信息请求DTO
     */
    public static class UpdateUserRequest {
        private String nickname;
        private String signature;
        private Integer gender;
        private java.time.LocalDate birthday;
        private String location;

        // Getters and setters
        public String getNickname() { return nickname; }
        public void setNickname(String nickname) { this.nickname = nickname; }

        public String getSignature() { return signature; }
        public void setSignature(String signature) { this.signature = signature; }

        public Integer getGender() { return gender; }
        public void setGender(Integer gender) { this.gender = gender; }

        public java.time.LocalDate getBirthday() { return birthday; }
        public void setBirthday(java.time.LocalDate birthday) { this.birthday = birthday; }

        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
    }
}
