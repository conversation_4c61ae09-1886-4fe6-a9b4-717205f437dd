package com.bilibili.auth;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Bilibili用户认证和授权服务启动类
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@SpringBootApplication
@MapperScan("com.bilibili.auth.mapper")
public class BilibiliAuthApplication {

    public static void main(String[] args) {
        // 先显示图案
        printBilibiliLogo();

        try {
            SpringApplication.run(BilibiliAuthApplication.class, args);

            // 启动成功后的信息
            printSuccessInfo();

        } catch (Exception e) {
            System.out.println();
            System.out.println("\033[31m           ❌ 启动失败 ❌\033[0m");
            System.out.println("错误信息: " + e.getMessage());
            System.out.println("请检查: 1.MySQL是否启动 2.Redis是否启动 3.Nacos配置是否正确");
        }
    }

    private static void printBilibiliLogo() {
        System.out.println();
        System.out.println("\033[36m██████╗ ██╗██╗     ██╗██████╗ ██╗██╗     ██╗\033[0m");
        System.out.println("\033[36m██╔══██╗██║██║     ██║██╔══██╗██║██║     ██║\033[0m");
        System.out.println("\033[36m██████╔╝██║██║     ██║██████╔╝██║██║     ██║\033[0m");
        System.out.println("\033[36m██╔══██╗██║██║     ██║██╔══██╗██║██║     ██║\033[0m");
        System.out.println("\033[36m██████╔╝██║███████╗██║██████╔╝██║███████╗██║\033[0m");
        System.out.println("\033[36m╚═════╝ ╚═╝╚══════╝╚═╝╚═════╝ ╚═╝╚══════╝╚═╝\033[0m");
        System.out.println();
        System.out.println("\033[35m     ██████╗ ██╗   ██╗████████╗██╗  ██╗\033[0m");
        System.out.println("\033[35m    ██╔═══██╗██║   ██║╚══██╔══╝██║  ██║\033[0m");
        System.out.println("\033[35m    ██║   ██║██║   ██║   ██║   ███████║\033[0m");
        System.out.println("\033[35m    ██║   ██║██║   ██║   ██║   ██╔══██║\033[0m");
        System.out.println("\033[35m    ╚██████╔╝╚██████╔╝   ██║   ██║  ██║\033[0m");
        System.out.println("\033[35m     ╚═════╝  ╚═════╝    ╚═╝   ╚═╝  ╚═╝\033[0m");
        System.out.println();
        System.out.println("\033[33m              🎬 干杯 (゜-゜)つロ 🎬\033[0m");
        System.out.println("\033[33m         ════════════════════════════\033[0m");
        System.out.println("\033[32m           🚀 认证服务启动中... 🚀\033[0m");
        System.out.println();
        System.out.println("========================================");
        System.out.println("🌟 \033[1;34mBilibili 用户认证和授权服务\033[0m 🌟");
        System.out.println("========================================");
    }

    private static void printSuccessInfo() {
        System.out.println();
        System.out.println("\033[32m           🎉 认证服务启动成功！ 🎉\033[0m");
        System.out.println("========================================");
        System.out.println("🔧 服务地址: \033[1;32mhttp://localhost:8090\033[0m");
        System.out.println("📖 API文档: \033[1;32mhttp://localhost:8090/doc.html\033[0m");
        System.out.println("🌟 健康检查: \033[1;32mhttp://localhost:8090/actuator/health\033[0m");
        System.out.println("📊 数据库监控: \033[1;32mhttp://localhost:8090/druid\033[0m");
        System.out.println("🌐 Nacos控制台: \033[1;32mhttp://***********:8848/nacos\033[0m");
        System.out.println("========================================");
        System.out.println("\033[1;36m🎯 环境: 生产环境 (连接远程Nacos)\033[0m");
        System.out.println("\033[1;33m⚡ 服务: bilibili-auth-service\033[0m");
        System.out.println("\033[1;35m🔥 版本: v1.0.0\033[0m");
        System.out.println("========================================");
    }
}