package com.bilibili.auth.aspect;

import com.bilibili.common.exception.ServiceException;
import com.bilibili.auth.annotation.RequireVip;
import com.bilibili.auth.entity.User;
import com.bilibili.auth.service.AuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

/**
 * VIP权限检查切面
 * 用于自动检查方法是否需要VIP权限
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class VipPermissionAspect {

    private final AuthService authService;

    @Before("@annotation(requireVip)")
    public void checkVipPermission(JoinPoint joinPoint, RequireVip requireVip) {
        try {
            User currentUser = authService.getCurrentUser();
            
            if (currentUser == null) {
                throw new ServiceException(40001, "用户未登录");
            }

            // 检查是否为VIP用户
            if (!currentUser.isVipUser()) {
                log.warn("非VIP用户尝试访问VIP功能: uid={}, method={}",
                    currentUser.getUid(), joinPoint.getSignature().getName());
                throw new ServiceException(40003, requireVip.message());
            }

            // 如果需要检查VIP有效性
            if (requireVip.requireValid() && !currentUser.isVipValid()) {
                log.warn("VIP已过期用户尝试访问VIP功能: uid={}, method={}, dueDate={}",
                    currentUser.getUid(), joinPoint.getSignature().getName(), currentUser.getVipDueDate());
                throw new ServiceException(40003, "VIP已过期，请续费后使用");
            }

            log.debug("VIP权限检查通过: uid={}, method={}", 
                currentUser.getUid(), joinPoint.getSignature().getName());

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("VIP权限检查失败", e);
            throw new ServiceException(50000, "权限检查失败");
        }
    }

    @Before("@within(requireVip)")
    public void checkClassVipPermission(JoinPoint joinPoint, RequireVip requireVip) {
        checkVipPermission(joinPoint, requireVip);
    }
}
