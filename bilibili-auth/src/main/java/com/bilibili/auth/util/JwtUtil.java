package com.bilibili.auth.util;

import com.bilibili.auth.config.JwtProperties;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtUtil {

    private final JwtProperties jwtProperties;

    private SecretKey getSigningKey() {
        String secret = jwtProperties.getSecret();
        log.debug("JWT密钥长度: {} 字符, {} 位", secret.length(), secret.length() * 8);

        if (secret.length() * 8 < 512) {
            log.error("JWT密钥长度不足: {} 位，HS512算法要求至少512位", secret.length() * 8);
            throw new IllegalArgumentException("JWT密钥长度不足，HS512算法要求至少512位");
        }

        return Keys.hmacShaKeyFor(secret.getBytes());
    }

    /**
     * 生成访问令牌
     */
    public String generateAccessToken(Long uid, String username) {
        return generateAccessToken(uid, username, null, null);
    }

    /**
     * 生成访问令牌（带Token ID）
     */
    public String generateAccessToken(Long uid, String username, String tokenId) {
        return generateAccessToken(uid, username, tokenId, null);
    }

    /**
     * 生成访问令牌（带VIP信息）
     */
    public String generateAccessToken(Long uid, String username, String tokenId, VipInfo vipInfo) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtProperties.getAccessTokenExpiration());
        //存放payload中的信息
        Map<String, Object> claims = new HashMap<>();
        claims.put("uid", uid);
        claims.put("username", username);
        claims.put("type", "access");

        // 如果没有提供tokenId，则生成一个
        if (tokenId == null) {
            tokenId = generateTokenId();
        }
        claims.put("tokenId", tokenId);

        // 添加VIP信息
        if (vipInfo != null) {
            claims.put("vip", vipInfo.toMap());
        } else {
            // 默认非VIP信息
            claims.put("vip", VipInfo.defaultVip().toMap());
        }

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(uid.toString())
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(Long uid) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtProperties.getRefreshTokenExpiration());

        Map<String, Object> claims = new HashMap<>();
        claims.put("uid", uid);
        claims.put("type", "refresh");

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(uid.toString())
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从令牌中获取用户ID
     */
    public Long getUidFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? Long.valueOf(claims.getSubject()) : null;
    }

    /**
     * 从令牌中获取用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? (String) claims.get("username") : null;
    }

    /**
     * 获取令牌类型
     */
    public String getTokenType(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? (String) claims.get("type") : null;
    }

    /**
     * 获取令牌过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.getExpiration() : null;
    }

    /**
     * 验证令牌
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token);
            return true;
        } catch (Exception e) {
            log.error("验证JWT令牌失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired(String token) {
        Date expiration = getExpirationDateFromToken(token);
        return expiration != null && expiration.before(new Date());
    }

    /**
     * 检查是否为访问令牌
     */
    public boolean isAccessToken(String token) {
        return "access".equals(getTokenType(token));
    }

    /**
     * 检查是否为刷新令牌
     */
    public boolean isRefreshToken(String token) {
        return "refresh".equals(getTokenType(token));
    }

    /**
     * 从令牌中解析Claims
     */
    private Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (Exception e) {
            log.error("解析JWT令牌失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取令牌剩余有效时间(毫秒)
     */
    public long getRemainingTime(String token) {
        Date expiration = getExpirationDateFromToken(token);
        if (expiration == null) {
            return 0;
        }
        return Math.max(0, expiration.getTime() - System.currentTimeMillis());
    }

    /**
     * 刷新令牌
     */
    public String refreshToken(String refreshToken) {
        if (!validateToken(refreshToken) || !isRefreshToken(refreshToken)) {
            throw new IllegalArgumentException("无效的刷新令牌");
        }

        Long uid = getUidFromToken(refreshToken);
        if (uid == null) {
            throw new IllegalArgumentException("无法从刷新令牌中获取用户信息");
        }

        // 这里需要从数据库获取用户名，简化处理
        // 实际应用中应该从用户服务获取
        return generateAccessToken(uid, "user_" + uid);
    }

    /**
     * 提取Bearer令牌
     */
    public String extractBearerToken(String bearerToken) {
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return bearerToken;
    }

    /**
     * 生成Token ID
     */
    public String generateTokenId() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 从令牌中获取Token ID
     */
    public String getTokenIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? (String) claims.get("tokenId") : null;
    }

    /**
     * 从Authorization头中提取Token
     */
    public String extractTokenFromAuthHeader(String authHeader) {
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }
}
