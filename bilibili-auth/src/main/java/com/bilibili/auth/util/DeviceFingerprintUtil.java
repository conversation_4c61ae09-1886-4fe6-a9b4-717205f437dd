package com.bilibili.auth.util;

import com.bilibili.auth.dto.LoginRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 设备指纹生成工具类
 * 用于生成唯一的设备标识，区分不同的登录设备
 */
@Slf4j
public class DeviceFingerprintUtil {

    private static final Pattern OS_PATTERN = Pattern.compile("\\(([^)]+)\\)");
    private static final Pattern BROWSER_PATTERN = Pattern.compile("(Chrome|Firefox|Safari|Edge|Opera)/(\\d+\\.\\d+)");

    /**
     * 生成设备指纹
     * 基于User-Agent、IP地址等信息生成唯一标识
     */
    public static String generateFingerprint(String userAgent, String ipAddress, LoginRequest.DeviceInfo deviceInfo) {
        StringBuilder fingerprintBuilder = new StringBuilder();

        // 添加User-Agent信息
        if (StringUtils.hasText(userAgent)) {
            fingerprintBuilder.append(userAgent);
        }

        // 添加设备信息
        if (deviceInfo != null) {
            if (StringUtils.hasText(deviceInfo.getDeviceId())) {
                fingerprintBuilder.append("|").append(deviceInfo.getDeviceId());
            }
            if (StringUtils.hasText(deviceInfo.getDeviceType())) {
                fingerprintBuilder.append("|").append(deviceInfo.getDeviceType());
            }
            if (StringUtils.hasText(deviceInfo.getOsType())) {
                fingerprintBuilder.append("|").append(deviceInfo.getOsType());
            }
            if (StringUtils.hasText(deviceInfo.getAppVersion())) {
                fingerprintBuilder.append("|").append(deviceInfo.getAppVersion());
            }
        }

        // 添加IP地址的前三段（保护隐私）
        if (StringUtils.hasText(ipAddress)) {
            String[] ipParts = ipAddress.split("\\.");
            if (ipParts.length >= 3) {
                fingerprintBuilder.append("|").append(ipParts[0]).append(".").append(ipParts[1]).append(".").append(ipParts[2]);
            }
        }

        // 生成MD5哈希
        String fingerprint = fingerprintBuilder.toString();
        return DigestUtils.md5DigestAsHex(fingerprint.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 解析设备名称
     */
    public static String parseDeviceName(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return "未知设备";
        }

        String browser = parseBrowser(userAgent);
        String os = parseOperatingSystem(userAgent);

        if (StringUtils.hasText(browser) && StringUtils.hasText(os)) {
            return browser + " - " + os;
        } else if (StringUtils.hasText(browser)) {
            return browser;
        } else if (StringUtils.hasText(os)) {
            return os;
        } else {
            return "未知设备";
        }
    }

    /**
     * 解析操作系统
     */
    public static String parseOperatingSystem(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return null;
        }

        if (userAgent.contains("Windows NT 10.0")) {
            return "Windows 10";
        } else if (userAgent.contains("Windows NT 6.3")) {
            return "Windows 8.1";
        } else if (userAgent.contains("Windows NT 6.2")) {
            return "Windows 8";
        } else if (userAgent.contains("Windows NT 6.1")) {
            return "Windows 7";
        } else if (userAgent.contains("Windows")) {
            return "Windows";
        } else if (userAgent.contains("Mac OS X")) {
            return "macOS";
        } else if (userAgent.contains("Linux")) {
            return "Linux";
        } else if (userAgent.contains("Android")) {
            return "Android";
        } else if (userAgent.contains("iPhone") || userAgent.contains("iPad")) {
            return "iOS";
        }

        return "未知系统";
    }

    /**
     * 解析浏览器
     */
    public static String parseBrowser(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return null;
        }

        Matcher matcher = BROWSER_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            return matcher.group(1) + " " + matcher.group(2);
        }

        if (userAgent.contains("Chrome")) {
            return "Chrome";
        } else if (userAgent.contains("Firefox")) {
            return "Firefox";
        } else if (userAgent.contains("Safari")) {
            return "Safari";
        } else if (userAgent.contains("Edge")) {
            return "Edge";
        } else if (userAgent.contains("Opera")) {
            return "Opera";
        }

        return "未知浏览器";
    }

    /**
     * 判断设备类型
     */
    public static Integer parseDeviceType(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return 5; // 其他
        }

        if (userAgent.contains("Mobile") || userAgent.contains("Android")) {
            return 2; // 手机APP
        } else if (userAgent.contains("Tablet") || userAgent.contains("iPad")) {
            return 3; // 平板APP
        } else if (userAgent.contains("Electron") || userAgent.contains("Desktop")) {
            return 4; // 桌面应用
        } else if (userAgent.contains("Mozilla") || userAgent.contains("Chrome") || userAgent.contains("Safari")) {
            return 1; // 浏览器
        }

        return 5; // 其他
    }
}
