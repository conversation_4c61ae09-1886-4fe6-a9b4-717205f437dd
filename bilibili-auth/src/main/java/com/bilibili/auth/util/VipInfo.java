package com.bilibili.auth.util;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

/**
 * VIP信息类（用于JWT）
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VipInfo {

    /**
     * 是否为VIP
     */
    private Boolean isVip;

    /**
     * VIP类型：0普通 1月会员 2年会员
     */
    private Integer vipType;

    /**
     * VIP状态：0无效 1有效 2已过期
     */
    private Integer vipStatus;

    /**
     * VIP到期时间戳（秒）
     */
    private Long vipEndTime;

    /**
     * 是否自动续费
     */
    private Boolean autoRenew;

    /**
     * VIP等级
     */
    private Integer vipLevel;

    /**
     * 创建默认非VIP信息
     */
    public static VipInfo defaultVip() {
        return VipInfo.builder()
                .isVip(false)
                .vipType(0)
                .vipStatus(0)
                .vipEndTime(null)
                .autoRenew(false)
                .vipLevel(0)
                .build();
    }

    /**
     * 从LocalDateTime创建VIP信息
     */
    public static VipInfo fromVipData(Integer vipType, Integer vipStatus, LocalDateTime vipEndTime, Boolean autoRenew) {
        boolean isVip = vipStatus != null && vipStatus == 1 && 
                       vipEndTime != null && vipEndTime.isAfter(LocalDateTime.now());
        
        return VipInfo.builder()
                .isVip(isVip)
                .vipType(vipType != null ? vipType : 0)
                .vipStatus(vipStatus != null ? vipStatus : 0)
                .vipEndTime(vipEndTime != null ? vipEndTime.toEpochSecond(ZoneOffset.UTC) : null)
                .autoRenew(autoRenew != null ? autoRenew : false)
                .vipLevel(vipType != null ? vipType : 0)
                .build();
    }

    /**
     * 转换为Map（用于JWT claims）
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("isVip", isVip);
        map.put("vipType", vipType);
        map.put("vipStatus", vipStatus);
        map.put("vipEndTime", vipEndTime);
        map.put("autoRenew", autoRenew);
        map.put("vipLevel", vipLevel);
        return map;
    }

    /**
     * 从Map创建VipInfo（用于JWT claims解析）
     */
    public static VipInfo fromMap(Map<String, Object> map) {
        if (map == null) {
            return defaultVip();
        }
        
        return VipInfo.builder()
                .isVip((Boolean) map.get("isVip"))
                .vipType((Integer) map.get("vipType"))
                .vipStatus((Integer) map.get("vipStatus"))
                .vipEndTime(map.get("vipEndTime") != null ? ((Number) map.get("vipEndTime")).longValue() : null)
                .autoRenew((Boolean) map.get("autoRenew"))
                .vipLevel((Integer) map.get("vipLevel"))
                .build();
    }

    /**
     * 检查是否有特定特权
     */
    public boolean hasPrivilege(String privilege) {
        if (!isVip) {
            return false;
        }
        
        switch (privilege) {
            case "ad_free":
            case "hd_quality":
            case "offline_download":
                return true;
            case "exclusive_content":
                return vipType != null && vipType == 2; // 年会员专享
            default:
                return false;
        }
    }

    /**
     * 检查VIP是否有效
     */
    public boolean isValid() {
        return isVip != null && isVip && 
               vipStatus != null && vipStatus == 1 &&
               (vipEndTime == null || vipEndTime > System.currentTimeMillis() / 1000);
    }
}
