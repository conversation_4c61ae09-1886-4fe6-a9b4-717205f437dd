package com.bilibili.auth.enums;

import lombok.Getter;

/**
 * 用户权限枚举
 * 简化权限管理，只分为大会员和非大会员两种权限
 */
@Getter
public enum UserPermission {
    
    /**
     * 普通用户权限
     */
    NORMAL_USER("NORMAL_USER", "普通用户", "基础功能权限"),
    
    /**
     * 大会员权限
     */
    VIP_USER("VIP_USER", "大会员", "大会员专享权限");

    private final String code;
    private final String name;
    private final String description;

    UserPermission(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据用户VIP状态获取权限
     */
    public static UserPermission fromVipStatus(Integer vipStatus, Integer vipType) {
        // 只要是有效的VIP（vipStatus=1且vipType>0），就是大会员
        if (vipStatus != null && vipStatus == 1 && vipType != null && vipType > 0) {
            return VIP_USER;
        }
        return NORMAL_USER;
    }

    /**
     * 检查是否为VIP权限
     */
    public boolean isVip() {
        return this == VIP_USER;
    }

    /**
     * 检查是否为普通用户权限
     */
    public boolean isNormal() {
        return this == NORMAL_USER;
    }
}
