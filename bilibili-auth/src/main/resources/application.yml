# Bilibili认证服务主配置文件

# 应用配置
spring:
  application:
    name: auth-service
  profiles:
    active: prod
  # <PERSON> JSON配置
  jackson:
    property-naming-strategy: SNAKE_CASE
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  # Spring Boot 3.x Nacos配置导入方式
  config:
    import:
      - optional:nacos:bilibili-auth-service.yml
      - optional:nacos:shared-spring.yaml
      - optional:nacos:shared-mybatis.yaml
      - optional:nacos:shared-redis.yaml
      - optional:nacos:shared-mq.yaml
      - optional:nacos:shared-feign.yaml
      - optional:nacos:shared-sms.yaml
      - optional:nacos:shared-elasticsearch.yaml
      - optional:nacos:shared-security.yaml
      - optional:nacos:shared-monitoring.yaml
      # 暂时注释掉Swagger配置
      # - optional:nacos:shared-swagger.yaml


  cloud:
    nacos:
      # Nacos Server地址
      server-addr: 8.156.72.38:8848
      # Nacos配置
      config:
        namespace: public
        group: DEFAULT_GROUP
        file-extension: yml
        # 重要：添加超时和重试配置，避免启动卡死
        timeout: 3000
        max-retry: 3
        # 如果配置中心连接失败，允许应用继续启动
        fail-fast: false
      # Nacos服务发现
      discovery:
        namespace: public
        group: DEFAULT_GROUP
        enabled: true

# 基础配置
server:
  port: 8090



# 阿里云短信服务配置
sms:
  aliyun:
    access-key-id: LTAI5t9y6mTh7gHCowreCCCZ
    access-key-secret: ******************************
    endpoint: dysmsapi.aliyuncs.com
    region: cn-hangzhou
    sign-name: 阿里云短信测试
    templates:
      # 使用正确的SMS模板ID
      login: SMS_154950909

# 验证码配置
verification:
  code:
    length: 6  # 验证码长度
    type: NUMERIC  # 验证码类型：NUMERIC（纯数字）或 ALPHANUMERIC（数字字母混合）
    expire-minutes: 5  # 验证码过期时间（分钟）
    send-interval-seconds: 60  # 发送间隔（秒）
    daily-limit: 10  # 每日发送次数限制

# 日志配置 - 减少冗余输出
logging:
  level:
    root: WARN
    com.bilibili.auth: INFO
    # 减少Spring框架日志
    org.springframework: WARN
    org.springframework.boot: WARN
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.springframework.cloud: WARN
    # 特别屏蔽LoadBalancer的BeanPostProcessor警告
    org.springframework.beans.factory.support.DefaultListableBeanFactory$BeanPostProcessorChecker: ERROR
    org.springframework.context.annotation.ConfigurationClassPostProcessor$ImportAwareBeanPostProcessor: ERROR
    # 减少数据库相关日志
    org.hibernate: WARN
    org.hibernate.SQL: WARN
    com.zaxxer.hikari: WARN
    com.alibaba.druid: WARN
    # 减少Nacos日志
    com.alibaba.nacos: ERROR
    com.alibaba.cloud.nacos: ERROR
    # 减少HTTP客户端日志
    org.apache.http: ERROR
    # 减少其他框架日志
    io.lettuce: WARN
    reactor.netty: WARN
    org.elasticsearch: WARN

  # 日志格式 - 简化输出
  pattern:
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} : %m%n"

# Bilibili业务配置
bilibili:
  # 设备管理配置
  device:
    max-device-count: 5                    # 最大设备数量
    normal-user-max-devices: 3             # 普通用户最大设备数
    vip-user-max-devices: 5                # VIP用户最大设备数
    trust-device-days: 30                  # 设备信任有效期（天）
    device-offline-timeout-minutes: 30     # 设备离线超时（分钟）
    enable-location-detection: true        # 启用地理位置检测
    enable-suspicious-device-detection: true # 启用可疑设备检测
    require-new-device-verification: false # 新设备登录是否需要验证

# 本地Knife4j配置 - 最简版本
knife4j:
  enable: true
  setting:
    language: zh-CN

# SpringDoc配置 - 最简版本
springdoc:
  api-docs:
    enabled: true
