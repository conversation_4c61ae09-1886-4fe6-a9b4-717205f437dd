package com.bilibili.aiassistant;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通义千问API测试类
 */
@SpringBootTest
public class QwenApiTest {

    private static final String API_KEY = "sk-8dddcff3955a489bb2d5240bf198cbef";
    private static final String BASE_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation";

    @Test
    public void testQwenApi() {
        System.out.println("========================================");
        System.out.println("通义千问API测试");
        System.out.println("========================================");
        
        RestTemplate restTemplate = new RestTemplate();
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(API_KEY);
        
        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "qwen-turbo");
        
        Map<String, Object> input = new HashMap<>();
        Map<String, String> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", "你好，请介绍一下自己");
        input.put("messages", List.of(message));
        requestBody.put("input", input);
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("temperature", 0.7);
        requestBody.put("parameters", parameters);
        
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        
        try {
            System.out.println("发送请求到: " + BASE_URL);
            System.out.println("API Key: " + API_KEY);
            System.out.println("请求体: " + requestBody);
            
            ResponseEntity<String> response = restTemplate.postForEntity(BASE_URL, request, String.class);
            
            System.out.println("响应状态: " + response.getStatusCode());
            System.out.println("响应内容: " + response.getBody());
            
            if (response.getStatusCode() == HttpStatus.OK) {
                System.out.println("✅ API测试成功！");
            } else {
                System.out.println("❌ API测试失败，状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            System.out.println("❌ API测试异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("========================================");
    }
    
    @Test
    public void testSimpleMessage() {
        System.out.println("简单消息测试...");
        
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(API_KEY);
        
        String jsonBody = """
            {
                "model": "qwen-turbo",
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello"
                        }
                    ]
                }
            }
            """;
        
        HttpEntity<String> request = new HttpEntity<>(jsonBody, headers);
        
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(BASE_URL, request, String.class);
            System.out.println("响应: " + response.getBody());
        } catch (Exception e) {
            System.out.println("错误: " + e.getMessage());
        }
    }
}
