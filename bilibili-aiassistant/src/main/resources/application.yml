# Bilibili AI助手服务主配置文件

# 服务配置
server:
  port: 8092
  tomcat:
    uri-encoding: UTF-8

# 应用配置
spring:
  application:
    name: aiassistant-service
  profiles:
    active: prod
    
  # Jackson JSON配置
  jackson:
    property-naming-strategy: SNAKE_CASE
    default-property-inclusion: NON_NULL
    
  # Spring Boot 3.x Nacos配置导入方式
  config:
    import:
      - optional:nacos:bilibili-aiassistant-service.yml
      - optional:nacos:shared-spring.yaml
      - optional:nacos:shared-mybatis.yaml
      - optional:nacos:shared-redis.yaml
      - optional:nacos:shared-mq.yaml
      - optional:nacos:shared-feign.yaml
      
  cloud:
    nacos:
      # Nacos Server地址
      server-addr: 8.156.72.38:8848
      # Nacos配置
      config:
        namespace: public
        group: DEFAULT_GROUP
        file-extension: yml
        # 重要：添加超时和重试配置，避免启动卡死
        timeout: 3000
        max-retry: 3
        # 如果配置中心连接失败，允许应用继续启动
        fail-fast: false
      # Nacos服务发现
      discovery:
        namespace: public
        group: DEFAULT_GROUP
        enabled: true
        register-enabled: true
        heartbeat-interval: 30000
        heartbeat-timeout: 180000
        ip-delete-timeout: 30000

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted_at
      logic-delete-value: now()
      logic-not-delete-value: 'null'
      id-type: auto

# 本地开发配置（当Nacos不可用时的备用配置）
---
spring:
  config:
    activate:
      on-profile: local
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
        
  ai:
    # 本地开发使用通义千问
    dashscope:
      api-key: sk-8dddcff3955a489bb2d5240bf198cbef
      base-url: https://dashscope.aliyuncs.com/api/v1
      chat:
        options:
          model: qwen-turbo
          temperature: 0.7
          max-tokens: 2000
          top-p: 0.8
      embedding:
        options:
          model: text-embedding-v2
    # 禁用OpenAI
    openai:
      api-key: disabled
      chat:
        enabled: false
      embedding:
        enabled: false
    vectorstore:
      chroma:
        client:
          host: localhost
          port: 8000
