package com.bilibili.aiassistant.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * AI向量嵌入实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_vector_embeddings")
public class AiVectorEmbedding {

    /**
     * 向量ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 内容类型 1视频 2用户 3评论 4文章
     */
    @TableField("content_type")
    private Integer contentType;

    /**
     * 内容ID
     */
    @TableField("content_id")
    private Long contentId;

    /**
     * 内容哈希
     */
    @TableField("content_hash")
    private String contentHash;

    /**
     * 文本内容
     */
    @TableField("text_content")
    private String textContent;

    /**
     * 向量数据
     */
    @TableField("embedding_vector")
    private String embeddingVector;

    /**
     * 向量化模型
     */
    @TableField("embedding_model")
    private String embeddingModel;

    /**
     * 分块索引
     */
    @TableField("chunk_index")
    private Integer chunkIndex;

    /**
     * 分块大小
     */
    @TableField("chunk_size")
    private Integer chunkSize;

    /**
     * 元数据
     */
    @TableField("metadata")
    private String metadata;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 内容类型枚举
     */
    public enum ContentType {
        VIDEO(1, "视频"),
        USER(2, "用户"),
        COMMENT(3, "评论"),
        ARTICLE(4, "文章");

        private final int code;
        private final String desc;

        ContentType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ContentType fromCode(int code) {
            for (ContentType type : values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }
    }
}
