package com.bilibili.aiassistant.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI推荐记录实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_recommendations")
public class AiRecommendation {

    /**
     * 推荐ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("uid")
    private Long uid;

    /**
     * 推荐类型 1视频 2UP主 3标签
     */
    @TableField("recommendation_type")
    private Integer recommendationType;

    /**
     * 推荐目标ID
     */
    @TableField("target_id")
    private Long targetId;

    /**
     * 推荐分数
     */
    @TableField("score")
    private BigDecimal score;

    /**
     * 推荐理由
     */
    @TableField("reason")
    private String reason;

    /**
     * 算法版本
     */
    @TableField("algorithm_version")
    private String algorithmVersion;

    /**
     * 是否点击
     */
    @TableField("is_clicked")
    private Integer isClicked;

    /**
     * 是否喜欢
     */
    @TableField("is_liked")
    private Integer isLiked;

    /**
     * 用户反馈分数 1-5
     */
    @TableField("feedback_score")
    private Integer feedbackScore;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 点击时间
     */
    @TableField("clicked_at")
    private LocalDateTime clickedAt;

    /**
     * 推荐类型枚举
     */
    public enum RecommendationType {
        VIDEO(1, "视频"),
        UPLOADER(2, "UP主"),
        TAG(3, "标签");

        private final int code;
        private final String desc;

        RecommendationType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static RecommendationType fromCode(int code) {
            for (RecommendationType type : values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 点击状态枚举
     */
    public enum ClickStatus {
        NOT_CLICKED(0, "未点击"),
        CLICKED(1, "已点击");

        private final int code;
        private final String desc;

        ClickStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ClickStatus fromCode(int code) {
            for (ClickStatus status : values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 喜欢状态枚举
     */
    public enum LikeStatus {
        NOT_LIKED(0, "不喜欢"),
        LIKED(1, "喜欢");

        private final int code;
        private final String desc;

        LikeStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static LikeStatus fromCode(int code) {
            for (LikeStatus status : values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }
    }
}
