package com.bilibili.aiassistant.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI内容分析结果实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_content_analysis")
public class AiContentAnalysis {

    /**
     * 分析ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 内容类型 1视频 2用户 3评论
     */
    @TableField("content_type")
    private Integer contentType;

    /**
     * 内容ID
     */
    @TableField("content_id")
    private Long contentId;

    /**
     * 分析类型 1摘要 2标签 3情感 4质量评分
     */
    @TableField("analysis_type")
    private Integer analysisType;

    /**
     * 分析结果
     */
    @TableField("analysis_result")
    private String analysisResult;

    /**
     * 置信度
     */
    @TableField("confidence_score")
    private BigDecimal confidenceScore;

    /**
     * 使用的模型
     */
    @TableField("model_used")
    private String modelUsed;

    /**
     * 分析版本
     */
    @TableField("analysis_version")
    private String analysisVersion;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 内容类型枚举
     */
    public enum ContentType {
        VIDEO(1, "视频"),
        USER(2, "用户"),
        COMMENT(3, "评论");

        private final int code;
        private final String desc;

        ContentType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ContentType fromCode(int code) {
            for (ContentType type : values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 分析类型枚举
     */
    public enum AnalysisType {
        SUMMARY(1, "摘要"),
        TAGS(2, "标签"),
        SENTIMENT(3, "情感"),
        QUALITY_SCORE(4, "质量评分");

        private final int code;
        private final String desc;

        AnalysisType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static AnalysisType fromCode(int code) {
            for (AnalysisType type : values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }
    }
}
