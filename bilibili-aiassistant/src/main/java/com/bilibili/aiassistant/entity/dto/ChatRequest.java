package com.bilibili.aiassistant.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * AI聊天请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Schema(description = "AI聊天请求")
public class ChatRequest {

    @Schema(description = "会话ID，首次对话可为空", example = "session_123456")
    private String sessionId;

    @NotBlank(message = "消息内容不能为空")
    @Size(max = 2000, message = "消息内容不能超过2000字符")
    @Schema(description = "用户消息内容", example = "请解释一下'yyds'这个网络梗的含义")
    private String message;

    @Schema(description = "对话类型", example = "QA", allowableValues = {"QA", "ANALYSIS", "RECOMMENDATION"})
    private String conversationType = "QA";

    @Schema(description = "是否流式响应", example = "false")
    private Boolean stream = false;

    @Schema(description = "上下文配置")
    private ContextConfig context;

    @Data
    @Schema(description = "上下文配置")
    public static class ContextConfig {
        
        @Schema(description = "是否包含历史对话", example = "true")
        private Boolean includeHistory = true;

        @Schema(description = "历史对话数量限制", example = "10")
        private Integer historyLimit = 10;

        @Schema(description = "是否启用RAG检索", example = "true")
        private Boolean enableRag = true;

        @Schema(description = "检索相关内容数量", example = "5")
        private Integer retrievalLimit = 5;
    }
}
