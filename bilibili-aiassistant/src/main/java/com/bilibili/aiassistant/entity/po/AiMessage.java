package com.bilibili.aiassistant.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI对话消息实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_messages")
public class AiMessage {

    /**
     * 消息ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 对话ID
     */
    @TableField("conversation_id")
    private Long conversationId;

    /**
     * 消息类型 1用户 2助手 3系统
     */
    @TableField("message_type")
    private Integer messageType;

    /**
     * 消息内容
     */
    @TableField("content")
    private String content;

    /**
     * 消息元数据
     */
    @TableField("metadata")
    private String metadata;

    /**
     * 使用的模型
     */
    @TableField("model_used")
    private String modelUsed;

    /**
     * 输入Token数
     */
    @TableField("prompt_tokens")
    private Integer promptTokens;

    /**
     * 输出Token数
     */
    @TableField("completion_tokens")
    private Integer completionTokens;

    /**
     * 总Token数
     */
    @TableField("total_tokens")
    private Integer totalTokens;

    /**
     * 成本(美元)
     */
    @TableField("cost")
    private BigDecimal cost;

    /**
     * 响应时间(毫秒)
     */
    @TableField("response_time")
    private Integer responseTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        USER(1, "用户"),
        ASSISTANT(2, "助手"),
        SYSTEM(3, "系统");

        private final int code;
        private final String desc;

        MessageType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static MessageType fromCode(int code) {
            for (MessageType type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown message type code: " + code);
        }
    }
}
