package com.bilibili.aiassistant.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI聊天响应VO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Schema(description = "AI聊天响应")
public class ChatResponse {

    @Schema(description = "会话ID", example = "session_123456")
    private String sessionId;

    @Schema(description = "消息ID", example = "123")
    private Long messageId;

    @Schema(description = "AI回复内容", example = "yyds是'永远的神'的缩写...")
    private String content;

    @Schema(description = "使用的AI模型", example = "gpt-4o-mini")
    private String modelUsed;

    @Schema(description = "响应时间(毫秒)", example = "1500")
    private Integer responseTime;

    @Schema(description = "Token使用情况")
    private TokenUsage tokenUsage;

    @Schema(description = "相关来源信息")
    private List<SourceInfo> sources;

    @Schema(description = "建议的后续问题")
    private List<String> suggestedQuestions;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "响应时间", example = "2025-07-28 10:30:00")
    private LocalDateTime timestamp;

    @Data
    @Schema(description = "Token使用情况")
    public static class TokenUsage {
        
        @Schema(description = "输入Token数", example = "150")
        private Integer promptTokens;

        @Schema(description = "输出Token数", example = "300")
        private Integer completionTokens;

        @Schema(description = "总Token数", example = "450")
        private Integer totalTokens;

        @Schema(description = "预估成本(美元)", example = "0.0045")
        private Double estimatedCost;
    }

    @Data
    @Schema(description = "来源信息")
    public static class SourceInfo {
        
        @Schema(description = "来源类型", example = "video", allowableValues = {"video", "user", "comment", "article"})
        private String sourceType;

        @Schema(description = "来源ID", example = "123456")
        private Long sourceId;

        @Schema(description = "来源标题", example = "【科普】网络流行语大全")
        private String title;

        @Schema(description = "相关度分数", example = "0.85")
        private Double relevanceScore;

        @Schema(description = "来源URL", example = "https://bilibili.com/video/BV123456")
        private String url;
    }
}
