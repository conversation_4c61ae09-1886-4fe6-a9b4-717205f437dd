package com.bilibili.aiassistant.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI对话会话实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_conversations")
public class AiConversation {

    /**
     * 对话ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 用户ID
     */
    @TableField("uid")
    private Long uid;

    /**
     * 对话类型 1问答 2分析 3推荐
     */
    @TableField("conversation_type")
    private Integer conversationType;

    /**
     * 对话标题
     */
    @TableField("title")
    private String title;

    /**
     * 状态 1进行中 2已结束 3已删除
     */
    @TableField("status")
    private Integer status;

    /**
     * 消息数量
     */
    @TableField("message_count")
    private Integer messageCount;

    /**
     * 总Token消耗
     */
    @TableField("total_tokens")
    private Integer totalTokens;

    /**
     * 总成本(美元)
     */
    @TableField("total_cost")
    private BigDecimal totalCost;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 删除时间
     */
    @TableField("deleted_at")
    @TableLogic
    private LocalDateTime deletedAt;

    /**
     * 对话类型枚举
     */
    public enum ConversationType {
        QA(1, "问答"),
        ANALYSIS(2, "分析"),
        RECOMMENDATION(3, "推荐");

        private final int code;
        private final String desc;

        ConversationType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 对话状态枚举
     */
    public enum Status {
        ACTIVE(1, "进行中"),
        ENDED(2, "已结束"),
        DELETED(3, "已删除");

        private final int code;
        private final String desc;

        Status(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
