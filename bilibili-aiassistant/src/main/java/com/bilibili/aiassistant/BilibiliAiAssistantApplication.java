package com.bilibili.aiassistant;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Bilibili AI助手服务启动类
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableAsync
@MapperScan("com.bilibili.aiassistant.mapper")
public class BilibiliAiAssistantApplication {

    public static void main(String[] args) {
        try {
            log.info("🚀 正在启动 Bilibili AI助手服务...");
            
            ConfigurableApplicationContext context = SpringApplication.run(BilibiliAiAssistantApplication.class, args);
            Environment env = context.getEnvironment();
            
            printSuccessInfo(env);
            
        } catch (Exception e) {
            log.error("❌ AI助手服务启动失败", e);
            System.exit(1);
        }
    }

    private static void printSuccessInfo(Environment env) {
        try {
            String protocol = "http";
            if (env.getProperty("server.ssl.key-store") != null) {
                protocol = "https";
            }
            
            String serverPort = env.getProperty("server.port", "8092");
            String contextPath = env.getProperty("server.servlet.context-path", "");
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            
            System.out.println();
            System.out.println("\033[32m           🤖 AI助手服务启动成功！ 🤖\033[0m");
            System.out.println("========================================");
            System.out.println("🔧 服务端口: \033[1;32m" + protocol + "://localhost:" + serverPort + contextPath + "\033[0m");
            System.out.println("🌐 外部访问: \033[1;32m" + protocol + "://" + hostAddress + ":" + serverPort + contextPath + "\033[0m");
            System.out.println("📖 API文档: \033[1;32m" + protocol + "://localhost:" + serverPort + contextPath + "/doc.html\033[0m");
            System.out.println("🌟 健康检查: \033[1;32m" + protocol + "://localhost:" + serverPort + contextPath + "/actuator/health\033[0m");
            System.out.println("📊 监控端点: \033[1;32m" + protocol + "://localhost:" + serverPort + contextPath + "/actuator\033[0m");
            System.out.println("🌐 Nacos控制台: \033[1;32mhttp://***********:8848/nacos\033[0m");
            System.out.println("========================================");
            System.out.println("\033[1;36m🎯 环境: " + env.getProperty("spring.profiles.active", "default") + "\033[0m");
            System.out.println("\033[1;36m🤖 AI功能: 智能问答、内容分析、推荐系统\033[0m");
            System.out.println("\033[1;36m📚 支持功能: 网络梗解释、UP主介绍、视频总结\033[0m");
            System.out.println("========================================");
            System.out.println();
            
        } catch (UnknownHostException e) {
            log.warn("无法获取主机地址", e);
        }
    }
}
