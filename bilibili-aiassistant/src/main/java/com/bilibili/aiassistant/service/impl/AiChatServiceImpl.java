package com.bilibili.aiassistant.service.impl;

import com.bilibili.aiassistant.entity.dto.ChatRequest;
import com.bilibili.aiassistant.entity.po.AiConversation;
import com.bilibili.aiassistant.entity.po.AiMessage;
import com.bilibili.aiassistant.entity.vo.ChatResponse;
import com.bilibili.aiassistant.mapper.AiConversationMapper;
import com.bilibili.aiassistant.mapper.AiMessageMapper;
import com.bilibili.aiassistant.service.AiChatService;
import com.bilibili.aiassistant.service.RagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * AI聊天服务实现
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiChatServiceImpl implements AiChatService {

    private final ChatClient chatClient;
    private final RagService ragService;
    private final AiConversationMapper conversationMapper;
    private final AiMessageMapper messageMapper;

    @Override
    @Transactional
    public com.bilibili.aiassistant.entity.vo.ChatResponse chat(ChatRequest request, Long userId) {
        log.info("处理用户聊天请求: userId={}, message={}", userId, request.getMessage());

        try {
            // 1. 获取或创建会话
            String sessionId = getOrCreateSession(request.getSessionId(), userId, request.getConversationType());

            // 2. 构建消息上下文
            List<Message> messages = buildMessageContext(request, sessionId, userId);

            // 3. 调用AI模型
            long startTime = System.currentTimeMillis();
            Prompt prompt = new Prompt(messages);
            ChatResponse aiResponse = chatClient.prompt(prompt).call().chatResponse();
            long responseTime = System.currentTimeMillis() - startTime;

            // 4. 保存对话记录
            AiMessage userMessage = saveUserMessage(sessionId, request.getMessage());
            AiMessage assistantMessage = saveAssistantMessage(sessionId, aiResponse, responseTime);

            // 5. 更新会话统计
            updateConversationStats(sessionId, aiResponse);

            // 6. 构建响应
            return buildChatResponse(sessionId, assistantMessage, aiResponse, responseTime);

        } catch (Exception e) {
            log.error("聊天处理失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new RuntimeException("AI聊天服务暂时不可用，请稍后重试");
        }
    }

    @Override
    public Flux<com.bilibili.aiassistant.entity.vo.ChatResponse> streamChat(ChatRequest request, Long userId) {
        log.info("处理用户流式聊天请求: userId={}, message={}", userId, request.getMessage());

        return Flux.create(sink -> {
            try {
                // 1. 获取或创建会话
                String sessionId = getOrCreateSession(request.getSessionId(), userId, request.getConversationType());

                // 2. 构建消息上下文
                List<Message> messages = buildMessageContext(request, sessionId, userId);

                // 3. 流式调用AI模型
                Prompt prompt = new Prompt(messages);
                chatClient.prompt(prompt).stream().chatResponse()
                    .doOnNext(response -> {
                        // 构建流式响应
                        com.bilibili.aiassistant.entity.vo.ChatResponse chatResponse = 
                            new com.bilibili.aiassistant.entity.vo.ChatResponse();
                        chatResponse.setSessionId(sessionId);
                        chatResponse.setContent(response.getResult().getOutput().getContent());
                        chatResponse.setTimestamp(LocalDateTime.now());
                        
                        sink.next(chatResponse);
                    })
                    .doOnComplete(() -> {
                        log.info("流式聊天完成: sessionId={}", sessionId);
                        sink.complete();
                    })
                    .doOnError(error -> {
                        log.error("流式聊天错误: sessionId={}, error={}", sessionId, error.getMessage());
                        sink.error(error);
                    })
                    .subscribe();

            } catch (Exception e) {
                log.error("流式聊天初始化失败: userId={}, error={}", userId, e.getMessage(), e);
                sink.error(e);
            }
        });
    }

    @Override
    public com.bilibili.aiassistant.entity.vo.ChatResponse explainMeme(String meme, Long userId) {
        log.info("解释网络梗: userId={}, meme={}", userId, meme);

        ChatRequest request = new ChatRequest();
        request.setMessage("请详细解释网络梗「" + meme + "」的含义、来源和使用场景");
        request.setConversationType("QA");

        return chat(request, userId);
    }

    @Override
    public com.bilibili.aiassistant.entity.vo.ChatResponse introduceUploader(String upName, Long userId) {
        log.info("介绍UP主: userId={}, upName={}", userId, upName);

        // 使用RAG检索UP主信息
        String context = ragService.retrieveUploaderInfo(upName);
        
        ChatRequest request = new ChatRequest();
        request.setMessage("请根据以下信息介绍UP主「" + upName + "」：\n" + context);
        request.setConversationType("ANALYSIS");

        return chat(request, userId);
    }

    @Override
    public com.bilibili.aiassistant.entity.vo.ChatResponse summarizeVideo(String videoId, Long userId) {
        log.info("总结视频: userId={}, videoId={}", userId, videoId);

        // 使用RAG检索视频信息
        String context = ragService.retrieveVideoInfo(videoId);
        
        ChatRequest request = new ChatRequest();
        request.setMessage("请根据以下信息总结视频「" + videoId + "」的内容：\n" + context);
        request.setConversationType("ANALYSIS");

        return chat(request, userId);
    }

    @Override
    public com.bilibili.aiassistant.entity.vo.ChatResponse getConversationHistory(String sessionId, Long userId, Integer limit) {
        // TODO: 实现获取会话历史
        throw new RuntimeException("功能开发中");
    }

    @Override
    public void deleteConversation(String sessionId, Long userId) {
        // TODO: 实现删除会话
        throw new RuntimeException("功能开发中");
    }

    @Override
    public com.bilibili.aiassistant.entity.vo.ChatResponse getUserConversations(Long userId, Integer limit) {
        // TODO: 实现获取用户会话列表
        throw new RuntimeException("功能开发中");
    }

    /**
     * 获取或创建会话
     */
    private String getOrCreateSession(String sessionId, Long userId, String conversationType) {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            sessionId = "session_" + UUID.randomUUID().toString().replace("-", "");
            
            // 创建新会话
            AiConversation conversation = new AiConversation();
            conversation.setSessionId(sessionId);
            conversation.setUid(userId);
            conversation.setConversationType(getConversationTypeCode(conversationType));
            conversation.setStatus(AiConversation.Status.ACTIVE.getCode());
            conversation.setMessageCount(0);
            conversation.setTotalTokens(0);
            conversation.setTotalCost(BigDecimal.ZERO);
            
            conversationMapper.insert(conversation);
            log.info("创建新会话: sessionId={}, userId={}", sessionId, userId);
        }
        
        return sessionId;
    }

    /**
     * 构建消息上下文
     */
    private List<Message> buildMessageContext(ChatRequest request, String sessionId, Long userId) {
        List<Message> messages = new ArrayList<>();
        
        // 添加系统消息
        String systemPrompt = buildSystemPrompt(request.getConversationType());
        messages.add(new SystemMessage(systemPrompt));
        
        // 添加历史消息（如果需要）
        if (request.getContext() != null && request.getContext().getIncludeHistory()) {
            // TODO: 添加历史消息逻辑
        }
        
        // 添加当前用户消息
        messages.add(new UserMessage(request.getMessage()));
        
        return messages;
    }

    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(String conversationType) {
        switch (conversationType.toUpperCase()) {
            case "QA":
                return "你是一个专业的B站AI助手，擅长解释网络文化、流行梗和互联网现象。请用生动有趣的方式回答用户问题。";
            case "ANALYSIS":
                return "你是一个专业的内容分析师，能够深入分析视频内容、UP主特点等。请提供客观、详细的分析。";
            case "RECOMMENDATION":
                return "你是一个智能推荐助手，能够根据用户喜好推荐相关内容。请提供个性化的推荐建议。";
            default:
                return "你是一个友好的AI助手，请帮助用户解决问题。";
        }
    }

    /**
     * 保存用户消息
     */
    private AiMessage saveUserMessage(String sessionId, String content) {
        // TODO: 实现保存用户消息
        return new AiMessage();
    }

    /**
     * 保存助手消息
     */
    private AiMessage saveAssistantMessage(String sessionId, ChatResponse aiResponse, long responseTime) {
        // TODO: 实现保存助手消息
        return new AiMessage();
    }

    /**
     * 更新会话统计
     */
    private void updateConversationStats(String sessionId, ChatResponse aiResponse) {
        // TODO: 实现更新会话统计
    }

    /**
     * 构建聊天响应
     */
    private com.bilibili.aiassistant.entity.vo.ChatResponse buildChatResponse(
            String sessionId, AiMessage message, ChatResponse aiResponse, long responseTime) {
        
        com.bilibili.aiassistant.entity.vo.ChatResponse response = 
            new com.bilibili.aiassistant.entity.vo.ChatResponse();
        response.setSessionId(sessionId);
        response.setMessageId(message.getId());
        response.setContent(aiResponse.getResult().getOutput().getContent());
        response.setResponseTime((int) responseTime);
        response.setTimestamp(LocalDateTime.now());
        
        return response;
    }

    /**
     * 获取对话类型代码
     */
    private Integer getConversationTypeCode(String type) {
        switch (type.toUpperCase()) {
            case "QA": return 1;
            case "ANALYSIS": return 2;
            case "RECOMMENDATION": return 3;
            default: return 1;
        }
    }
}
