package com.bilibili.aiassistant.service;

import com.bilibili.aiassistant.entity.dto.ChatRequest;
import com.bilibili.aiassistant.entity.vo.ChatResponse;
import reactor.core.publisher.Flux;

/**
 * AI聊天服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface AiChatService {

    /**
     * 同步聊天
     *
     * @param request 聊天请求
     * @param userId  用户ID
     * @return 聊天响应
     */
    ChatResponse chat(ChatRequest request, Long userId);

    /**
     * 流式聊天
     *
     * @param request 聊天请求
     * @param userId  用户ID
     * @return 流式响应
     */
    Flux<ChatResponse> streamChat(ChatRequest request, Long userId);

    /**
     * 解释网络梗
     *
     * @param meme   网络梗内容
     * @param userId 用户ID
     * @return 解释结果
     */
    ChatResponse explainMeme(String meme, Long userId);

    /**
     * 介绍UP主
     *
     * @param upName UP主名称或UID
     * @param userId 用户ID
     * @return 介绍结果
     */
    ChatResponse introduceUploader(String upName, Long userId);

    /**
     * 总结视频内容
     *
     * @param videoId 视频ID (BV号或AV号)
     * @param userId  用户ID
     * @return 总结结果
     */
    ChatResponse summarizeVideo(String videoId, Long userId);

    /**
     * 获取会话历史
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param limit     限制数量
     * @return 历史消息列表
     */
    ChatResponse getConversationHistory(String sessionId, Long userId, Integer limit);

    /**
     * 删除会话
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     */
    void deleteConversation(String sessionId, Long userId);

    /**
     * 获取用户的会话列表
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 会话列表
     */
    ChatResponse getUserConversations(Long userId, Integer limit);
}
