package com.bilibili.aiassistant.service;

import java.util.List;

/**
 * RAG (检索增强生成) 服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface RagService {

    /**
     * 检索UP主信息
     *
     * @param upName UP主名称或UID
     * @return 格式化的UP主信息
     */
    String retrieveUploaderInfo(String upName);

    /**
     * 检索视频信息
     *
     * @param videoId 视频ID (BV号或AV号)
     * @return 格式化的视频信息
     */
    String retrieveVideoInfo(String videoId);

    /**
     * 语义搜索相关内容
     *
     * @param query 查询文本
     * @param contentType 内容类型 (video/user/comment)
     * @param limit 返回数量限制
     * @return 相关内容列表
     */
    List<String> semanticSearch(String query, String contentType, int limit);

    /**
     * 向量化文本内容
     *
     * @param text 文本内容
     * @return 向量数组
     */
    float[] embedText(String text);

    /**
     * 添加内容到向量数据库
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param text 文本内容
     * @param metadata 元数据
     */
    void addToVectorStore(String contentType, Long contentId, String text, String metadata);

    /**
     * 批量处理内容向量化
     *
     * @param contentType 内容类型
     * @param batchSize 批处理大小
     */
    void batchProcessContent(String contentType, int batchSize);

    /**
     * 删除向量数据
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     */
    void removeFromVectorStore(String contentType, Long contentId);
}
