package com.bilibili.aiassistant.controller;

import com.bilibili.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * AI测试控制器
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/ai/test")
@RequiredArgsConstructor
@Tag(name = "AI测试", description = "AI功能测试接口")
public class AiTestController {

    private final ChatClient chatClient;

    @Operation(summary = "服务健康检查", description = "检查AI助手服务是否正常运行")
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "bilibili-aiassistant-service");
        result.put("status", "running");
        result.put("timestamp", LocalDateTime.now());
        result.put("message", "🤖 AI助手服务运行正常");
        
        return ApiResponse.success("服务健康", result);
    }

    @Operation(summary = "AI模型测试", description = "测试AI模型是否可用")
    @PostMapping("/model")
    public ApiResponse<Map<String, Object>> testModel(@RequestParam(defaultValue = "你好") String message) {
        log.info("测试AI模型: message={}", message);
        
        try {
            long startTime = System.currentTimeMillis();
            
            String response = chatClient.prompt()
                    .user(message)
                    .call()
                    .content();
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            Map<String, Object> result = new HashMap<>();
            result.put("input", message);
            result.put("output", response);
            result.put("responseTime", responseTime + "ms");
            result.put("status", "success");
            result.put("timestamp", LocalDateTime.now());
            
            return ApiResponse.success("AI模型测试成功", result);
            
        } catch (Exception e) {
            log.error("AI模型测试失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("input", message);
            result.put("error", e.getMessage());
            result.put("status", "failed");
            result.put("timestamp", LocalDateTime.now());
            
            return ApiResponse.error("AI模型测试失败", result);
        }
    }

    @Operation(summary = "快速问答测试", description = "快速测试AI问答功能")
    @PostMapping("/quick-qa")
    public ApiResponse<Map<String, Object>> quickQA(@RequestParam String question) {
        log.info("快速问答测试: question={}", question);
        
        try {
            String systemPrompt = "你是一个专业的B站AI助手，请简洁明了地回答用户问题。";
            
            String response = chatClient.prompt()
                    .system(systemPrompt)
                    .user(question)
                    .call()
                    .content();
            
            Map<String, Object> result = new HashMap<>();
            result.put("question", question);
            result.put("answer", response);
            result.put("timestamp", LocalDateTime.now());
            
            return ApiResponse.success("问答成功", result);
            
        } catch (Exception e) {
            log.error("快速问答失败: {}", e.getMessage(), e);
            return ApiResponse.error("问答失败: " + e.getMessage());
        }
    }

    @Operation(summary = "网络梗解释测试", description = "测试网络梗解释功能")
    @PostMapping("/meme-test")
    public ApiResponse<Map<String, Object>> memeTest(@RequestParam(defaultValue = "yyds") String meme) {
        log.info("网络梗解释测试: meme={}", meme);
        
        try {
            String prompt = String.format(
                "请详细解释网络梗「%s」的含义、来源和使用场景。要求：\n" +
                "1. 解释要生动有趣\n" +
                "2. 包含来源背景\n" +
                "3. 举例说明使用场景\n" +
                "4. 控制在200字以内", meme);
            
            String response = chatClient.prompt()
                    .system("你是一个网络文化专家，擅长解释各种网络流行语和梗。")
                    .user(prompt)
                    .call()
                    .content();
            
            Map<String, Object> result = new HashMap<>();
            result.put("meme", meme);
            result.put("explanation", response);
            result.put("timestamp", LocalDateTime.now());
            
            return ApiResponse.success("梗解释成功", result);
            
        } catch (Exception e) {
            log.error("网络梗解释失败: {}", e.getMessage(), e);
            return ApiResponse.error("梗解释失败: " + e.getMessage());
        }
    }

    @Operation(summary = "配置信息", description = "获取AI服务配置信息")
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> getConfig() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "bilibili-aiassistant-service");
        result.put("version", "1.0.0");
        result.put("features", new String[]{
            "智能问答", "网络梗解释", "UP主介绍", "视频总结", "内容推荐"
        });
        result.put("models", new String[]{
            "阿里云通义千问 (qwen-turbo)", "免费使用中"
        });
        result.put("timestamp", LocalDateTime.now());
        
        return ApiResponse.success("配置信息获取成功", result);
    }
}
