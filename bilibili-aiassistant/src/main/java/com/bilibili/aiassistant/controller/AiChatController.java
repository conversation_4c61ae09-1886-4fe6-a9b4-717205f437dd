package com.bilibili.aiassistant.controller;

import com.bilibili.aiassistant.entity.dto.ChatRequest;
import com.bilibili.aiassistant.entity.vo.ChatResponse;
import com.bilibili.aiassistant.service.AiChatService;
import com.bilibili.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * AI聊天控制器
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/ai/chat")
@RequiredArgsConstructor
@Tag(name = "AI聊天", description = "AI智能对话相关接口")
public class AiChatController {

    private final AiChatService aiChatService;

    @Operation(summary = "AI聊天", description = "与AI助手进行对话")
    @PostMapping("/message")
    public ApiResponse<ChatResponse> chat(
            @Valid @RequestBody ChatRequest request,
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long userId) {
        
        log.info("AI聊天请求: userId={}, message={}", userId, request.getMessage());
        
        ChatResponse response = aiChatService.chat(request, userId);
        return ApiResponse.success("对话成功", response);
    }

    @Operation(summary = "流式AI聊天", description = "与AI助手进行流式对话")
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> streamChat(
            @Valid @RequestBody ChatRequest request,
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long userId) {
        
        log.info("流式AI聊天请求: userId={}, message={}", userId, request.getMessage());
        
        return aiChatService.streamChat(request, userId);
    }

    @Operation(summary = "解释网络梗", description = "解释网络流行语和梗的含义")
    @PostMapping("/explain-meme")
    public ApiResponse<ChatResponse> explainMeme(
            @Parameter(description = "网络梗内容", required = true, example = "yyds")
            @RequestParam @NotBlank(message = "网络梗内容不能为空") String meme,
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long userId) {
        
        log.info("解释网络梗请求: userId={}, meme={}", userId, meme);
        
        ChatResponse response = aiChatService.explainMeme(meme, userId);
        return ApiResponse.success("解释成功", response);
    }

    @Operation(summary = "介绍UP主", description = "介绍指定UP主的基本信息和特点")
    @PostMapping("/introduce-uploader")
    public ApiResponse<ChatResponse> introduceUploader(
            @Parameter(description = "UP主名称或UID", required = true, example = "某科学的超电磁炮")
            @RequestParam @NotBlank(message = "UP主名称不能为空") String upName,
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long userId) {
        
        log.info("介绍UP主请求: userId={}, upName={}", userId, upName);
        
        ChatResponse response = aiChatService.introduceUploader(upName, userId);
        return ApiResponse.success("介绍成功", response);
    }

    @Operation(summary = "总结视频内容", description = "总结指定视频的主要内容")
    @PostMapping("/summarize-video")
    public ApiResponse<ChatResponse> summarizeVideo(
            @Parameter(description = "视频ID (BV号或AV号)", required = true, example = "BV1234567890")
            @RequestParam @NotBlank(message = "视频ID不能为空") String videoId,
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long userId) {
        
        log.info("总结视频请求: userId={}, videoId={}", userId, videoId);
        
        ChatResponse response = aiChatService.summarizeVideo(videoId, userId);
        return ApiResponse.success("总结成功", response);
    }

    @Operation(summary = "获取会话历史", description = "获取指定会话的历史消息")
    @GetMapping("/history/{sessionId}")
    public ApiResponse<ChatResponse> getConversationHistory(
            @Parameter(description = "会话ID", required = true) @PathVariable String sessionId,
            @Parameter(description = "消息数量限制", example = "20") @RequestParam(defaultValue = "20") Integer limit,
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long userId) {
        
        log.info("获取会话历史请求: userId={}, sessionId={}, limit={}", userId, sessionId, limit);
        
        ChatResponse response = aiChatService.getConversationHistory(sessionId, userId, limit);
        return ApiResponse.success("获取成功", response);
    }

    @Operation(summary = "删除会话", description = "删除指定的对话会话")
    @DeleteMapping("/conversation/{sessionId}")
    public ApiResponse<Void> deleteConversation(
            @Parameter(description = "会话ID", required = true) @PathVariable String sessionId,
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long userId) {
        
        log.info("删除会话请求: userId={}, sessionId={}", userId, sessionId);
        
        aiChatService.deleteConversation(sessionId, userId);
        return ApiResponse.success("删除成功");
    }

    @Operation(summary = "获取用户会话列表", description = "获取用户的所有对话会话")
    @GetMapping("/conversations")
    public ApiResponse<ChatResponse> getUserConversations(
            @Parameter(description = "会话数量限制", example = "10") @RequestParam(defaultValue = "10") Integer limit,
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long userId) {
        
        log.info("获取用户会话列表请求: userId={}, limit={}", userId, limit);
        
        ChatResponse response = aiChatService.getUserConversations(userId, limit);
        return ApiResponse.success("获取成功", response);
    }
}
