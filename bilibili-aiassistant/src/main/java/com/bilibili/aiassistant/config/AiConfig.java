package com.bilibili.aiassistant.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.chroma.ChromaVectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * AI配置类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Configuration
public class AiConfig {

    @Value("${ai.model.primary:dashscope}")
    private String primaryModel;

    @Value("${spring.ai.vectorstore.chroma.client.host:localhost}")
    private String chromaHost;

    @Value("${spring.ai.vectorstore.chroma.client.port:8000}")
    private int chromaPort;

    @Value("${spring.ai.vectorstore.chroma.collection-name:bilibili-content}")
    private String collectionName;

    /**
     * 配置主要的ChatClient
     */
    @Bean
    @Primary
    public ChatClient chatClient(ChatModel chatModel) {
        log.info("配置ChatClient，使用模型: {}", primaryModel);
        
        return ChatClient.builder(chatModel)
                .defaultSystem("你是一个专业的B站AI助手，擅长解释网络文化、分析视频内容和推荐相关内容。" +
                              "请用友好、专业且有趣的方式回答用户问题。当前使用通义千问模型为您服务。")
                .build();
    }

    /**
     * 配置向量数据库
     */
    @Bean
    public VectorStore vectorStore(EmbeddingModel embeddingModel) {
        log.info("配置向量数据库: Chroma, host={}, port={}, collection={}", 
                chromaHost, chromaPort, collectionName);
        
        try {
            return ChromaVectorStore.builder()
                    .embeddingModel(embeddingModel)
                    .host(chromaHost)
                    .port(chromaPort)
                    .collectionName(collectionName)
                    .initializeSchema(true)
                    .build();
        } catch (Exception e) {
            log.warn("Chroma向量数据库连接失败，使用内存向量存储: {}", e.getMessage());
            // 如果Chroma不可用，可以使用简单的内存向量存储作为备选
            return new SimpleInMemoryVectorStore(embeddingModel);
        }
    }

    /**
     * 简单的内存向量存储实现（备用方案）
     */
    private static class SimpleInMemoryVectorStore implements VectorStore {
        private final EmbeddingModel embeddingModel;

        public SimpleInMemoryVectorStore(EmbeddingModel embeddingModel) {
            this.embeddingModel = embeddingModel;
        }

        @Override
        public void add(java.util.List<org.springframework.ai.document.Document> documents) {
            // 简单实现：暂时不存储，仅用于开发测试
            log.debug("添加文档到内存向量存储: {} 个文档", documents.size());
        }

        @Override
        public java.util.Optional<Boolean> delete(java.util.List<String> idList) {
            log.debug("从内存向量存储删除文档: {} 个ID", idList.size());
            return java.util.Optional.of(true);
        }

        @Override
        public java.util.List<org.springframework.ai.document.Document> similaritySearch(
                org.springframework.ai.vectorstore.SearchRequest request) {
            log.debug("内存向量存储相似性搜索: {}", request.getQuery());
            // 简单实现：返回空列表
            return java.util.List.of();
        }
    }
}
