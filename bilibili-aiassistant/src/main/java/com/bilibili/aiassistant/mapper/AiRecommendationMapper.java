package com.bilibili.aiassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.aiassistant.entity.po.AiRecommendation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI推荐记录Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface AiRecommendationMapper extends BaseMapper<AiRecommendation> {

    /**
     * 根据用户ID查询推荐记录
     *
     * @param uid 用户ID
     * @return 推荐记录列表
     */
    List<AiRecommendation> selectByUid(@Param("uid") Long uid);

    /**
     * 根据用户ID和推荐类型查询推荐记录
     *
     * @param uid                用户ID
     * @param recommendationType 推荐类型
     * @return 推荐记录列表
     */
    List<AiRecommendation> selectByUidAndType(@Param("uid") Long uid,
                                              @Param("recommendationType") Integer recommendationType);

    /**
     * 根据用户ID查询未点击的推荐记录
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 推荐记录列表
     */
    List<AiRecommendation> selectUnclickedByUid(@Param("uid") Long uid,
                                                @Param("limit") Integer limit);

    /**
     * 根据用户ID和目标ID查询推荐记录
     *
     * @param uid      用户ID
     * @param targetId 目标ID
     * @return 推荐记录
     */
    AiRecommendation selectByUidAndTargetId(@Param("uid") Long uid,
                                            @Param("targetId") Long targetId);

    /**
     * 更新推荐记录的点击状态
     *
     * @param id        推荐记录ID
     * @param isClicked 是否点击
     * @return 更新数量
     */
    int updateClickStatus(@Param("id") Long id,
                          @Param("isClicked") Integer isClicked);

    /**
     * 更新推荐记录的喜欢状态
     *
     * @param id       推荐记录ID
     * @param isLiked  是否喜欢
     * @return 更新数量
     */
    int updateLikeStatus(@Param("id") Long id,
                         @Param("isLiked") Integer isLiked);

    /**
     * 更新推荐记录的反馈分数
     *
     * @param id            推荐记录ID
     * @param feedbackScore 反馈分数
     * @return 更新数量
     */
    int updateFeedbackScore(@Param("id") Long id,
                            @Param("feedbackScore") Integer feedbackScore);

    /**
     * 批量插入推荐记录
     *
     * @param recommendations 推荐记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("recommendations") List<AiRecommendation> recommendations);

    /**
     * 删除用户的历史推荐记录
     *
     * @param uid  用户ID
     * @param days 保留天数
     * @return 删除数量
     */
    int deleteHistoryRecommendations(@Param("uid") Long uid,
                                     @Param("days") Integer days);

    /**
     * 统计用户的推荐点击率
     *
     * @param uid 用户ID
     * @return 点击率
     */
    Double calculateClickRate(@Param("uid") Long uid);

    /**
     * 获取热门推荐目标
     *
     * @param recommendationType 推荐类型
     * @param limit              限制数量
     * @return 目标ID列表
     */
    List<Long> selectPopularTargets(@Param("recommendationType") Integer recommendationType,
                                    @Param("limit") Integer limit);
}
