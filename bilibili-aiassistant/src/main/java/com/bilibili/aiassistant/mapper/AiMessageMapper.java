package com.bilibili.aiassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.aiassistant.entity.po.AiMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * AI对话消息Mapper
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface AiMessageMapper extends BaseMapper<AiMessage> {

    /**
     * 根据会话ID查询消息列表
     *
     * @param conversationId 会话ID
     * @param limit          限制数量
     * @return 消息列表
     */
    @Select("SELECT * FROM ai_messages WHERE conversation_id = #{conversationId} ORDER BY created_at DESC LIMIT #{limit}")
    List<AiMessage> findByConversationIdOrderByCreatedAtDesc(@Param("conversationId") Long conversationId, @Param("limit") Integer limit);

    /**
     * 根据会话ID查询最新消息
     *
     * @param conversationId 会话ID
     * @param limit          限制数量
     * @return 消息列表（按时间正序）
     */
    @Select("SELECT * FROM ai_messages WHERE conversation_id = #{conversationId} ORDER BY created_at ASC LIMIT #{limit}")
    List<AiMessage> findRecentMessagesByConversationId(@Param("conversationId") Long conversationId, @Param("limit") Integer limit);

    /**
     * 统计会话消息数量
     *
     * @param conversationId 会话ID
     * @return 消息数量
     */
    @Select("SELECT COUNT(*) FROM ai_messages WHERE conversation_id = #{conversationId}")
    Integer countByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 查询会话的Token使用统计
     *
     * @param conversationId 会话ID
     * @return Token总数
     */
    @Select("SELECT COALESCE(SUM(total_tokens), 0) FROM ai_messages WHERE conversation_id = #{conversationId}")
    Integer sumTokensByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 查询会话的成本统计
     *
     * @param conversationId 会话ID
     * @return 总成本
     */
    @Select("SELECT COALESCE(SUM(cost), 0) FROM ai_messages WHERE conversation_id = #{conversationId}")
    Double sumCostByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 删除会话的所有消息
     *
     * @param conversationId 会话ID
     */
    @Select("DELETE FROM ai_messages WHERE conversation_id = #{conversationId}")
    void deleteByConversationId(@Param("conversationId") Long conversationId);
}
