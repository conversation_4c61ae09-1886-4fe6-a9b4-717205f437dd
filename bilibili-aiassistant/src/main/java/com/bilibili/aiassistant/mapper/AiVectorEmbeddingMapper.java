package com.bilibili.aiassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.aiassistant.entity.po.AiVectorEmbedding;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI向量嵌入Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface AiVectorEmbeddingMapper extends BaseMapper<AiVectorEmbedding> {

    /**
     * 根据内容类型和内容ID查询向量嵌入
     *
     * @param contentType 内容类型
     * @param contentId   内容ID
     * @return 向量嵌入列表
     */
    List<AiVectorEmbedding> selectByContentTypeAndId(@Param("contentType") Integer contentType,
                                                     @Param("contentId") Long contentId);

    /**
     * 根据内容哈希查询向量嵌入
     *
     * @param contentHash 内容哈希
     * @return 向量嵌入
     */
    AiVectorEmbedding selectByContentHash(@Param("contentHash") String contentHash);

    /**
     * 根据向量化模型查询向量嵌入
     *
     * @param embeddingModel 向量化模型
     * @return 向量嵌入列表
     */
    List<AiVectorEmbedding> selectByEmbeddingModel(@Param("embeddingModel") String embeddingModel);

    /**
     * 批量插入向量嵌入
     *
     * @param embeddings 向量嵌入列表
     * @return 插入数量
     */
    int batchInsert(@Param("embeddings") List<AiVectorEmbedding> embeddings);

    /**
     * 删除指定内容的所有向量嵌入
     *
     * @param contentType 内容类型
     * @param contentId   内容ID
     * @return 删除数量
     */
    int deleteByContentTypeAndId(@Param("contentType") Integer contentType,
                                 @Param("contentId") Long contentId);
}
