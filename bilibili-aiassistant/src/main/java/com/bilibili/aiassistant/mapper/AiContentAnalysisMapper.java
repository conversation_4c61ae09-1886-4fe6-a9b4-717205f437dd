package com.bilibili.aiassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.aiassistant.entity.po.AiContentAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI内容分析结果Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface AiContentAnalysisMapper extends BaseMapper<AiContentAnalysis> {

    /**
     * 根据内容类型和内容ID查询分析结果
     *
     * @param contentType 内容类型
     * @param contentId   内容ID
     * @return 分析结果列表
     */
    List<AiContentAnalysis> selectByContentTypeAndId(@Param("contentType") Integer contentType,
                                                     @Param("contentId") Long contentId);

    /**
     * 根据内容类型、内容ID和分析类型查询分析结果
     *
     * @param contentType  内容类型
     * @param contentId    内容ID
     * @param analysisType 分析类型
     * @return 分析结果
     */
    AiContentAnalysis selectByContentAndAnalysisType(@Param("contentType") Integer contentType,
                                                     @Param("contentId") Long contentId,
                                                     @Param("analysisType") Integer analysisType);

    /**
     * 根据分析类型查询分析结果
     *
     * @param analysisType 分析类型
     * @return 分析结果列表
     */
    List<AiContentAnalysis> selectByAnalysisType(@Param("analysisType") Integer analysisType);

    /**
     * 根据使用的模型查询分析结果
     *
     * @param modelUsed 使用的模型
     * @return 分析结果列表
     */
    List<AiContentAnalysis> selectByModelUsed(@Param("modelUsed") String modelUsed);

    /**
     * 查询最新的分析结果
     *
     * @param contentType  内容类型
     * @param contentId    内容ID
     * @param analysisType 分析类型
     * @return 最新的分析结果
     */
    AiContentAnalysis selectLatestAnalysis(@Param("contentType") Integer contentType,
                                           @Param("contentId") Long contentId,
                                           @Param("analysisType") Integer analysisType);

    /**
     * 批量插入分析结果
     *
     * @param analyses 分析结果列表
     * @return 插入数量
     */
    int batchInsert(@Param("analyses") List<AiContentAnalysis> analyses);

    /**
     * 删除指定内容的所有分析结果
     *
     * @param contentType 内容类型
     * @param contentId   内容ID
     * @return 删除数量
     */
    int deleteByContentTypeAndId(@Param("contentType") Integer contentType,
                                 @Param("contentId") Long contentId);
}
