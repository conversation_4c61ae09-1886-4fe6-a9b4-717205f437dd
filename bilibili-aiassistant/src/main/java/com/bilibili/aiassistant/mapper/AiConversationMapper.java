package com.bilibili.aiassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.aiassistant.entity.po.AiConversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * AI对话会话Mapper
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface AiConversationMapper extends BaseMapper<AiConversation> {

    /**
     * 根据会话ID和用户ID查询会话
     *
     * @param sessionId 会话ID
     * @param uid       用户ID
     * @return 会话信息
     */
    @Select("SELECT * FROM ai_conversations WHERE session_id = #{sessionId} AND uid = #{uid} AND deleted_at IS NULL")
    AiConversation findBySessionIdAndUid(@Param("sessionId") String sessionId, @Param("uid") Long uid);

    /**
     * 查询用户的会话列表
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 会话列表
     */
    @Select("SELECT * FROM ai_conversations WHERE uid = #{uid} AND deleted_at IS NULL ORDER BY updated_at DESC LIMIT #{limit}")
    List<AiConversation> findByUidOrderByUpdatedAtDesc(@Param("uid") Long uid, @Param("limit") Integer limit);

    /**
     * 更新会话统计信息
     *
     * @param sessionId    会话ID
     * @param messageCount 消息数量增量
     * @param totalTokens  Token数量增量
     * @param totalCost    成本增量
     */
    @Update("UPDATE ai_conversations SET message_count = message_count + #{messageCount}, " +
            "total_tokens = total_tokens + #{totalTokens}, total_cost = total_cost + #{totalCost}, " +
            "updated_at = NOW() WHERE session_id = #{sessionId}")
    void updateStats(@Param("sessionId") String sessionId,
                     @Param("messageCount") Integer messageCount,
                     @Param("totalTokens") Integer totalTokens,
                     @Param("totalCost") Double totalCost);

    /**
     * 软删除会话
     *
     * @param sessionId 会话ID
     * @param uid       用户ID
     */
    @Update("UPDATE ai_conversations SET deleted_at = NOW() WHERE session_id = #{sessionId} AND uid = #{uid}")
    void softDeleteBySessionIdAndUid(@Param("sessionId") String sessionId, @Param("uid") Long uid);

    /**
     * 查询用户今日对话数量
     *
     * @param uid 用户ID
     * @return 今日对话数量
     */
    @Select("SELECT COUNT(*) FROM ai_conversations WHERE uid = #{uid} AND DATE(created_at) = CURDATE()")
    Integer countTodayConversations(@Param("uid") Long uid);

    /**
     * 查询用户今日消息数量
     *
     * @param uid 用户ID
     * @return 今日消息数量
     */
    @Select("SELECT COALESCE(SUM(message_count), 0) FROM ai_conversations WHERE uid = #{uid} AND DATE(created_at) = CURDATE()")
    Integer countTodayMessages(@Param("uid") Long uid);
}
