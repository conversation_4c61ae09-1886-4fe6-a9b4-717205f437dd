package com.bilibili.user.service;

import com.bilibili.user.entity.dto.BindPhoneDTO;
import com.bilibili.user.entity.dto.BindWechatDTO;
import com.bilibili.user.entity.dto.ChangePasswordDTO;
import com.bilibili.user.entity.dto.RealNameAuthDTO;
import com.bilibili.user.entity.vo.SecurityStatusVO;

/**
 * 账号安全服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface AccountSecurityService {

    /**
     * 获取安全状态
     *
     * @param uid 用户ID
     * @return 安全状态
     */
    SecurityStatusVO getSecurityStatus(Long uid);

    /**
     * 修改密码
     *
     * @param uid 用户ID
     * @param changePasswordDTO 修改密码请求
     */
    void changePassword(Long uid, ChangePasswordDTO changePasswordDTO);

    /**
     * 绑定手机号
     *
     * @param uid 用户ID
     * @param bindPhoneDTO 绑定手机号请求
     */
    void bindPhone(Long uid, BindPhoneDTO bindPhoneDTO);

    /**
     * 解绑手机号
     *
     * @param uid 用户ID
     * @param code 验证码
     */
    void unbindPhone(Long uid, String code);

    /**
     * 绑定微信
     *
     * @param uid 用户ID
     * @param bindWechatDTO 绑定微信请求
     */
    void bindWechat(Long uid, BindWechatDTO bindWechatDTO);

    /**
     * 解绑微信
     *
     * @param uid 用户ID
     * @param code 验证码
     */
    void unbindWechat(Long uid, String code);

    /**
     * 实名认证
     *
     * @param uid 用户ID
     * @param realNameAuthDTO 实名认证请求
     */
    void realNameAuth(Long uid, RealNameAuthDTO realNameAuthDTO);
}
