package com.bilibili.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bilibili.common.exception.ServiceException;
import com.bilibili.user.entity.dto.VipPurchaseDTO;
import com.bilibili.user.entity.po.UserVip;
import com.bilibili.user.entity.vo.VipInfoVO;
import com.bilibili.user.entity.vo.VipPrivilegeVO;
import com.bilibili.user.entity.vo.VipPackageVO;
import com.bilibili.user.mapper.UserVipMapper;
import com.bilibili.user.service.VipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * VIP服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VipServiceImpl implements VipService {

    private final UserVipMapper userVipMapper;

    @Override
    public VipInfoVO getVipInfo(Long uid) {
        log.info("获取VIP信息: uid={}", uid);

        UserVip userVip = getUserVip(uid);
        
        boolean isVip = isVipValid(userVip);
        long remainingDays = 0;
        boolean nearExpiry = false;
        
        if (userVip != null && userVip.getVipEndTime() != null) {
            remainingDays = ChronoUnit.DAYS.between(LocalDateTime.now(), userVip.getVipEndTime());
            nearExpiry = remainingDays <= 7 && remainingDays > 0;
        }

        return VipInfoVO.builder()
                .isVip(isVip)
                .vipType(userVip != null ? userVip.getVipType() : 0)
                .vipTypeDesc(getVipTypeDesc(userVip != null ? userVip.getVipType() : 0))
                .vipStatus(userVip != null ? userVip.getVipStatus() : 0)
                .vipStatusDesc(getVipStatusDesc(userVip != null ? userVip.getVipStatus() : 0))
                .vipStartTime(userVip != null ? userVip.getVipStartTime() : null)
                .vipEndTime(userVip != null ? userVip.getVipEndTime() : null)
                .remainingDays(Math.max(remainingDays, 0))
                .nearExpiry(nearExpiry)
                .autoRenew(userVip != null && userVip.getAutoRenew() == 1)
                .totalAmount(userVip != null ? userVip.getVipAmount() : BigDecimal.ZERO)
                .purchaseCount(1) // TODO: 从购买记录表统计
                .vipLevel(calculateVipLevel(userVip))
                .vipPoints(0) // TODO: 从积分系统获取
                .nextRenewAmount(getNextRenewAmount(userVip))
                .nextRenewTime(getNextRenewTime(userVip))
                .vipBadge(getVipBadge(userVip))
                .exclusiveService(isVip)
                .adFree(isVip)
                .hdQuality(isVip)
                .offlineDownload(isVip)
                .exclusiveContent(userVip != null && userVip.getVipType() == 2) // 年会员专享
                .build();
    }

    @Override
    public List<VipPrivilegeVO> getVipPrivileges(Long uid) {
        log.info("获取VIP特权: uid={}", uid);

        UserVip userVip = getUserVip(uid);
        boolean isVip = isVipValid(userVip);
        int vipLevel = calculateVipLevel(userVip);

        List<VipPrivilegeVO> privileges = new ArrayList<>();

        // 基础特权
        privileges.add(VipPrivilegeVO.builder()
                .privilegeId("ad_free")
                .name("免广告")
                .description("观看视频无广告打扰")
                .icon("no-ads")
                .unlocked(isVip)
                .requiredVipLevel(1)
                .type("basic")
                .usageLimit(-1)
                .usedCount(0)
                .remainingCount(-1)
                .status(isVip ? "active" : "inactive")
                .detailUrl("/vip/privilege/ad-free")
                .build());

        privileges.add(VipPrivilegeVO.builder()
                .privilegeId("hd_quality")
                .name("高清画质")
                .description("享受1080P高清画质")
                .icon("hd")
                .unlocked(isVip)
                .requiredVipLevel(1)
                .type("basic")
                .usageLimit(-1)
                .usedCount(0)
                .remainingCount(-1)
                .status(isVip ? "active" : "inactive")
                .detailUrl("/vip/privilege/hd-quality")
                .build());

        // 高级特权
        privileges.add(VipPrivilegeVO.builder()
                .privilegeId("offline_download")
                .name("离线下载")
                .description("下载视频离线观看")
                .icon("download")
                .unlocked(isVip)
                .requiredVipLevel(1)
                .type("premium")
                .usageLimit(100)
                .usedCount(0) // TODO: 从使用记录获取
                .remainingCount(100)
                .status(isVip ? "active" : "inactive")
                .detailUrl("/vip/privilege/offline-download")
                .build());

        // 专享特权（年会员）
        privileges.add(VipPrivilegeVO.builder()
                .privilegeId("exclusive_content")
                .name("会员专享")
                .description("观看会员专享内容")
                .icon("exclusive")
                .unlocked(userVip != null && userVip.getVipType() == 2)
                .requiredVipLevel(2)
                .type("exclusive")
                .usageLimit(-1)
                .usedCount(0)
                .remainingCount(-1)
                .status(userVip != null && userVip.getVipType() == 2 ? "active" : "inactive")
                .detailUrl("/vip/privilege/exclusive-content")
                .build());

        return privileges;
    }

    @Override
    @Transactional
    public VipInfoVO purchaseVip(Long uid, VipPurchaseDTO vipPurchaseDTO) {
        log.info("购买VIP: uid={}, type={}, duration={}", uid, vipPurchaseDTO.getVipType(), vipPurchaseDTO.getDuration());

        // 计算价格
        BigDecimal amount = calculateVipPrice(vipPurchaseDTO.getVipType(), vipPurchaseDTO.getDuration());
        
        // TODO: 处理支付逻辑
        // paymentService.processPayment(uid, amount, vipPurchaseDTO.getPaymentMethod());

        UserVip userVip = getUserVip(uid);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now;
        LocalDateTime endTime;

        if (userVip != null && userVip.getVipEndTime() != null && userVip.getVipEndTime().isAfter(now)) {
            // 续费：从当前到期时间开始计算
            startTime = userVip.getVipEndTime();
        }

        endTime = startTime.plusMonths(vipPurchaseDTO.getDuration());

        if (userVip == null) {
            // 新购买
            userVip = UserVip.builder()
                    .uid(uid)
                    .vipType(vipPurchaseDTO.getVipType())
                    .vipStatus(1)
                    .vipStartTime(now)
                    .vipEndTime(endTime)
                    .vipAmount(amount)
                    .vipPurchaseTime(now)
                    .autoRenew(vipPurchaseDTO.getAutoRenew() ? 1 : 0)
                    .createdAt(now)
                    .updatedAt(now)
                    .build();
            userVipMapper.insert(userVip);
        } else {
            // 续费或升级
            userVip.setVipType(vipPurchaseDTO.getVipType());
            userVip.setVipStatus(1);
            userVip.setVipEndTime(endTime);
            userVip.setVipAmount(userVip.getVipAmount().add(amount));
            userVip.setVipPurchaseTime(now);
            userVip.setAutoRenew(vipPurchaseDTO.getAutoRenew() ? 1 : 0);
            userVip.setUpdatedAt(now);
            userVipMapper.updateById(userVip);
        }

        log.info("VIP购买成功: uid={}, endTime={}", uid, endTime);
        return getVipInfo(uid);
    }

    @Override
    @Transactional
    public void enableAutoRenew(Long uid) {
        log.info("开启自动续费: uid={}", uid);

        UserVip userVip = getUserVip(uid);
        if (userVip == null) {
            throw new ServiceException("用户未购买VIP");
        }

        userVip.setAutoRenew(1);
        userVip.setUpdatedAt(LocalDateTime.now());
        userVipMapper.updateById(userVip);
    }

    @Override
    @Transactional
    public void disableAutoRenew(Long uid) {
        log.info("关闭自动续费: uid={}", uid);

        UserVip userVip = getUserVip(uid);
        if (userVip == null) {
            throw new ServiceException("用户未购买VIP");
        }

        userVip.setAutoRenew(0);
        userVip.setUpdatedAt(LocalDateTime.now());
        userVipMapper.updateById(userVip);
    }

    @Override
    public List<VipPackageVO> getVipPackages() {
        log.info("获取VIP套餐列表");

        List<VipPackageVO> packages = new ArrayList<>();

        // 月会员套餐
        packages.add(VipPackageVO.builder()
                .packageId("monthly_1")
                .name("月会员")
                .description("1个月大会员")
                .vipType(1)
                .duration(1)
                .originalPrice(new BigDecimal("25.00"))
                .currentPrice(new BigDecimal("25.00"))
                .discount(new BigDecimal("1.0"))
                .recommended(false)
                .popular(false)
                .tags(new String[]{"基础"})
                .privileges(new String[]{"免广告", "高清画质", "离线下载"})
                .icon("monthly")
                .color("#FB7299")
                .available(true)
                .purchaseLimit(12)
                .validUntil("长期有效")
                .build());

        packages.add(VipPackageVO.builder()
                .packageId("monthly_3")
                .name("季度会员")
                .description("3个月大会员")
                .vipType(1)
                .duration(3)
                .originalPrice(new BigDecimal("75.00"))
                .currentPrice(new BigDecimal("68.00"))
                .discount(new BigDecimal("0.91"))
                .recommended(true)
                .popular(false)
                .tags(new String[]{"推荐", "9折"})
                .privileges(new String[]{"免广告", "高清画质", "离线下载"})
                .icon("quarterly")
                .color("#FB7299")
                .available(true)
                .purchaseLimit(4)
                .validUntil("长期有效")
                .build());

        // 年会员套餐
        packages.add(VipPackageVO.builder()
                .packageId("yearly_12")
                .name("年会员")
                .description("12个月大会员")
                .vipType(2)
                .duration(12)
                .originalPrice(new BigDecimal("300.00"))
                .currentPrice(new BigDecimal("233.00"))
                .discount(new BigDecimal("0.78"))
                .recommended(false)
                .popular(true)
                .tags(new String[]{"热门", "7.8折", "专享内容"})
                .privileges(new String[]{"免广告", "高清画质", "离线下载", "会员专享", "专属客服"})
                .icon("yearly")
                .color("#FF6B35")
                .available(true)
                .purchaseLimit(1)
                .validUntil("长期有效")
                .build());

        return packages;
    }

    @Override
    public boolean isVip(Long uid) {
        UserVip userVip = getUserVip(uid);
        return isVipValid(userVip);
    }

    @Override
    public boolean hasPrivilege(Long uid, String privilegeId) {
        UserVip userVip = getUserVip(uid);
        boolean isVip = isVipValid(userVip);

        switch (privilegeId) {
            case "ad_free":
            case "hd_quality":
            case "offline_download":
                return isVip;
            case "exclusive_content":
                return userVip != null && userVip.getVipType() == 2 && isVip;
            default:
                return false;
        }
    }

    // 私有方法
    private UserVip getUserVip(Long uid) {
        return userVipMapper.selectOne(new LambdaQueryWrapper<UserVip>().eq(UserVip::getUid, uid));
    }

    private boolean isVipValid(UserVip userVip) {
        if (userVip == null || userVip.getVipStatus() != 1) {
            return false;
        }
        return userVip.getVipEndTime() != null && userVip.getVipEndTime().isAfter(LocalDateTime.now());
    }

    private String getVipTypeDesc(Integer vipType) {
        switch (vipType) {
            case 1: return "月会员";
            case 2: return "年会员";
            default: return "普通用户";
        }
    }

    private String getVipStatusDesc(Integer vipStatus) {
        switch (vipStatus) {
            case 1: return "有效";
            case 2: return "已过期";
            default: return "无效";
        }
    }

    private int calculateVipLevel(UserVip userVip) {
        if (userVip == null || userVip.getVipType() == 0) {
            return 0;
        }
        return userVip.getVipType();
    }

    private BigDecimal getNextRenewAmount(UserVip userVip) {
        if (userVip == null || userVip.getAutoRenew() != 1) {
            return null;
        }
        return calculateVipPrice(userVip.getVipType(), 1); // 默认续费1个月
    }

    private LocalDateTime getNextRenewTime(UserVip userVip) {
        if (userVip == null || userVip.getAutoRenew() != 1) {
            return null;
        }
        return userVip.getVipEndTime();
    }

    private String getVipBadge(UserVip userVip) {
        if (userVip == null || !isVipValid(userVip)) {
            return null;
        }
        return userVip.getVipType() == 2 ? "年会员" : "月会员";
    }

    private BigDecimal calculateVipPrice(Integer vipType, Integer duration) {
        BigDecimal monthlyPrice = vipType == 2 ? new BigDecimal("25.00") : new BigDecimal("25.00");
        BigDecimal totalPrice = monthlyPrice.multiply(new BigDecimal(duration));
        
        // 折扣计算
        if (duration >= 12) {
            totalPrice = totalPrice.multiply(new BigDecimal("0.78")); // 年费7.8折
        } else if (duration >= 3) {
            totalPrice = totalPrice.multiply(new BigDecimal("0.91")); // 季费9.1折
        }
        
        return totalPrice;
    }
}
