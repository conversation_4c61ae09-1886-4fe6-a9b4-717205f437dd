package com.bilibili.user.service.impl;


import com.bilibili.common.exception.ErrorCode;
import com.bilibili.common.exception.ServiceException;
import com.bilibili.user.entity.po.Users;
import com.bilibili.user.entity.po.UserStats;
import com.bilibili.user.entity.po.UserLevels;
import com.bilibili.user.entity.vo.UserInfoVO;
import com.bilibili.user.mapper.UsersMapper;
import com.bilibili.user.mapper.UserStatsMapper;
import com.bilibili.user.mapper.UserLevelsMapper;
import com.bilibili.user.service.IUsersService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UsersServiceImpl extends ServiceImpl<UsersMapper, Users> implements IUsersService {

    private final UserStatsMapper userStatsMapper;
    private final UserLevelsMapper userLevelsMapper;

    @Override
    public UserInfoVO getUserInfo(String username) {
        log.info("开始获取用户信息，用户名: {}", username);

        // 1. 根据用户名查询用户基础信息
        LambdaQueryWrapper<Users> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(Users::getUsername, username);
        Users user = this.getOne(userWrapper);

        if (user == null) {
            log.warn("用户不存在，用户名: {}", username);
            return null;
        }

        // 2. 查询用户统计信息
        LambdaQueryWrapper<UserStats> statsWrapper = new LambdaQueryWrapper<>();
        statsWrapper.eq(UserStats::getUid, user.getUid());
        UserStats userStats = userStatsMapper.selectOne(statsWrapper);

        // 3. 查询用户等级信息，获取下一等级所需经验值
        LambdaQueryWrapper<UserLevels> levelWrapper = new LambdaQueryWrapper<>();
        levelWrapper.eq(UserLevels::getLevel, user.getLevel() + 1);
        UserLevels nextLevel = userLevelsMapper.selectOne(levelWrapper);

        // 4. 构建返回对象
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setUid(user.getUid());
        userInfoVO.setUsername(user.getUsername());
        userInfoVO.setNickname(user.getNickname());
        userInfoVO.setAvatar(user.getAvatar() != null ? user.getAvatar() : "https://avatar.default.com/default.jpg");
        userInfoVO.setBanner(user.getBanner() != null ? user.getBanner() : "https://banner.default.com/default.jpg");
        userInfoVO.setSignature(user.getSignature() != null ? user.getSignature() : "这个人很懒，什么都没留下");
        userInfoVO.setLevel(user.getLevel());
        userInfoVO.setCoins(user.getCoins() != null ? user.getCoins() : 0.0);
        userInfoVO.setVipType(user.getVipType());
        userInfoVO.setVipStatus(user.getVipStatus());

        // 5. 设置经验值信息
        UserInfoVO.ExpInfo expInfo = new UserInfoVO.ExpInfo();
        expInfo.setCurrent(user.getExp());
        expInfo.setNextLevel(nextLevel != null ? nextLevel.getMinExp() : user.getExp());
        userInfoVO.setExp(expInfo);

        // 6. 设置统计信息
        if (userStats != null) {
            userInfoVO.setFollowingCount(userStats.getFollowingCount() != null ? userStats.getFollowingCount() : 0);
            userInfoVO.setFollowerCount(userStats.getFollowerCount() != null ? userStats.getFollowerCount() : 0);

            UserInfoVO.Statistics statistics = new UserInfoVO.Statistics();
            statistics.setVideoCount(userStats.getVideoCount() != null ? userStats.getVideoCount() : 0);
            statistics.setViewCount(userStats.getViewCount() != null ? userStats.getViewCount() : 0L);
            statistics.setLikeCount(userStats.getLikeCount() != null ? userStats.getLikeCount() : 0L);
            userInfoVO.setStatistics(statistics);
        } else {
            // 如果没有统计数据，设置默认值
            userInfoVO.setFollowingCount(0);
            userInfoVO.setFollowerCount(0);

            UserInfoVO.Statistics statistics = new UserInfoVO.Statistics();
            statistics.setVideoCount(0);
            statistics.setViewCount(0L);
            statistics.setLikeCount(0L);
            userInfoVO.setStatistics(statistics);
        }

        log.info("成功获取用户信息，用户ID: {}, 昵称: {}", user.getUid(), user.getNickname());
        return userInfoVO;
    }

    @Override
    public Users getUserInfoByUid(Long uid) {
        if(uid == null){
            throw new ServiceException(ErrorCode.PARAM_ERROR);
        }

        log.info("开始查询用户信息: uid={}", uid);

        // 调试：先查询表中总记录数（包含逻辑删除的）
        long totalCount = this.count();
        log.info("users表总记录数(deleted_at=0): {}", totalCount);

        // 调试：查询所有记录（忽略逻辑删除）
        List<Users> allUsersIncludeDeleted = this.getBaseMapper().selectList(null);
        log.info("users表所有记录数(包含已删除): {}", allUsersIncludeDeleted.size());

        if (!allUsersIncludeDeleted.isEmpty()) {
            Users firstUser = allUsersIncludeDeleted.get(0);
            log.info("第一条记录: id={}, uid={}, username={}, deleted_at={}",
                firstUser.getId(), firstUser.getUid(), firstUser.getUsername(), firstUser.getDeletedAt());
        }

        // 调试：查询所有用户的uid（限制前10条，只查询未删除的）
        List<Users> allUsers = this.lambdaQuery()
            .select(Users::getUid, Users::getUsername, Users::getId, Users::getDeletedAt)
            .last("LIMIT 10")
            .list();
        log.info("数据库中存在的用户列表(deleted_at=0,前10条): {}",
            allUsers.stream().map(u -> "id=" + u.getId() + ",uid=" + u.getUid() + ",username=" + u.getUsername() + ",deleted_at=" + u.getDeletedAt())
            .collect(java.util.stream.Collectors.toList()));

        Users user = this.lambdaQuery()
                .eq(Users::getUid, uid)
                .one();

        if (user != null) {
            log.info("查询到用户信息: uid={}, username={}, nickname={}",
                user.getUid(), user.getUsername(), user.getNickname());
        } else {
            log.warn("未查询到用户信息: uid={}", uid);
        }

        return user;
    }
}
