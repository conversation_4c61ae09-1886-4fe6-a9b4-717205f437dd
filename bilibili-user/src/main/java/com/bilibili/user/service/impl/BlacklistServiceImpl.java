package com.bilibili.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bilibili.common.dto.PageQuery;
import com.bilibili.common.dto.PageResult;
import com.bilibili.common.exception.ServiceException;
import com.bilibili.user.entity.dto.AddBlacklistDTO;
import com.bilibili.user.entity.po.UserBlacklist;
import com.bilibili.user.entity.po.Users;
import com.bilibili.user.entity.vo.BlacklistVO;
import com.bilibili.user.mapper.UserBlacklistMapper;
import com.bilibili.user.mapper.UsersMapper;
import com.bilibili.user.service.BlacklistService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 黑名单服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BlacklistServiceImpl implements BlacklistService {

    private final UserBlacklistMapper userBlacklistMapper;
    private final UsersMapper usersMapper;

    @Override
    public PageResult<BlacklistVO> getBlacklistList(Long uid, PageQuery pageQuery) {
        log.info("获取黑名单列表: uid={}", uid);

        Page<UserBlacklist> page = new Page<>(pageQuery.getPage(), pageQuery.getSize());
        
        IPage<UserBlacklist> blacklistPage = userBlacklistMapper.selectPage(page,
                new LambdaQueryWrapper<UserBlacklist>()
                        .eq(UserBlacklist::getUid, uid)
                        .orderByDesc(UserBlacklist::getCreatedAt));

        List<BlacklistVO> blacklistVOs = blacklistPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.of(blacklistVOs, blacklistPage.getTotal(), pageQuery.getPage(), pageQuery.getSize());
    }

    @Override
    @Transactional
    public void addToBlacklist(Long uid, AddBlacklistDTO addBlacklistDTO) {
        log.info("添加黑名单: uid={}, targetUid={}", uid, addBlacklistDTO.getTargetUid());

        // 检查是否拉黑自己
        if (uid.equals(addBlacklistDTO.getTargetUid())) {
            throw new ServiceException("不能拉黑自己");
        }

        // 检查目标用户是否存在
        Users targetUser = usersMapper.selectOne(
                new LambdaQueryWrapper<Users>().eq(Users::getUid, addBlacklistDTO.getTargetUid()));
        if (targetUser == null) {
            throw new ServiceException("目标用户不存在");
        }

        // 检查是否已经拉黑
        Integer count = userBlacklistMapper.checkBlacklisted(uid, addBlacklistDTO.getTargetUid());
        if (count > 0) {
            throw new ServiceException("该用户已在黑名单中");
        }

        // 添加到黑名单
        UserBlacklist blacklist = UserBlacklist.builder()
                .uid(uid)
                .targetUid(addBlacklistDTO.getTargetUid())
                .targetUsername(targetUser.getUsername())
                .targetNickname(targetUser.getNickname())
                .targetAvatar(targetUser.getAvatar())
                .reason(addBlacklistDTO.getReason())
                .createdAt(LocalDateTime.now())
                .build();

        userBlacklistMapper.insert(blacklist);
        log.info("添加黑名单成功: uid={}, targetUid={}", uid, addBlacklistDTO.getTargetUid());
    }

    @Override
    @Transactional
    public void removeFromBlacklist(Long uid, Long targetUid) {
        log.info("移除黑名单: uid={}, targetUid={}", uid, targetUid);

        // 检查是否在黑名单中
        Integer count = userBlacklistMapper.checkBlacklisted(uid, targetUid);
        if (count == 0) {
            throw new ServiceException("该用户不在黑名单中");
        }

        // 从黑名单移除
        Integer deletedCount = userBlacklistMapper.deleteByUidAndTargetUid(uid, targetUid);
        if (deletedCount == 0) {
            throw new ServiceException("移除失败");
        }

        log.info("移除黑名单成功: uid={}, targetUid={}", uid, targetUid);
    }

    @Override
    public boolean isBlocked(Long uid, Long targetUid) {
        log.info("检查黑名单: uid={}, targetUid={}", uid, targetUid);
        
        Integer count = userBlacklistMapper.checkBlacklisted(uid, targetUid);
        return count > 0;
    }

    @Override
    @Transactional
    public void batchRemoveFromBlacklist(Long uid, Long[] targetUids) {
        log.info("批量移除黑名单: uid={}, count={}", uid, targetUids.length);

        if (targetUids == null || targetUids.length == 0) {
            throw new ServiceException("目标用户列表不能为空");
        }

        // 批量删除
        int deletedCount = userBlacklistMapper.delete(
                new LambdaQueryWrapper<UserBlacklist>()
                        .eq(UserBlacklist::getUid, uid)
                        .in(UserBlacklist::getTargetUid, Arrays.asList(targetUids)));

        log.info("批量移除黑名单完成: uid={}, 删除数量={}", uid, deletedCount);
    }

    // 私有方法
    private BlacklistVO convertToVO(UserBlacklist blacklist) {
        return BlacklistVO.builder()
                .id(blacklist.getId())
                .targetUid(blacklist.getTargetUid())
                .targetUsername(blacklist.getTargetUsername())
                .targetNickname(blacklist.getTargetNickname())
                .targetAvatar(blacklist.getTargetAvatar())
                .reason(blacklist.getReason())
                .createdAt(blacklist.getCreatedAt())
                .isOnline(false) // TODO: 从在线状态服务获取
                .lastActiveTime(null) // TODO: 从用户活跃状态获取
                .build();
    }
}
