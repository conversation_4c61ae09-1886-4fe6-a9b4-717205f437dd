package com.bilibili.user.service;

import com.bilibili.common.dto.PageQuery;
import com.bilibili.common.dto.PageResult;
import com.bilibili.user.entity.vo.CoinBalanceVO;
import com.bilibili.user.entity.vo.CoinRecordVO;

/**
 * 硬币服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface CoinService {

    /**
     * 获取硬币余额
     *
     * @param uid 用户ID
     * @return 硬币余额信息
     */
    CoinBalanceVO getCoinBalance(Long uid);

    /**
     * 获取硬币记录
     *
     * @param uid 用户ID
     * @param pageQuery 分页参数
     * @param type 记录类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 硬币记录列表
     */
    PageResult<CoinRecordVO> getCoinRecords(Long uid, PageQuery pageQuery, String type, String startTime, String endTime);

    /**
     * 每日签到
     *
     * @param uid 用户ID
     * @return 签到后的余额信息
     */
    CoinBalanceVO dailyCheckin(Long uid);

    /**
     * 投币给视频
     *
     * @param uid 用户ID
     * @param videoId 视频ID
     * @param amount 投币数量
     * @return 投币后的余额信息
     */
    CoinBalanceVO throwCoin(Long uid, Long videoId, Integer amount);

    /**
     * 增加硬币
     *
     * @param uid 用户ID
     * @param amount 增加数量
     * @param sourceType 来源类型
     * @param sourceId 来源ID
     * @param description 描述
     */
    void addCoins(Long uid, Double amount, Integer sourceType, Long sourceId, String description);

    /**
     * 扣除硬币
     *
     * @param uid 用户ID
     * @param amount 扣除数量
     * @param sourceType 来源类型
     * @param sourceId 来源ID
     * @param description 描述
     */
    void deductCoins(Long uid, Double amount, Integer sourceType, Long sourceId, String description);
}
