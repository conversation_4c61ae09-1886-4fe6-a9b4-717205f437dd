package com.bilibili.user.service;

import com.bilibili.common.dto.PageQuery;
import com.bilibili.common.dto.PageResult;
import com.bilibili.user.entity.dto.AddBlacklistDTO;
import com.bilibili.user.entity.vo.BlacklistVO;

/**
 * 黑名单服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface BlacklistService {

    /**
     * 获取黑名单列表
     *
     * @param uid 用户ID
     * @param pageQuery 分页参数
     * @return 黑名单列表
     */
    PageResult<BlacklistVO> getBlacklistList(Long uid, PageQuery pageQuery);

    /**
     * 添加到黑名单
     *
     * @param uid 用户ID
     * @param addBlacklistDTO 添加黑名单请求
     */
    void addToBlacklist(Long uid, AddBlacklistDTO addBlacklistDTO);

    /**
     * 从黑名单移除
     *
     * @param uid 用户ID
     * @param targetUid 目标用户ID
     */
    void removeFromBlacklist(Long uid, Long targetUid);

    /**
     * 检查是否已拉黑
     *
     * @param uid 用户ID
     * @param targetUid 目标用户ID
     * @return 是否已拉黑
     */
    boolean isBlocked(Long uid, Long targetUid);

    /**
     * 批量移除黑名单
     *
     * @param uid 用户ID
     * @param targetUids 目标用户ID数组
     */
    void batchRemoveFromBlacklist(Long uid, Long[] targetUids);
}
