package com.bilibili.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bilibili.user.entity.po.*;
import com.bilibili.user.entity.vo.PersonalCenterVO;
import com.bilibili.user.mapper.*;
import com.bilibili.user.service.PersonalCenterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 个人中心服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonalCenterServiceImpl implements PersonalCenterService {

    private final UsersMapper usersMapper;
    private final UserStatsMapper userStatsMapper;
    private final UserVipMapper userVipMapper;
    private final UserBalanceMapper userBalanceMapper;
    private final UserSecurityMapper userSecurityMapper;

    @Override
    public PersonalCenterVO getPersonalCenterHome(Long uid) {
        log.info("获取个人中心首页数据: uid={}", uid);

        // 获取用户基本信息
        Users user = usersMapper.selectOne(new LambdaQueryWrapper<Users>().eq(Users::getUid, uid));
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 获取用户统计信息
        UserStats userStats = userStatsMapper.selectByUid(uid);
        
        // 获取VIP信息
        UserVip userVip = userVipMapper.selectOne(new LambdaQueryWrapper<UserVip>().eq(UserVip::getUid, uid));
        
        // 获取余额信息
        UserBalance userBalance = userBalanceMapper.selectOne(new LambdaQueryWrapper<UserBalance>().eq(UserBalance::getUid, uid));
        
        // 获取安全信息
        UserSecurity userSecurity = userSecurityMapper.selectOne(new LambdaQueryWrapper<UserSecurity>().eq(UserSecurity::getUid, uid));

        return PersonalCenterVO.builder()
                .userInfo(buildUserBasicInfo(user))
                .vipInfo(buildVipInfo(userVip))
                .coinInfo(buildCoinInfo(userBalance))
                .statsInfo(buildStatsInfo(userStats))
                .securityInfo(buildSecurityInfo(user, userSecurity))
                .quickActions(buildQuickActions(uid))
                .build();
    }

    private PersonalCenterVO.UserBasicInfo buildUserBasicInfo(Users user) {
        return PersonalCenterVO.UserBasicInfo.builder()
                .uid(user.getUid())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .level(user.getLevel())
                .exp(user.getExp())
                .realNameVerified(user.getRealNameVerified() != null && user.getRealNameVerified() == 1)
                .verifiedInfo(user.getVerifiedInfo())
                .build();
    }

    private PersonalCenterVO.VipInfo buildVipInfo(UserVip userVip) {
        if (userVip == null) {
            return PersonalCenterVO.VipInfo.builder()
                    .vipType(0)
                    .vipStatus(0)
                    .vipDays(0L)
                    .nearExpiry(false)
                    .build();
        }

        Long vipDays = 0L;
        Boolean nearExpiry = false;
        
        if (userVip.getVipEndTime() != null) {
            vipDays = ChronoUnit.DAYS.between(LocalDateTime.now(), userVip.getVipEndTime());
            nearExpiry = vipDays <= 7 && vipDays > 0; // 7天内到期
        }

        return PersonalCenterVO.VipInfo.builder()
                .vipType(userVip.getVipType())
                .vipStatus(userVip.getVipStatus())
                .vipDueDate(userVip.getVipEndTime())
                .vipDays(Math.max(vipDays, 0L))
                .nearExpiry(nearExpiry)
                .build();
    }

    private PersonalCenterVO.CoinInfo buildCoinInfo(UserBalance userBalance) {
        if (userBalance == null) {
            return PersonalCenterVO.CoinInfo.builder()
                    .coins(0.0)
                    .bCoin(0)
                    .todayEarned(0.0)
                    .todaySpent(0.0)
                    .build();
        }

        // TODO: 计算今日收入和支出，需要查询硬币记录表
        return PersonalCenterVO.CoinInfo.builder()
                .coins(userBalance.getBalance().doubleValue())
                .bCoin(userBalance.getBCoin())
                .todayEarned(0.0) // 需要从硬币记录表计算
                .todaySpent(0.0)  // 需要从硬币记录表计算
                .build();
    }

    private PersonalCenterVO.StatsInfo buildStatsInfo(UserStats userStats) {
        if (userStats == null) {
            return PersonalCenterVO.StatsInfo.builder()
                    .followingCount(0)
                    .followerCount(0)
                    .likeCount(0L)
                    .viewCount(0L)
                    .videoCount(0)
                    .build();
        }

        return PersonalCenterVO.StatsInfo.builder()
                .followingCount(userStats.getFollowingCount())
                .followerCount(userStats.getFollowerCount())
                .likeCount(userStats.getLikeCount())
                .viewCount(userStats.getViewCount())
                .videoCount(userStats.getVideoCount())
                .build();
    }

    private PersonalCenterVO.SecurityInfo buildSecurityInfo(Users user, UserSecurity userSecurity) {
        return PersonalCenterVO.SecurityInfo.builder()
                .phoneBound(user.getPhoneBound() != null && user.getPhoneBound())
                .emailBound(user.getEmail() != null && !user.getEmail().isEmpty())
                .twoFactorEnabled(userSecurity != null && userSecurity.getTwoFactorEnabled() == 1)
                .lastLoginTime(user.getLastLoginTime())
                .lastLoginIp(user.getLastLoginIp())
                .build();
    }

    private PersonalCenterVO.QuickActions buildQuickActions(Long uid) {
        // TODO: 实现具体的快捷功能统计
        return PersonalCenterVO.QuickActions.builder()
                .unreadMessages(0)
                .pendingTasks(0)
                .newFollowers(0)
                .hasSystemNotice(false)
                .build();
    }
}
