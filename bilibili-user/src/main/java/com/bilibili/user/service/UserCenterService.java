package com.bilibili.user.service;

import com.bilibili.user.entity.dto.PrivacySettingsUpdateRequest;
import com.bilibili.user.entity.dto.UserProfileUpdateRequest;
import com.bilibili.user.entity.po.UserActions;
import com.bilibili.user.entity.po.UserFavorites;
import com.bilibili.user.entity.po.UserPrivacySettings;
import com.bilibili.user.entity.vo.UserProfileVO;

import java.util.List;

/**
 * 用户个人中心服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface UserCenterService {

    /**
     * 获取用户个人资料
     *
     * @param uid 用户ID
     * @return 用户个人资料
     */
    UserProfileVO getUserProfile(Long uid);

    /**
     * 更新用户个人资料
     *
     * @param uid     用户ID
     * @param request 更新请求
     * @return 是否成功
     */
    boolean updateUserProfile(Long uid, UserProfileUpdateRequest request);

    /**
     * 获取用户隐私设置
     *
     * @param uid 用户ID
     * @return 隐私设置
     */
    UserPrivacySettings getPrivacySettings(Long uid);

    /**
     * 更新用户隐私设置
     *
     * @param uid     用户ID
     * @param request 更新请求
     * @return 是否成功
     */
    boolean updatePrivacySettings(Long uid, PrivacySettingsUpdateRequest request);

    /**
     * 获取用户收藏夹列表
     *
     * @param uid 用户ID
     * @return 收藏夹列表
     */
    List<UserFavorites> getUserFavorites(Long uid);

    /**
     * 创建收藏夹
     *
     * @param uid         用户ID
     * @param title       标题
     * @param description 描述
     * @param isPublic    是否公开
     * @param type        类型
     * @return 收藏夹ID
     */
    Long createFavorite(Long uid, String title, String description, Integer isPublic, Integer type);

    /**
     * 删除收藏夹
     *
     * @param uid        用户ID
     * @param favoriteId 收藏夹ID
     * @return 是否成功
     */
    boolean deleteFavorite(Long uid, Long favoriteId);

    /**
     * 获取用户最近行为记录
     *
     * @param uid        用户ID
     * @param actionType 行为类型
     * @param limit      限制数量
     * @return 行为记录列表
     */
    List<UserActions> getUserRecentActions(Long uid, Integer actionType, Integer limit);

    /**
     * 获取用户最近点赞的视频
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 点赞记录列表
     */
    List<UserActions> getUserRecentLikes(Long uid, Integer limit);

    /**
     * 获取用户最近投币的视频
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 投币记录列表
     */
    List<UserActions> getUserRecentCoins(Long uid, Integer limit);

    /**
     * 获取用户最近收藏的视频
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 收藏记录列表
     */
    List<UserActions> getUserRecentFavorites(Long uid, Integer limit);

    /**
     * 获取用户最近观看的视频
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 观看记录列表
     */
    List<UserActions> getUserRecentViews(Long uid, Integer limit);

    /**
     * 添加用户行为记录
     *
     * @param uid          用户ID
     * @param actionType   行为类型
     * @param targetType   目标类型
     * @param targetId     目标ID
     * @param targetTitle  目标标题
     * @param targetAuthor 目标作者
     * @param targetCover  目标封面
     * @return 是否成功
     */
    boolean addUserAction(Long uid, Integer actionType, Integer targetType, Long targetId,
                          String targetTitle, String targetAuthor, String targetCover);

    /**
     * 设置密码
     *
     * @param uid      用户ID
     * @param password 新密码
     * @return 是否成功
     */
    boolean setPassword(Long uid, String password);

    /**
     * 修改密码
     *
     * @param uid         用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(Long uid, String oldPassword, String newPassword);

    /**
     * 绑定手机号
     *
     * @param uid   用户ID
     * @param phone 手机号
     * @param code  验证码
     * @return 是否成功
     */
    boolean bindPhone(Long uid, String phone, String code);



    /**
     * 获取用户黑名单
     *
     * @param uid 用户ID
     * @return 黑名单列表
     */
    List<Object> getUserBlacklist(Long uid);

    /**
     * 添加到黑名单
     *
     * @param uid       用户ID
     * @param targetUid 目标用户ID
     * @param reason    原因
     * @return 是否成功
     */
    boolean addToBlacklist(Long uid, Long targetUid, String reason);

    /**
     * 从黑名单移除
     *
     * @param uid       用户ID
     * @param targetUid 目标用户ID
     * @return 是否成功
     */
    boolean removeFromBlacklist(Long uid, Long targetUid);
}
