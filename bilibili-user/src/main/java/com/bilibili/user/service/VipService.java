package com.bilibili.user.service;

import com.bilibili.user.entity.dto.VipPurchaseDTO;
import com.bilibili.user.entity.vo.VipInfoVO;
import com.bilibili.user.entity.vo.VipPrivilegeVO;
import com.bilibili.user.entity.vo.VipPackageVO;

import java.util.List;

/**
 * VIP服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface VipService {

    /**
     * 获取VIP信息
     *
     * @param uid 用户ID
     * @return VIP信息
     */
    VipInfoVO getVipInfo(Long uid);

    /**
     * 获取VIP特权
     *
     * @param uid 用户ID
     * @return VIP特权列表
     */
    List<VipPrivilegeVO> getVipPrivileges(Long uid);

    /**
     * 购买VIP
     *
     * @param uid 用户ID
     * @param vipPurchaseDTO 购买信息
     * @return 购买后的VIP信息
     */
    VipInfoVO purchaseVip(Long uid, VipPurchaseDTO vipPurchaseDTO);

    /**
     * 开启自动续费
     *
     * @param uid 用户ID
     */
    void enableAutoRenew(Long uid);

    /**
     * 关闭自动续费
     *
     * @param uid 用户ID
     */
    void disableAutoRenew(Long uid);

    /**
     * 获取VIP套餐
     *
     * @return VIP套餐列表
     */
    List<VipPackageVO> getVipPackages();

    /**
     * 检查VIP状态
     *
     * @param uid 用户ID
     * @return 是否为VIP
     */
    boolean isVip(Long uid);

    /**
     * 检查VIP特权
     *
     * @param uid 用户ID
     * @param privilegeId 特权ID
     * @return 是否拥有特权
     */
    boolean hasPrivilege(Long uid, String privilegeId);
}
