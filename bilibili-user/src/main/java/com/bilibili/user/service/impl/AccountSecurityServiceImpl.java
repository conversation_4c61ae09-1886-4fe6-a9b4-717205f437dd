package com.bilibili.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bilibili.common.exception.ServiceException;
import com.bilibili.user.entity.dto.BindPhoneDTO;
import com.bilibili.user.entity.dto.BindWechatDTO;
import com.bilibili.user.entity.dto.ChangePasswordDTO;
import com.bilibili.user.entity.dto.RealNameAuthDTO;
import com.bilibili.user.entity.po.Users;
import com.bilibili.user.entity.po.UserSecurity;
import com.bilibili.user.entity.vo.SecurityStatusVO;
import com.bilibili.user.mapper.UsersMapper;
import com.bilibili.user.mapper.UserSecurityMapper;
import com.bilibili.user.service.AccountSecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * 账号安全服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountSecurityServiceImpl implements AccountSecurityService {

    private final UsersMapper usersMapper;
    private final UserSecurityMapper userSecurityMapper;
    private final PasswordEncoder passwordEncoder;

    @Override
    public SecurityStatusVO getSecurityStatus(Long uid) {
        log.info("获取安全状态: uid={}", uid);

        Users user = getUserByUid(uid);
        UserSecurity userSecurity = getUserSecurity(uid);

        return SecurityStatusVO.builder()
                .passwordSecurity(buildPasswordSecurity(user, userSecurity))
                .phoneBinding(buildPhoneBinding(user))
                .wechatBinding(buildWechatBinding(user))
                .realNameAuth(buildRealNameAuth(user))
                .securityScore(buildSecurityScore(user, userSecurity))
                .build();
    }

    @Override
    @Transactional
    public void changePassword(Long uid, ChangePasswordDTO changePasswordDTO) {
        log.info("修改密码: uid={}", uid);

        // 验证新密码和确认密码是否一致
        if (!changePasswordDTO.getNewPassword().equals(changePasswordDTO.getConfirmPassword())) {
            throw new ServiceException("新密码和确认密码不一致");
        }

        Users user = getUserByUid(uid);

        // 验证原密码
        if (!passwordEncoder.matches(changePasswordDTO.getOldPassword(), user.getPasswordHash())) {
            throw new ServiceException("原密码错误");
        }

        // TODO: 验证验证码
        // verificationCodeService.verifyCode(user.getPhone(), changePasswordDTO.getVerificationCode(), "change_password");

        // 更新密码
        String newPasswordHash = passwordEncoder.encode(changePasswordDTO.getNewPassword());
        user.setPasswordHash(newPasswordHash);
        user.setUpdatedAt(LocalDateTime.now());
        usersMapper.updateById(user);

        // 更新安全信息
        updatePasswordSecurity(uid, changePasswordDTO.getNewPassword());

        log.info("密码修改成功: uid={}", uid);
    }

    @Override
    @Transactional
    public void bindPhone(Long uid, BindPhoneDTO bindPhoneDTO) {
        log.info("绑定手机号: uid={}, phone={}", uid, bindPhoneDTO.getPhone());

        Users user = getUserByUid(uid);

        // 检查手机号是否已被其他用户使用
        Users existingUser = usersMapper.selectOne(
                new LambdaQueryWrapper<Users>()
                        .eq(Users::getPhone, bindPhoneDTO.getPhone())
                        .ne(Users::getUid, uid)
        );
        if (existingUser != null) {
            throw new ServiceException("该手机号已被其他用户绑定");
        }

        // TODO: 验证验证码
        // verificationCodeService.verifyCode(bindPhoneDTO.getPhone(), bindPhoneDTO.getVerificationCode(), "bind_phone");

        // 如果是更换手机号，需要验证原手机号
        if (user.getPhoneBound() && bindPhoneDTO.getOldPhoneCode() != null) {
            // TODO: 验证原手机号验证码
            // verificationCodeService.verifyCode(user.getPhone(), bindPhoneDTO.getOldPhoneCode(), "unbind_phone");
        }

        // 更新手机号
        user.setPhone(bindPhoneDTO.getPhone());
        user.setPhoneBound(true);
        user.setUpdatedAt(LocalDateTime.now());
        usersMapper.updateById(user);

        log.info("手机号绑定成功: uid={}, phone={}", uid, bindPhoneDTO.getPhone());
    }

    @Override
    @Transactional
    public void unbindPhone(Long uid, String code) {
        log.info("解绑手机号: uid={}", uid);

        Users user = getUserByUid(uid);

        if (!user.getPhoneBound()) {
            throw new ServiceException("未绑定手机号");
        }

        // 检查是否可以解绑（需要有其他安全验证方式）
        if (user.getWechatOpenId() == null || user.getWechatOpenId().isEmpty()) {
            throw new ServiceException("请先绑定微信后再解绑手机号");
        }

        // TODO: 验证验证码
        // verificationCodeService.verifyCode(user.getPhone(), code, "unbind_phone");

        // 解绑手机号
        user.setPhone(null);
        user.setPhoneBound(false);
        user.setUpdatedAt(LocalDateTime.now());
        usersMapper.updateById(user);

        log.info("手机号解绑成功: uid={}", uid);
    }

    @Override
    @Transactional
    public void bindWechat(Long uid, BindWechatDTO bindWechatDTO) {
        log.info("绑定微信: uid={}", uid);

        Users user = getUserByUid(uid);

        // TODO: 通过微信授权码获取微信用户信息
        // WechatUserInfo wechatUserInfo = wechatService.getUserInfo(bindWechatDTO.getCode());

        // 检查微信是否已被其他用户绑定
        Users existingUser = usersMapper.selectOne(
                new LambdaQueryWrapper<Users>()
                        .eq(Users::getWechatOpenId, "mock_openid") // TODO: 使用真实的openid
                        .ne(Users::getUid, uid)
        );
        if (existingUser != null) {
            throw new ServiceException("该微信账号已被其他用户绑定");
        }

        // 更新微信信息
        user.setWechatOpenId("mock_openid"); // TODO: 使用真实的openid
        user.setWechatUnionId("mock_unionid"); // TODO: 使用真实的unionid
        user.setUpdatedAt(LocalDateTime.now());
        usersMapper.updateById(user);

        log.info("微信绑定成功: uid={}", uid);
    }

    @Override
    @Transactional
    public void unbindWechat(Long uid, String code) {
        log.info("解绑微信: uid={}", uid);

        Users user = getUserByUid(uid);

        if (user.getWechatOpenId() == null || user.getWechatOpenId().isEmpty()) {
            throw new ServiceException("未绑定微信");
        }

        // 检查是否可以解绑（需要有其他安全验证方式）
        if (!user.getPhoneBound()) {
            throw new ServiceException("请先绑定手机号后再解绑微信");
        }

        // TODO: 验证验证码
        // verificationCodeService.verifyCode(user.getPhone(), code, "unbind_wechat");

        // 解绑微信
        user.setWechatOpenId(null);
        user.setWechatUnionId(null);
        user.setUpdatedAt(LocalDateTime.now());
        usersMapper.updateById(user);

        log.info("微信解绑成功: uid={}", uid);
    }

    @Override
    @Transactional
    public void realNameAuth(Long uid, RealNameAuthDTO realNameAuthDTO) {
        log.info("实名认证: uid={}, realName={}", uid, realNameAuthDTO.getRealName());

        Users user = getUserByUid(uid);

        if (user.getRealNameVerified() != null && user.getRealNameVerified() == 1) {
            throw new ServiceException("已完成实名认证");
        }

        // TODO: 调用第三方实名认证接口验证身份信息
        // boolean authResult = realNameAuthService.verify(realNameAuthDTO);

        // 更新实名信息
        user.setRealName(realNameAuthDTO.getRealName());
        user.setIdCard(realNameAuthDTO.getIdCard());
        user.setRealNameVerified(1); // 假设认证通过，实际应该是审核状态
        user.setUpdatedAt(LocalDateTime.now());
        usersMapper.updateById(user);

        log.info("实名认证提交成功: uid={}", uid);
    }

    // 私有方法
    private Users getUserByUid(Long uid) {
        Users user = usersMapper.selectOne(new LambdaQueryWrapper<Users>().eq(Users::getUid, uid));
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        return user;
    }

    private UserSecurity getUserSecurity(Long uid) {
        return userSecurityMapper.selectOne(new LambdaQueryWrapper<UserSecurity>().eq(UserSecurity::getUid, uid));
    }

    private SecurityStatusVO.PasswordSecurity buildPasswordSecurity(Users user, UserSecurity userSecurity) {
        int strength = calculatePasswordStrength(user.getPasswordHash());
        LocalDateTime lastChangeTime = userSecurity != null ? userSecurity.getLastPasswordChange() : user.getCreatedAt();
        boolean needChange = lastChangeTime != null && ChronoUnit.DAYS.between(lastChangeTime, LocalDateTime.now()) > 90;

        return SecurityStatusVO.PasswordSecurity.builder()
                .strength(strength)
                .lastChangeTime(lastChangeTime)
                .needChange(needChange)
                .suggestion(strength < 3 ? "建议使用更复杂的密码" : "密码强度良好")
                .build();
    }

    private SecurityStatusVO.PhoneBinding buildPhoneBinding(Users user) {
        return SecurityStatusVO.PhoneBinding.builder()
                .bound(user.getPhoneBound() != null && user.getPhoneBound())
                .phoneNumber(user.getPhone() != null ? maskPhone(user.getPhone()) : null)
                .bindTime(user.getCreatedAt()) // TODO: 使用实际绑定时间
                .canUnbind(user.getWechatOpenId() != null && !user.getWechatOpenId().isEmpty())
                .build();
    }

    private SecurityStatusVO.WechatBinding buildWechatBinding(Users user) {
        boolean bound = user.getWechatOpenId() != null && !user.getWechatOpenId().isEmpty();
        return SecurityStatusVO.WechatBinding.builder()
                .bound(bound)
                .wechatNickname(bound ? "微信用户" : null) // TODO: 获取真实微信昵称
                .wechatAvatar(user.getAvatar())
                .bindTime(user.getCreatedAt()) // TODO: 使用实际绑定时间
                .canUnbind(user.getPhoneBound() != null && user.getPhoneBound())
                .build();
    }

    private SecurityStatusVO.RealNameAuth buildRealNameAuth(Users user) {
        int status = user.getRealNameVerified() != null ? user.getRealNameVerified() : 0;
        return SecurityStatusVO.RealNameAuth.builder()
                .status(status)
                .realName(user.getRealName() != null ? maskName(user.getRealName()) : null)
                .idCard(user.getIdCard() != null ? maskIdCard(user.getIdCard()) : null)
                .authTime(status > 0 ? user.getUpdatedAt() : null)
                .failReason(null)
                .build();
    }

    private SecurityStatusVO.SecurityScore buildSecurityScore(Users user, UserSecurity userSecurity) {
        int score = 0;
        List<String> suggestions = new ArrayList<>();
        List<String> risks = new ArrayList<>();

        // 密码强度评分
        int passwordStrength = calculatePasswordStrength(user.getPasswordHash());
        score += passwordStrength * 20;
        if (passwordStrength < 3) {
            suggestions.add("提高密码强度");
            risks.add("密码强度较低");
        }

        // 手机绑定评分
        if (user.getPhoneBound() != null && user.getPhoneBound()) {
            score += 25;
        } else {
            suggestions.add("绑定手机号");
            risks.add("未绑定手机号");
        }

        // 微信绑定评分
        if (user.getWechatOpenId() != null && !user.getWechatOpenId().isEmpty()) {
            score += 20;
        } else {
            suggestions.add("绑定微信账号");
        }

        // 实名认证评分
        if (user.getRealNameVerified() != null && user.getRealNameVerified() == 1) {
            score += 35;
        } else {
            suggestions.add("完成实名认证");
            risks.add("未完成实名认证");
        }

        String level = score >= 80 ? "高" : score >= 60 ? "中" : "低";

        return SecurityStatusVO.SecurityScore.builder()
                .totalScore(score)
                .level(level)
                .suggestions(suggestions.toArray(new String[0]))
                .risks(risks.toArray(new String[0]))
                .build();
    }

    private void updatePasswordSecurity(Long uid, String newPassword) {
        UserSecurity userSecurity = getUserSecurity(uid);
        if (userSecurity == null) {
            userSecurity = UserSecurity.builder()
                    .uid(uid)
                    .passwordStrength(calculatePasswordStrength(newPassword))
                    .lastPasswordChange(LocalDateTime.now())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            userSecurityMapper.insert(userSecurity);
        } else {
            userSecurity.setPasswordStrength(calculatePasswordStrength(newPassword));
            userSecurity.setLastPasswordChange(LocalDateTime.now());
            userSecurity.setUpdatedAt(LocalDateTime.now());
            userSecurityMapper.updateById(userSecurity);
        }
    }

    private int calculatePasswordStrength(String password) {
        // 简单的密码强度计算
        if (password == null || password.length() < 6) return 1;
        if (password.length() >= 8 && password.matches(".*[a-zA-Z].*") && password.matches(".*\\d.*")) {
            if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) {
                return 3; // 强
            }
            return 2; // 中
        }
        return 1; // 弱
    }

    private String maskPhone(String phone) {
        if (phone == null || phone.length() != 11) return phone;
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    private String maskName(String name) {
        if (name == null || name.length() <= 1) return name;
        return name.charAt(0) + "*".repeat(name.length() - 1);
    }

    private String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() != 18) return idCard;
        return idCard.substring(0, 6) + "********" + idCard.substring(14);
    }
}
