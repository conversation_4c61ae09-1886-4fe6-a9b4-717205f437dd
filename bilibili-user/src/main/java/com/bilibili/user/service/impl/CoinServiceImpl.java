package com.bilibili.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bilibili.common.dto.PageQuery;
import com.bilibili.common.dto.PageResult;
import com.bilibili.common.exception.ServiceException;
import com.bilibili.user.entity.po.UserBalance;
import com.bilibili.user.entity.po.UserCoinRecord;
import com.bilibili.user.entity.vo.CoinBalanceVO;
import com.bilibili.user.entity.vo.CoinRecordVO;
import com.bilibili.user.mapper.UserBalanceMapper;
import com.bilibili.user.mapper.UserCoinRecordMapper;
import com.bilibili.user.service.CoinService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 硬币服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoinServiceImpl implements CoinService {

    private final UserBalanceMapper userBalanceMapper;
    private final UserCoinRecordMapper userCoinRecordMapper;

    @Override
    public CoinBalanceVO getCoinBalance(Long uid) {
        log.info("获取硬币余额: uid={}", uid);

        UserBalance userBalance = getUserBalance(uid);
        
        // 计算今日收支
        LocalDateTime todayStart = LocalDate.now().atStartOfDay();
        LocalDateTime todayEnd = LocalDate.now().atTime(LocalTime.MAX);
        BigDecimal todayEarned = userCoinRecordMapper.sumIncomeByPeriod(uid, todayStart, todayEnd);
        BigDecimal todaySpent = userCoinRecordMapper.sumExpenseByPeriod(uid, todayStart, todayEnd);

        // 计算本月收支
        LocalDateTime monthStart = LocalDate.now().withDayOfMonth(1).atStartOfDay();
        LocalDateTime monthEnd = LocalDate.now().atTime(LocalTime.MAX);
        BigDecimal monthEarned = userCoinRecordMapper.sumIncomeByPeriod(uid, monthStart, monthEnd);
        BigDecimal monthSpent = userCoinRecordMapper.sumExpenseByPeriod(uid, monthStart, monthEnd);

        // 检查签到状态
        boolean checkedIn = userCoinRecordMapper.hasCheckedInToday(uid);
        int consecutiveDays = userCoinRecordMapper.getConsecutiveCheckinDays(uid);

        return CoinBalanceVO.builder()
                .balance(userBalance.getBalance())
                .bCoin(userBalance.getBCoin())
                .frozenAmount(userBalance.getFrozenAmount())
                .availableBalance(userBalance.getBalance().subtract(userBalance.getFrozenAmount()))
                .todayEarned(todayEarned)
                .todaySpent(todaySpent)
                .monthEarned(monthEarned)
                .monthSpent(monthSpent)
                .totalIncome(userBalance.getTotalIncome())
                .totalExpense(userBalance.getTotalExpense())
                .checkedIn(checkedIn)
                .consecutiveDays(consecutiveDays)
                .nextCheckinReward(calculateCheckinReward(consecutiveDays + 1))
                .lastUpdateTime(userBalance.getUpdatedAt())
                .build();
    }

    @Override
    public PageResult<CoinRecordVO> getCoinRecords(Long uid, PageQuery pageQuery, String type, String startTime, String endTime) {
        log.info("获取硬币记录: uid={}, type={}", uid, type);

        Page<UserCoinRecord> page = new Page<>(pageQuery.getPage(), pageQuery.getSize());
        
        Integer changeType = null;
        Integer sourceType = null;
        
        // 解析类型参数
        if ("income".equals(type)) {
            changeType = UserCoinRecord.ChangeType.EARN.getCode();
        } else if ("expense".equals(type)) {
            changeType = UserCoinRecord.ChangeType.SPEND.getCode();
        } else if (type != null && type.matches("\\d+")) {
            sourceType = Integer.parseInt(type);
        }

        // 解析时间参数
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;
        if (startTime != null) {
            startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        if (endTime != null) {
            endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        IPage<UserCoinRecord> recordPage = userCoinRecordMapper.selectPageByCondition(
                page, uid, changeType, sourceType, startDateTime, endDateTime);

        List<CoinRecordVO> records = recordPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.of(records, recordPage.getTotal(), pageQuery.getPage(), pageQuery.getSize());
    }

    @Override
    @Transactional
    public CoinBalanceVO dailyCheckin(Long uid) {
        log.info("每日签到: uid={}", uid);

        // 检查今日是否已签到
        if (userCoinRecordMapper.hasCheckedInToday(uid)) {
            throw new ServiceException("今日已签到");
        }

        // 计算签到奖励
        int consecutiveDays = userCoinRecordMapper.getConsecutiveCheckinDays(uid);
        BigDecimal reward = calculateCheckinReward(consecutiveDays + 1);

        // 增加硬币
        addCoins(uid, reward.doubleValue(), UserCoinRecord.SourceType.CHECKIN.getCode(), null, 
                String.format("每日签到奖励（连续%d天）", consecutiveDays + 1));

        return getCoinBalance(uid);
    }

    @Override
    @Transactional
    public CoinBalanceVO throwCoin(Long uid, Long videoId, Integer amount) {
        log.info("投币给视频: uid={}, videoId={}, amount={}", uid, videoId, amount);

        if (amount <= 0 || amount > 2) {
            throw new ServiceException("投币数量必须在1-2之间");
        }

        // 检查今日是否已对该视频投币
        if (userCoinRecordMapper.hasThrownCoinToVideo(uid, videoId)) {
            throw new ServiceException("今日已对该视频投币");
        }

        // 检查余额是否足够
        UserBalance userBalance = getUserBalance(uid);
        if (userBalance.getBalance().compareTo(BigDecimal.valueOf(amount)) < 0) {
            throw new ServiceException("硬币余额不足");
        }

        // 扣除硬币
        deductCoins(uid, amount.doubleValue(), UserCoinRecord.SourceType.THROW_COIN.getCode(), videoId, 
                String.format("给视频投币 %d 个", amount));

        return getCoinBalance(uid);
    }

    @Override
    @Transactional
    public void addCoins(Long uid, Double amount, Integer sourceType, Long sourceId, String description) {
        log.info("增加硬币: uid={}, amount={}, sourceType={}", uid, amount, sourceType);

        UserBalance userBalance = getUserBalance(uid);
        BigDecimal changeAmount = BigDecimal.valueOf(amount);
        BigDecimal newBalance = userBalance.getBalance().add(changeAmount);

        // 更新余额
        userBalance.setBalance(newBalance);
        userBalance.setTotalIncome(userBalance.getTotalIncome().add(changeAmount));
        userBalance.setUpdatedAt(LocalDateTime.now());
        userBalanceMapper.updateById(userBalance);

        // 记录变动
        UserCoinRecord record = UserCoinRecord.builder()
                .uid(uid)
                .changeType(UserCoinRecord.ChangeType.EARN.getCode())
                .changeAmount(changeAmount)
                .balanceAfter(newBalance)
                .sourceType(sourceType)
                .sourceId(sourceId)
                .description(description)
                .createdAt(LocalDateTime.now())
                .build();
        userCoinRecordMapper.insert(record);
    }

    @Override
    @Transactional
    public void deductCoins(Long uid, Double amount, Integer sourceType, Long sourceId, String description) {
        log.info("扣除硬币: uid={}, amount={}, sourceType={}", uid, amount, sourceType);

        UserBalance userBalance = getUserBalance(uid);
        BigDecimal changeAmount = BigDecimal.valueOf(amount);
        
        if (userBalance.getBalance().compareTo(changeAmount) < 0) {
            throw new ServiceException("硬币余额不足");
        }

        BigDecimal newBalance = userBalance.getBalance().subtract(changeAmount);

        // 更新余额
        userBalance.setBalance(newBalance);
        userBalance.setTotalExpense(userBalance.getTotalExpense().add(changeAmount));
        userBalance.setUpdatedAt(LocalDateTime.now());
        userBalanceMapper.updateById(userBalance);

        // 记录变动
        UserCoinRecord record = UserCoinRecord.builder()
                .uid(uid)
                .changeType(UserCoinRecord.ChangeType.SPEND.getCode())
                .changeAmount(changeAmount)
                .balanceAfter(newBalance)
                .sourceType(sourceType)
                .sourceId(sourceId)
                .description(description)
                .createdAt(LocalDateTime.now())
                .build();
        userCoinRecordMapper.insert(record);
    }

    // 私有方法
    private UserBalance getUserBalance(Long uid) {
        UserBalance userBalance = userBalanceMapper.selectOne(
                new LambdaQueryWrapper<UserBalance>().eq(UserBalance::getUid, uid));
        
        if (userBalance == null) {
            // 创建默认余额记录
            userBalance = UserBalance.builder()
                    .uid(uid)
                    .balance(BigDecimal.ZERO)
                    .bCoin(0)
                    .frozenAmount(BigDecimal.ZERO)
                    .totalIncome(BigDecimal.ZERO)
                    .totalExpense(BigDecimal.ZERO)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            userBalanceMapper.insert(userBalance);
        }
        
        return userBalance;
    }

    private BigDecimal calculateCheckinReward(int consecutiveDays) {
        // 签到奖励规则：基础1硬币，连续签到有额外奖励
        if (consecutiveDays <= 7) {
            return BigDecimal.valueOf(1);
        } else if (consecutiveDays <= 14) {
            return BigDecimal.valueOf(2);
        } else if (consecutiveDays <= 30) {
            return BigDecimal.valueOf(3);
        } else {
            return BigDecimal.valueOf(5);
        }
    }

    private CoinRecordVO convertToVO(UserCoinRecord record) {
        UserCoinRecord.SourceType sourceType = UserCoinRecord.SourceType.fromCode(record.getSourceType());
        boolean isIncome = record.getChangeType().equals(UserCoinRecord.ChangeType.EARN.getCode());
        
        return CoinRecordVO.builder()
                .id(record.getId())
                .type(sourceType.name().toLowerCase())
                .typeDesc(sourceType.getDesc())
                .amount(record.getChangeAmount())
                .balance(record.getBalanceAfter())
                .description(record.getDescription())
                .relatedId(record.getSourceId())
                .relatedType(getRelatedType(record.getSourceType()))
                .relatedTitle(getRelatedTitle(record.getSourceType(), record.getSourceId()))
                .status("completed")
                .createdAt(record.getCreatedAt())
                .isIncome(isIncome)
                .icon(getIcon(record.getSourceType()))
                .color(isIncome ? "#52c41a" : "#ff4d4f")
                .build();
    }

    private String getRelatedType(Integer sourceType) {
        UserCoinRecord.SourceType type = UserCoinRecord.SourceType.fromCode(sourceType);
        switch (type) {
            case THROW_COIN:
                return "video";
            case REWARD:
                return "user";
            case UPLOAD:
                return "video";
            default:
                return "system";
        }
    }

    private String getRelatedTitle(Integer sourceType, Long sourceId) {
        // TODO: 根据sourceType和sourceId获取相关对象的标题
        UserCoinRecord.SourceType type = UserCoinRecord.SourceType.fromCode(sourceType);
        switch (type) {
            case THROW_COIN:
                return "视频标题"; // TODO: 从视频服务获取
            case REWARD:
                return "用户昵称"; // TODO: 从用户服务获取
            case UPLOAD:
                return "投稿视频"; // TODO: 从视频服务获取
            default:
                return null;
        }
    }

    private String getIcon(Integer sourceType) {
        UserCoinRecord.SourceType type = UserCoinRecord.SourceType.fromCode(sourceType);
        switch (type) {
            case CHECKIN:
                return "calendar";
            case UPLOAD:
                return "upload";
            case RECHARGE:
                return "wallet";
            case THROW_COIN:
                return "coin";
            case REWARD:
                return "gift";
            case TASK:
                return "trophy";
            case REFUND:
                return "undo";
            default:
                return "info";
        }
    }
}
