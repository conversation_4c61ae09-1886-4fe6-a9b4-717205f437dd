package com.bilibili.user.service;


import com.bilibili.user.entity.po.Users;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bilibili.user.entity.vo.UserInfoVO;

/**
 * <p>
 * 用户基础信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface IUsersService extends IService<Users> {

    UserInfoVO getUserInfo(String username);

    Users getUserInfoByUid(Long uid);
}
