package com.bilibili.user.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bilibili.user.entity.dto.PrivacySettingsUpdateRequest;
import com.bilibili.user.entity.dto.UserProfileUpdateRequest;
import com.bilibili.user.entity.po.*;
import com.bilibili.user.entity.vo.UserProfileVO;
import com.bilibili.user.mapper.*;
import com.bilibili.user.service.UserCenterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户个人中心服务实现
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserCenterServiceImpl implements UserCenterService {

    private final UsersMapper usersMapper;
    private final UserStatsMapper userStatsMapper;
    private final UserPrivacySettingsMapper privacySettingsMapper;
    private final UserFavoritesMapper favoritesMapper;
    private final UserActionsMapper actionsMapper;
    private final UserBlacklistMapper blacklistMapper;
    private final BCryptPasswordEncoder passwordEncoder;

    @Override
    public UserProfileVO getUserProfile(Long uid) {
        log.info("获取用户个人资料: uid={}", uid);

        // 查询用户基本信息
        log.info("开始查询用户基本信息: uid={}", uid);
        Users user;
        try {
            user = usersMapper.selectOne(
                new LambdaQueryWrapper<Users>().eq(Users::getUid, uid)
            );
            log.info("查询用户基本信息结果: user={}", user);

            if (user == null) {
                log.warn("用户不存在: uid={}", uid);
                throw new RuntimeException("用户不存在");
            }
        } catch (Exception e) {
            log.error("查询用户基本信息失败: uid={}, error={}", uid, e.getMessage(), e);
            throw new RuntimeException("用户不存在");
        }
        
        // 查询用户统计信息
        UserStats userStats = userStatsMapper.selectOne(
            new LambdaQueryWrapper<UserStats>().eq(UserStats::getUid, uid)
        );
        
        // 构建返回对象
        UserProfileVO profileVO = new UserProfileVO();
        BeanUtils.copyProperties(user, profileVO);
        
        // 处理个人标签
        if (StringUtils.hasText(user.getPersonalTags())) {
            try {
                List<String> tags = JSON.parseArray(user.getPersonalTags(), String.class);
                profileVO.setPersonalTags(tags);
            } catch (Exception e) {
                log.warn("解析个人标签失败: uid={}, tags={}", uid, user.getPersonalTags());
            }
        }
        
        // 设置统计信息
        if (userStats != null) {
            UserProfileVO.UserStatsVO statsVO = new UserProfileVO.UserStatsVO();
            BeanUtils.copyProperties(userStats, statsVO);
            profileVO.setStats(statsVO);
        }
        
        return profileVO;
    }

    @Override
    @Transactional
    public boolean updateUserProfile(Long uid, UserProfileUpdateRequest request) {
        log.info("更新用户个人资料: uid={}, request={}", uid, request);
        
        try {
            LambdaUpdateWrapper<Users> updateWrapper = new LambdaUpdateWrapper<Users>()
                .eq(Users::getUid, uid);
            
            // 更新昵称
            if (StringUtils.hasText(request.getNickname())) {
                updateWrapper.set(Users::getNickname, request.getNickname());
            }
            
            // 更新个人签名
            if (request.getSignature() != null) {
                updateWrapper.set(Users::getSignature, request.getSignature());
            }
            
            // 更新性别
            if (request.getGender() != null) {
                updateWrapper.set(Users::getGender, request.getGender());
            }
            
            // 更新生日
            if (request.getBirthday() != null) {
                updateWrapper.set(Users::getBirthday, request.getBirthday());
            }
            
            // 更新位置
            if (request.getLocation() != null) {
                updateWrapper.set(Users::getLocation, request.getLocation());
            }
            
            // 更新学校信息
            if (request.getSchool() != null) {
                updateWrapper.set(Users::getSchool, request.getSchool());
            }
            
            // 更新个人标签
            if (request.getPersonalTags() != null) {
                String tagsJson = JSON.toJSONString(request.getPersonalTags());
                updateWrapper.set(Users::getPersonalTags, tagsJson);
            }
            
            // 更新头像
            if (StringUtils.hasText(request.getAvatar())) {
                updateWrapper.set(Users::getAvatar, request.getAvatar());
            }
            
            // 更新横幅
            if (StringUtils.hasText(request.getBanner())) {
                updateWrapper.set(Users::getBanner, request.getBanner());
            }
            
            updateWrapper.set(Users::getUpdatedAt, LocalDateTime.now());
            
            int updated = usersMapper.update(null, updateWrapper);
            return updated > 0;
            
        } catch (Exception e) {
            log.error("更新用户个人资料失败: uid={}, error={}", uid, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public UserPrivacySettings getPrivacySettings(Long uid) {
        log.info("获取用户隐私设置: uid={}", uid);
        
        UserPrivacySettings settings = privacySettingsMapper.findByUid(uid);
        
        // 如果不存在，创建默认设置
        if (settings == null) {
            settings = new UserPrivacySettings();
            settings.setUid(uid);
            settings.setShowFavorites(1);
            settings.setShowBangumi(1);
            settings.setShowCoins(1);
            settings.setShowLikes(1);
            settings.setShowFollowing(1);
            settings.setShowFollowers(1);
            settings.setShowBirthday(1);
            settings.setShowSchool(1);
            settings.setShowGames(1);
            settings.setShowFanBadge(1);
            settings.setShowManga(1);
            settings.setShowFanDress(1);
            settings.setShowLiveReplay(1);
            settings.setShowClassVideo(1);
            settings.setShowChargeVideo(1);
            settings.setCreatedAt(LocalDateTime.now());
            settings.setUpdatedAt(LocalDateTime.now());
            
            privacySettingsMapper.insert(settings);
        }
        
        return settings;
    }

    @Override
    @Transactional
    public boolean updatePrivacySettings(Long uid, PrivacySettingsUpdateRequest request) {
        log.info("更新用户隐私设置: uid={}, request={}", uid, request);
        
        try {
            LambdaUpdateWrapper<UserPrivacySettings> updateWrapper = 
                new LambdaUpdateWrapper<UserPrivacySettings>().eq(UserPrivacySettings::getUid, uid);
            
            if (request.getShowFavorites() != null) {
                updateWrapper.set(UserPrivacySettings::getShowFavorites, request.getShowFavorites());
            }
            if (request.getShowBangumi() != null) {
                updateWrapper.set(UserPrivacySettings::getShowBangumi, request.getShowBangumi());
            }
            if (request.getShowCoins() != null) {
                updateWrapper.set(UserPrivacySettings::getShowCoins, request.getShowCoins());
            }
            if (request.getShowLikes() != null) {
                updateWrapper.set(UserPrivacySettings::getShowLikes, request.getShowLikes());
            }
            if (request.getShowFollowing() != null) {
                updateWrapper.set(UserPrivacySettings::getShowFollowing, request.getShowFollowing());
            }
            if (request.getShowFollowers() != null) {
                updateWrapper.set(UserPrivacySettings::getShowFollowers, request.getShowFollowers());
            }
            if (request.getShowBirthday() != null) {
                updateWrapper.set(UserPrivacySettings::getShowBirthday, request.getShowBirthday());
            }
            if (request.getShowSchool() != null) {
                updateWrapper.set(UserPrivacySettings::getShowSchool, request.getShowSchool());
            }
            if (request.getShowGames() != null) {
                updateWrapper.set(UserPrivacySettings::getShowGames, request.getShowGames());
            }
            if (request.getShowFanBadge() != null) {
                updateWrapper.set(UserPrivacySettings::getShowFanBadge, request.getShowFanBadge());
            }
            if (request.getShowManga() != null) {
                updateWrapper.set(UserPrivacySettings::getShowManga, request.getShowManga());
            }
            if (request.getShowFanDress() != null) {
                updateWrapper.set(UserPrivacySettings::getShowFanDress, request.getShowFanDress());
            }
            if (request.getShowLiveReplay() != null) {
                updateWrapper.set(UserPrivacySettings::getShowLiveReplay, request.getShowLiveReplay());
            }
            if (request.getShowClassVideo() != null) {
                updateWrapper.set(UserPrivacySettings::getShowClassVideo, request.getShowClassVideo());
            }
            if (request.getShowChargeVideo() != null) {
                updateWrapper.set(UserPrivacySettings::getShowChargeVideo, request.getShowChargeVideo());
            }
            
            updateWrapper.set(UserPrivacySettings::getUpdatedAt, LocalDateTime.now());
            
            int updated = privacySettingsMapper.update(null, updateWrapper);
            return updated > 0;
            
        } catch (Exception e) {
            log.error("更新用户隐私设置失败: uid={}, error={}", uid, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<UserFavorites> getUserFavorites(Long uid) {
        log.info("获取用户收藏夹列表: uid={}", uid);
        return favoritesMapper.findByUid(uid);
    }

    @Override
    @Transactional
    public Long createFavorite(Long uid, String title, String description, Integer isPublic, Integer type) {
        log.info("创建收藏夹: uid={}, title={}, isPublic={}, type={}", uid, title, isPublic, type);
        
        try {
            UserFavorites favorite = new UserFavorites();
            favorite.setUid(uid);
            favorite.setTitle(title);
            favorite.setDescription(description);
            favorite.setIsPublic(isPublic);
            favorite.setType(type);
            favorite.setMediaCount(0);
            favorite.setViewCount(0);
            favorite.setSortOrder(0);
            favorite.setCreatedAt(LocalDateTime.now());
            favorite.setUpdatedAt(LocalDateTime.now());
            
            int inserted = favoritesMapper.insert(favorite);
            return inserted > 0 ? favorite.getId() : null;
            
        } catch (Exception e) {
            log.error("创建收藏夹失败: uid={}, error={}", uid, e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean deleteFavorite(Long uid, Long favoriteId) {
        log.info("删除收藏夹: uid={}, favoriteId={}", uid, favoriteId);
        
        try {
            // 检查收藏夹是否属于该用户
            Integer ownership = favoritesMapper.checkOwnership(favoriteId, uid);
            if (ownership == null || ownership == 0) {
                log.warn("收藏夹不属于该用户: uid={}, favoriteId={}", uid, favoriteId);
                return false;
            }
            
            // 删除收藏夹
            int deleted = favoritesMapper.deleteById(favoriteId);
            return deleted > 0;
            
        } catch (Exception e) {
            log.error("删除收藏夹失败: uid={}, favoriteId={}, error={}", uid, favoriteId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<UserActions> getUserRecentActions(Long uid, Integer actionType, Integer limit) {
        log.info("获取用户最近行为记录: uid={}, actionType={}, limit={}", uid, actionType, limit);
        return actionsMapper.findRecentByUidAndActionType(uid, actionType, limit);
    }

    @Override
    public List<UserActions> getUserRecentLikes(Long uid, Integer limit) {
        log.info("获取用户最近点赞: uid={}, limit={}", uid, limit);
        return actionsMapper.findRecentLikes(uid, limit);
    }

    @Override
    public List<UserActions> getUserRecentCoins(Long uid, Integer limit) {
        log.info("获取用户最近投币: uid={}, limit={}", uid, limit);
        return actionsMapper.findRecentCoins(uid, limit);
    }

    @Override
    public List<UserActions> getUserRecentFavorites(Long uid, Integer limit) {
        log.info("获取用户最近收藏: uid={}, limit={}", uid, limit);
        return actionsMapper.findRecentFavorites(uid, limit);
    }

    @Override
    public List<UserActions> getUserRecentViews(Long uid, Integer limit) {
        log.info("获取用户观看历史: uid={}, limit={}", uid, limit);
        return actionsMapper.findRecentViews(uid, limit);
    }

    @Override
    @Transactional
    public boolean addUserAction(Long uid, Integer actionType, Integer targetType, Long targetId,
                                String targetTitle, String targetAuthor, String targetCover) {
        log.info("添加用户行为记录: uid={}, actionType={}, targetType={}, targetId={}", 
                uid, actionType, targetType, targetId);
        
        try {
            UserActions action = new UserActions();
            action.setUid(uid);
            action.setActionType(actionType);
            action.setTargetType(targetType);
            action.setTargetId(targetId);
            action.setTargetTitle(targetTitle);
            action.setTargetAuthor(targetAuthor);
            action.setTargetCover(targetCover);
            action.setCreatedAt(LocalDateTime.now());
            
            int inserted = actionsMapper.insert(action);
            return inserted > 0;
            
        } catch (Exception e) {
            log.error("添加用户行为记录失败: uid={}, error={}", uid, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean setPassword(Long uid, String password) {
        log.info("设置用户密码: uid={}", uid);

        try {
            String encodedPassword = passwordEncoder.encode(password);

            LambdaUpdateWrapper<Users> updateWrapper = new LambdaUpdateWrapper<Users>()
                .eq(Users::getUid, uid)
                .set(Users::getPasswordHash, encodedPassword)
                .set(Users::getUpdatedAt, LocalDateTime.now());

            int updated = usersMapper.update(null, updateWrapper);
            return updated > 0;

        } catch (Exception e) {
            log.error("设置用户密码失败: uid={}, error={}", uid, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean changePassword(Long uid, String oldPassword, String newPassword) {
        log.info("修改用户密码: uid={}", uid);

        try {
            // 查询用户当前密码
            Users user = usersMapper.selectOne(
                new LambdaQueryWrapper<Users>().eq(Users::getUid, uid)
            );

            if (user == null) {
                log.warn("用户不存在: uid={}", uid);
                return false;
            }

            // 验证旧密码
            if (!passwordEncoder.matches(oldPassword, user.getPasswordHash())) {
                log.warn("旧密码不正确: uid={}", uid);
                return false;
            }

            // 设置新密码
            return setPassword(uid, newPassword);

        } catch (Exception e) {
            log.error("修改用户密码失败: uid={}, error={}", uid, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean bindPhone(Long uid, String phone, String code) {
        log.info("绑定手机号: uid={}, phone={}", uid, phone);

        // TODO: 调用bilibili-auth服务验证短信验证码
        // 可以通过Feign客户端调用认证服务的验证码验证接口

        try {
            LambdaUpdateWrapper<Users> updateWrapper = new LambdaUpdateWrapper<Users>()
                .eq(Users::getUid, uid)
                .set(Users::getPhone, phone)
                .set(Users::getPhoneBound, true)
                .set(Users::getUpdatedAt, LocalDateTime.now());

            int updated = usersMapper.update(null, updateWrapper);
            return updated > 0;

        } catch (Exception e) {
            log.error("绑定手机号失败: uid={}, error={}", uid, e.getMessage(), e);
            return false;
        }
    }



    @Override
    public List<Object> getUserBlacklist(Long uid) {
        log.info("获取用户黑名单: uid={}", uid);
        List<UserBlacklist> blacklist = blacklistMapper.findByUid(uid);
        return List.of(blacklist.toArray());
    }

    @Override
    @Transactional
    public boolean addToBlacklist(Long uid, Long targetUid, String reason) {
        log.info("添加黑名单: uid={}, targetUid={}, reason={}", uid, targetUid, reason);
        
        try {
            // 检查是否已经在黑名单中
            Integer exists = blacklistMapper.checkBlacklisted(uid, targetUid);
            if (exists != null && exists > 0) {
                log.warn("用户已在黑名单中: uid={}, targetUid={}", uid, targetUid);
                return true;
            }
            
            // 查询目标用户信息
            Users targetUser = usersMapper.selectOne(
                new LambdaQueryWrapper<Users>().eq(Users::getUid, targetUid)
            );
            
            if (targetUser == null) {
                log.warn("目标用户不存在: targetUid={}", targetUid);
                return false;
            }
            
            // 添加到黑名单
            UserBlacklist blacklist = new UserBlacklist();
            blacklist.setUid(uid);
            blacklist.setTargetUid(targetUid);
            blacklist.setTargetUsername(targetUser.getUsername());
            blacklist.setTargetNickname(targetUser.getNickname());
            blacklist.setTargetAvatar(targetUser.getAvatar());
            blacklist.setReason(reason);
            blacklist.setCreatedAt(LocalDateTime.now());
            
            int inserted = blacklistMapper.insert(blacklist);
            return inserted > 0;
            
        } catch (Exception e) {
            log.error("添加黑名单失败: uid={}, targetUid={}, error={}", uid, targetUid, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean removeFromBlacklist(Long uid, Long targetUid) {
        log.info("移除黑名单: uid={}, targetUid={}", uid, targetUid);
        
        try {
            int deleted = blacklistMapper.deleteByUidAndTargetUid(uid, targetUid);
            return deleted > 0;
            
        } catch (Exception e) {
            log.error("移除黑名单失败: uid={}, targetUid={}, error={}", uid, targetUid, e.getMessage(), e);
            return false;
        }
    }
}
