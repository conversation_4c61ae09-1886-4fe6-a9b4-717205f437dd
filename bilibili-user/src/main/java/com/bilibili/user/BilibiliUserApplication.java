package com.bilibili.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Bilibili用户服务启动类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@SpringBootApplication
@MapperScan("com.bilibili.user.mapper")
@RefreshScope
@EnableFeignClients
public class BilibiliUserApplication {

    public static void main(String[] args) {
        // 先显示图案
        printBilibiliLogo();

        try {
            SpringApplication.run(BilibiliUserApplication.class, args);

            // 启动成功后的信息
            printSuccessInfo();

        } catch (Exception e) {
            System.out.println();
            System.out.println("\033[31m           ❌ 启动失败 ❌\033[0m");
            System.out.println("错误信息: " + e.getMessage());
            System.out.println("请检查: 1.MySQL是否启动 2.Nacos配置是否正确 3.Auth服务是否启动");
        }
    }

    private static void printBilibiliLogo() {
        System.out.println();
        System.out.println("\033[36m    ██╗   ██╗███████╗███████╗██████╗ \033[0m");
        System.out.println("\033[36m    ██║   ██║██╔════╝██╔════╝██╔══██╗\033[0m");
        System.out.println("\033[36m    ██║   ██║███████╗█████╗  ██████╔╝\033[0m");
        System.out.println("\033[36m    ██║   ██║╚════██║██╔══╝  ██╔══██╗\033[0m");
        System.out.println("\033[36m    ╚██████╔╝███████║███████╗██║  ██║\033[0m");
        System.out.println("\033[36m     ╚═════╝ ╚══════╝╚══════╝╚═╝  ╚═╝\033[0m");
        System.out.println();
        System.out.println("\033[33m              👤 用户中心 (｡◕‿◕｡) 👤\033[0m");
        System.out.println("\033[33m         ════════════════════════════\033[0m");
        System.out.println("\033[32m           🚀 用户服务启动中... 🚀\033[0m");
        System.out.println();
        System.out.println("========================================");
        System.out.println("🌟 \033[1;34mBilibili 用户信息管理服务\033[0m 🌟");
        System.out.println("========================================");
    }

    private static void printSuccessInfo() {
        System.out.println();
        System.out.println("\033[32m           🎉 用户服务启动成功！ 🎉\033[0m");
        System.out.println("========================================");
        System.out.println("🔧 服务地址: \033[1;32mhttp://localhost:8091\033[0m");
        System.out.println("📖 API文档: \033[1;32mhttp://localhost:8091/doc.html\033[0m");
        System.out.println("🌟 健康检查: \033[1;32mhttp://localhost:8091/actuator/health\033[0m");
        System.out.println("📊 数据库监控: \033[1;32mhttp://localhost:8091/druid\033[0m");
        System.out.println("🌐 Nacos控制台: \033[1;32mhttp://***********:8848/nacos\033[0m");
        System.out.println("========================================");
        System.out.println("\033[1;36m🎯 环境: 生产环境 (连接远程Nacos)\033[0m");
        System.out.println("\033[1;33m⚡ 服务: bilibili-user-service\033[0m");
        System.out.println("\033[1;35m🔥 版本: v1.0.0\033[0m");
        System.out.println("========================================");
    }
}
