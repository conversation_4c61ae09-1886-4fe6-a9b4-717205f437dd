package com.bilibili.user.controller;

import com.bilibili.common.dto.PageQuery;
import com.bilibili.common.dto.PageResult;
import com.bilibili.common.response.ApiResponse;
import com.bilibili.user.entity.vo.CoinBalanceVO;
import com.bilibili.user.entity.vo.CoinRecordVO;
import com.bilibili.user.service.CoinService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 硬币管理控制器
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/user/coin")
@RequiredArgsConstructor
@Tag(name = "硬币管理", description = "硬币相关接口")
public class CoinController {

    private final CoinService coinService;

    @Operation(summary = "获取硬币余额", description = "获取用户硬币余额信息")
    @GetMapping("/balance")
    public ApiResponse<CoinBalanceVO> getCoinBalance(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("获取硬币余额: uid={}", uid);
        try {
            CoinBalanceVO balance = coinService.getCoinBalance(uid);
            return ApiResponse.success("获取成功", balance);
        } catch (Exception e) {
            log.error("获取硬币余额失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取硬币记录", description = "获取用户硬币收支记录")
    @GetMapping("/records")
    public ApiResponse<PageResult<CoinRecordVO>> getCoinRecords(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @ModelAttribute PageQuery pageQuery,
            @Parameter(description = "记录类型") @RequestParam(required = false) String type,
            @Parameter(description = "开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) String endTime) {
        
        log.info("获取硬币记录: uid={}, type={}", uid, type);
        try {
            PageResult<CoinRecordVO> records = coinService.getCoinRecords(uid, pageQuery, type, startTime, endTime);
            return ApiResponse.success("获取成功", records);
        } catch (Exception e) {
            log.error("获取硬币记录失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(summary = "每日签到", description = "每日签到获取硬币")
    @PostMapping("/daily-checkin")
    public ApiResponse<CoinBalanceVO> dailyCheckin(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("每日签到: uid={}", uid);
        try {
            CoinBalanceVO balance = coinService.dailyCheckin(uid);
            return ApiResponse.success("签到成功", balance);
        } catch (Exception e) {
            log.error("每日签到失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("签到失败: " + e.getMessage());
        }
    }

    @Operation(summary = "投币给视频", description = "给视频投币")
    @PostMapping("/throw/{videoId}")
    public ApiResponse<CoinBalanceVO> throwCoin(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Parameter(description = "视频ID") @PathVariable Long videoId,
            @Parameter(description = "投币数量") @RequestParam Integer amount) {
        
        log.info("投币给视频: uid={}, videoId={}, amount={}", uid, videoId, amount);
        try {
            CoinBalanceVO balance = coinService.throwCoin(uid, videoId, amount);
            return ApiResponse.success("投币成功", balance);
        } catch (Exception e) {
            log.error("投币失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("投币失败: " + e.getMessage());
        }
    }
}
