package com.bilibili.user.controller;

import com.bilibili.common.dto.PageQuery;
import com.bilibili.common.dto.PageResult;
import com.bilibili.common.response.ApiResponse;
import com.bilibili.user.entity.dto.AddBlacklistDTO;
import com.bilibili.user.entity.vo.BlacklistVO;
import com.bilibili.user.service.BlacklistService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 黑名单管理控制器
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/user/blacklist")
@RequiredArgsConstructor
@Tag(name = "黑名单管理", description = "黑名单相关接口")
public class BlacklistController {

    private final BlacklistService blacklistService;

    @Operation(summary = "获取黑名单列表", description = "分页获取用户黑名单列表")
    @GetMapping("/list")
    public ApiResponse<PageResult<BlacklistVO>> getBlacklistList(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @ModelAttribute PageQuery pageQuery) {
        
        log.info("获取黑名单列表: uid={}", uid);
        try {
            PageResult<BlacklistVO> blacklist = blacklistService.getBlacklistList(uid, pageQuery);
            return ApiResponse.success("获取成功", blacklist);
        } catch (Exception e) {
            log.error("获取黑名单列表失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(summary = "添加黑名单", description = "将用户添加到黑名单")
    @PostMapping("/add")
    public ApiResponse<Void> addToBlacklist(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @RequestBody AddBlacklistDTO addBlacklistDTO) {
        
        log.info("添加黑名单: uid={}, targetUid={}", uid, addBlacklistDTO.getTargetUid());
        try {
            blacklistService.addToBlacklist(uid, addBlacklistDTO);
            return ApiResponse.success("添加成功", null);
        } catch (Exception e) {
            log.error("添加黑名单失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("添加失败: " + e.getMessage());
        }
    }

    @Operation(summary = "移除黑名单", description = "将用户从黑名单中移除")
    @DeleteMapping("/remove/{targetUid}")
    public ApiResponse<Void> removeFromBlacklist(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Parameter(description = "目标用户ID") @PathVariable Long targetUid) {
        
        log.info("移除黑名单: uid={}, targetUid={}", uid, targetUid);
        try {
            blacklistService.removeFromBlacklist(uid, targetUid);
            return ApiResponse.success("移除成功", null);
        } catch (Exception e) {
            log.error("移除黑名单失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("移除失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查是否拉黑", description = "检查是否已将某用户拉黑")
    @GetMapping("/check/{targetUid}")
    public ApiResponse<Boolean> checkBlacklist(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Parameter(description = "目标用户ID") @PathVariable Long targetUid) {
        
        log.info("检查黑名单: uid={}, targetUid={}", uid, targetUid);
        try {
            boolean isBlocked = blacklistService.isBlocked(uid, targetUid);
            return ApiResponse.success("检查成功", isBlocked);
        } catch (Exception e) {
            log.error("检查黑名单失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("检查失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量移除黑名单", description = "批量移除黑名单用户")
    @DeleteMapping("/batch-remove")
    public ApiResponse<Void> batchRemoveFromBlacklist(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Parameter(description = "目标用户ID列表") @RequestBody Long[] targetUids) {
        
        log.info("批量移除黑名单: uid={}, count={}", uid, targetUids.length);
        try {
            blacklistService.batchRemoveFromBlacklist(uid, targetUids);
            return ApiResponse.success("批量移除成功", null);
        } catch (Exception e) {
            log.error("批量移除黑名单失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("批量移除失败: " + e.getMessage());
        }
    }
}
