package com.bilibili.user.controller;

import com.bilibili.common.response.ApiResponse;
import com.bilibili.user.entity.dto.VipPurchaseDTO;
import com.bilibili.user.entity.vo.VipInfoVO;
import com.bilibili.user.entity.vo.VipPrivilegeVO;
import com.bilibili.user.entity.vo.VipPackageVO;
import com.bilibili.user.service.VipService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 大会员管理控制器
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/user/vip")
@RequiredArgsConstructor
@Tag(name = "大会员管理", description = "大会员相关接口")
public class VipController {

    private final VipService vipService;

    @Operation(summary = "获取VIP信息", description = "获取用户VIP详细信息")
    @GetMapping("/info")
    public ApiResponse<VipInfoVO> getVipInfo(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("获取VIP信息: uid={}", uid);
        try {
            VipInfoVO vipInfo = vipService.getVipInfo(uid);
            return ApiResponse.success("获取成功", vipInfo);
        } catch (Exception e) {
            log.error("获取VIP信息失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取VIP特权", description = "获取VIP特权列表")
    @GetMapping("/privileges")
    public ApiResponse<List<VipPrivilegeVO>> getVipPrivileges(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("获取VIP特权: uid={}", uid);
        try {
            List<VipPrivilegeVO> privileges = vipService.getVipPrivileges(uid);
            return ApiResponse.success("获取成功", privileges);
        } catch (Exception e) {
            log.error("获取VIP特权失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(summary = "购买VIP", description = "购买或续费VIP")
    @PostMapping("/purchase")
    public ApiResponse<VipInfoVO> purchaseVip(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @RequestBody VipPurchaseDTO vipPurchaseDTO) {
        
        log.info("购买VIP: uid={}, type={}, duration={}", uid, vipPurchaseDTO.getVipType(), vipPurchaseDTO.getDuration());
        try {
            VipInfoVO vipInfo = vipService.purchaseVip(uid, vipPurchaseDTO);
            return ApiResponse.success("购买成功", vipInfo);
        } catch (Exception e) {
            log.error("购买VIP失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("购买失败: " + e.getMessage());
        }
    }

    @Operation(summary = "开启自动续费", description = "开启VIP自动续费")
    @PostMapping("/auto-renew/enable")
    public ApiResponse<Void> enableAutoRenew(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("开启自动续费: uid={}", uid);
        try {
            vipService.enableAutoRenew(uid);
            return ApiResponse.success("开启成功", null);
        } catch (Exception e) {
            log.error("开启自动续费失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("开启失败: " + e.getMessage());
        }
    }

    @Operation(summary = "关闭自动续费", description = "关闭VIP自动续费")
    @PostMapping("/auto-renew/disable")
    public ApiResponse<Void> disableAutoRenew(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("关闭自动续费: uid={}", uid);
        try {
            vipService.disableAutoRenew(uid);
            return ApiResponse.success("关闭成功", null);
        } catch (Exception e) {
            log.error("关闭自动续费失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("关闭失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取VIP套餐", description = "获取可购买的VIP套餐列表")
    @GetMapping("/packages")
    public ApiResponse<List<VipPackageVO>> getVipPackages() {
        
        log.info("获取VIP套餐列表");
        try {
            List<VipPackageVO> packages = vipService.getVipPackages();
            return ApiResponse.success("获取成功", packages);
        } catch (Exception e) {
            log.error("获取VIP套餐失败: error={}", e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查VIP状态", description = "检查用户VIP状态")
    @GetMapping("/status")
    public ApiResponse<Boolean> checkVipStatus(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("检查VIP状态: uid={}", uid);
        try {
            boolean isVip = vipService.isVip(uid);
            return ApiResponse.success("检查成功", isVip);
        } catch (Exception e) {
            log.error("检查VIP状态失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("检查失败: " + e.getMessage());
        }
    }
}
