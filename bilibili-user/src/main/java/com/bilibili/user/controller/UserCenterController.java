package com.bilibili.user.controller;

import com.bilibili.user.entity.dto.PrivacySettingsUpdateRequest;
import com.bilibili.user.entity.dto.UserProfileUpdateRequest;
import com.bilibili.user.entity.po.UserPrivacySettings;
import com.bilibili.user.entity.vo.UserProfileVO;
import com.bilibili.user.service.UserCenterService;
import com.bilibili.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 用户个人中心控制器
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/user/center")
@RequiredArgsConstructor
@Tag(name = "用户个人中心", description = "用户个人中心相关接口")
public class UserCenterController {

    private final UserCenterService userCenterService;

    @Operation(
        summary = "获取个人资料",
        description = "获取用户的个人资料信息，需要JWT认证",
        security = @SecurityRequirement(name = "BearerAuth")
    )
    @GetMapping("/profile")
    public ApiResponse<UserProfileVO> getUserProfile(
            @Parameter(description = "用户ID", required = false) @RequestHeader(value = "X-User-Id", required = false) Long uid,
            @Parameter(description = "查询的用户ID") @RequestParam(required = false) Long queryUid) {
        
        // 确定要查询的用户ID
        Long targetUid = queryUid != null ? queryUid : uid;

        if (targetUid == null) {
            return ApiResponse.error("请提供用户ID");
        }

        log.info("获取用户个人资料: targetUid={}", targetUid);
        try {
            UserProfileVO profile = userCenterService.getUserProfile(targetUid);
            return ApiResponse.success("获取成功", profile);
        } catch (Exception e) {
            log.error("获取用户个人资料失败: targetUid={}, error={}", targetUid, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(
        summary = "更新个人资料",
        description = "更新用户的个人资料信息，需要JWT认证",
        security = @SecurityRequirement(name = "BearerAuth")
    )
    @PutMapping("/profile")
    public ApiResponse<Void> updateUserProfile(
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @RequestBody UserProfileUpdateRequest request) {
        
        log.info("更新用户个人资料: uid={}, request={}", uid, request);
        try {
            boolean success = userCenterService.updateUserProfile(uid, request);
            if (success) {
                return ApiResponse.successMessage("更新成功");
            } else {
                return ApiResponse.<Void>error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户个人资料失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.<Void>error("更新失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取隐私设置", description = "获取用户的隐私设置")
    @GetMapping("/privacy")
    public ApiResponse<UserPrivacySettings> getPrivacySettings(
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("获取用户隐私设置: uid={}", uid);
        try {
            UserPrivacySettings settings = userCenterService.getPrivacySettings(uid);
            return ApiResponse.success("获取成功", settings);
        } catch (Exception e) {
            log.error("获取用户隐私设置失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新隐私设置", description = "更新用户的隐私设置")
    @PutMapping("/privacy")
    public ApiResponse<Void> updatePrivacySettings(
            @Parameter(description = "用户ID", required = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @RequestBody PrivacySettingsUpdateRequest request) {

        log.info("更新用户隐私设置: uid={}, request={}", uid, request);
        try {
            boolean success = userCenterService.updatePrivacySettings(uid, request);
            if (success) {
                return ApiResponse.successMessage("更新成功");
            } else {
                return ApiResponse.<Void>error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户隐私设置失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.<Void>error("更新失败: " + e.getMessage());
        }
    }


}
