package com.bilibili.user.controller;

import com.bilibili.common.response.ApiResponse;
import com.bilibili.user.entity.dto.ChangePasswordDTO;
import com.bilibili.user.entity.dto.BindPhoneDTO;
import com.bilibili.user.entity.dto.BindWechatDTO;
import com.bilibili.user.entity.dto.RealNameAuthDTO;
import com.bilibili.user.entity.vo.SecurityStatusVO;
import com.bilibili.user.service.AccountSecurityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 账号安全控制器
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/user/security")
@RequiredArgsConstructor
@Tag(name = "账号安全", description = "账号安全相关接口")
public class AccountSecurityController {

    private final AccountSecurityService accountSecurityService;

    @Operation(summary = "获取安全状态", description = "获取用户账号安全状态")
    @GetMapping("/status")
    public ApiResponse<SecurityStatusVO> getSecurityStatus(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("获取安全状态: uid={}", uid);
        try {
            SecurityStatusVO status = accountSecurityService.getSecurityStatus(uid);
            return ApiResponse.success("获取成功", status);
        } catch (Exception e) {
            log.error("获取安全状态失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(summary = "修改密码", description = "修改用户登录密码")
    @PutMapping("/password")
    public ApiResponse<Void> changePassword(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @RequestBody ChangePasswordDTO changePasswordDTO) {
        
        log.info("修改密码: uid={}", uid);
        try {
            accountSecurityService.changePassword(uid, changePasswordDTO);
            return ApiResponse.success("密码修改成功", null);
        } catch (Exception e) {
            log.error("修改密码失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("修改失败: " + e.getMessage());
        }
    }

    @Operation(summary = "绑定手机号", description = "绑定或更换手机号")
    @PostMapping("/phone/bind")
    public ApiResponse<Void> bindPhone(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @RequestBody BindPhoneDTO bindPhoneDTO) {
        
        log.info("绑定手机号: uid={}, phone={}", uid, bindPhoneDTO.getPhone());
        try {
            accountSecurityService.bindPhone(uid, bindPhoneDTO);
            return ApiResponse.success("手机号绑定成功", null);
        } catch (Exception e) {
            log.error("绑定手机号失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("绑定失败: " + e.getMessage());
        }
    }

    @Operation(summary = "解绑手机号", description = "解绑当前手机号")
    @DeleteMapping("/phone/unbind")
    public ApiResponse<Void> unbindPhone(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Parameter(description = "验证码") @RequestParam String code) {
        
        log.info("解绑手机号: uid={}", uid);
        try {
            accountSecurityService.unbindPhone(uid, code);
            return ApiResponse.success("手机号解绑成功", null);
        } catch (Exception e) {
            log.error("解绑手机号失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("解绑失败: " + e.getMessage());
        }
    }

    @Operation(summary = "绑定微信", description = "绑定微信账号")
    @PostMapping("/wechat/bind")
    public ApiResponse<Void> bindWechat(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @RequestBody BindWechatDTO bindWechatDTO) {
        
        log.info("绑定微信: uid={}", uid);
        try {
            accountSecurityService.bindWechat(uid, bindWechatDTO);
            return ApiResponse.success("微信绑定成功", null);
        } catch (Exception e) {
            log.error("绑定微信失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("绑定失败: " + e.getMessage());
        }
    }

    @Operation(summary = "解绑微信", description = "解绑微信账号")
    @DeleteMapping("/wechat/unbind")
    public ApiResponse<Void> unbindWechat(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Parameter(description = "验证码") @RequestParam String code) {
        
        log.info("解绑微信: uid={}", uid);
        try {
            accountSecurityService.unbindWechat(uid, code);
            return ApiResponse.success("微信解绑成功", null);
        } catch (Exception e) {
            log.error("解绑微信失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("解绑失败: " + e.getMessage());
        }
    }

    @Operation(summary = "实名认证", description = "提交实名认证信息")
    @PostMapping("/realname/auth")
    public ApiResponse<Void> realNameAuth(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @RequestBody RealNameAuthDTO realNameAuthDTO) {
        
        log.info("实名认证: uid={}", uid);
        try {
            accountSecurityService.realNameAuth(uid, realNameAuthDTO);
            return ApiResponse.success("实名认证提交成功，请等待审核", null);
        } catch (Exception e) {
            log.error("实名认证失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("认证失败: " + e.getMessage());
        }
    }
}
