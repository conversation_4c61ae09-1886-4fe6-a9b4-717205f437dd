package com.bilibili.user.controller;

import com.bilibili.common.response.ApiResponse;
import com.bilibili.user.entity.vo.PersonalCenterVO;
import com.bilibili.user.service.PersonalCenterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 个人中心控制器
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/user/center")
@RequiredArgsConstructor
@Tag(name = "个人中心", description = "个人中心相关接口")
public class PersonalCenterController {

    private final PersonalCenterService personalCenterService;

    @Operation(summary = "获取个人中心首页", description = "获取个人中心首页数据")
    @GetMapping("/home")
    public ApiResponse<PersonalCenterVO> getPersonalCenterHome(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid) {
        
        log.info("获取个人中心首页: uid={}", uid);
        try {
            PersonalCenterVO centerData = personalCenterService.getPersonalCenterHome(uid);
            return ApiResponse.success("获取成功", centerData);
        } catch (Exception e) {
            log.error("获取个人中心首页失败: uid={}, error={}", uid, e.getMessage(), e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }
}
