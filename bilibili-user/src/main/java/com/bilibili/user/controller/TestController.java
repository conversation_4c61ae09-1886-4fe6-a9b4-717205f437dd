package com.bilibili.user.controller;

import com.bilibili.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Tag(name = "系统测试", description = "系统测试相关接口")
@RestController
@RequestMapping("/test")
public class TestController {

    @Operation(summary = "健康检查", description = "检查用户服务是否正常运行")
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "bilibili-user-service");
        result.put("status", "UP");
        result.put("timestamp", LocalDateTime.now());
        result.put("version", "v1.0.0");
        
        return ApiResponse.success("用户服务运行正常", result);
    }

    @Operation(summary = "获取服务信息", description = "获取用户服务的详细信息")
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> info() {
        Map<String, Object> result = new HashMap<>();
        result.put("serviceName", "bilibili-user-service");
        result.put("port", 8091);
        result.put("description", "Bilibili用户服务");
        result.put("features", new String[]{"用户管理", "个人中心", "VIP管理", "硬币系统"});
        result.put("timestamp", LocalDateTime.now());
        
        return ApiResponse.success("获取服务信息成功", result);
    }
}
