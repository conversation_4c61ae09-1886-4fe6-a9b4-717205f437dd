package com.bilibili.user.feign;

import org.springframework.cloud.openfeign.FeignClient;

/**
 * 认证服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@FeignClient(name = "auth-service", path = "/auth")
public interface AuthServiceClient {

    // 暂时注释掉所有接口，因为我们使用网关进行JWT验证
    // 如果后续需要调用Auth服务，可以根据实际的Auth接口来添加

    // 注意：Auth模块的实际接口路径：
    // - 发送验证码: POST /send-code (body: {phone})
    // - 登录: POST /login-by-code, /login-by-password
    // - 刷新令牌: POST /refresh-token
    // - 重置密码: POST /password/reset
}
