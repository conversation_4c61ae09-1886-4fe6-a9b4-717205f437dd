package com.bilibili.user.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger/OpenAPI配置 - 用户服务
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Configuration
public class SwaggerConfig {

    private static final String SECURITY_SCHEME_NAME = "BearerAuth";

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                // 配置服务器地址，优先使用网关地址进行API测试
                .addServersItem(new Server().url("http://localhost:8080/api/v1")
                        .description("🌉 网关访问（推荐）- 统一入口"))
                .addServersItem(new Server().url("http://localhost:8091")
                        .description("🔗 直接访问用户服务 - 仅开发调试"))
                .info(new Info()
                        .title("🎬 Bilibili 用户服务API文档")
                        .description("""
                                ## 📖 API接口说明
                                
                                本文档提供Bilibili用户服务的完整API接口文档，包括：
                                
                                ### 👤 用户功能
                                - 获取当前用户信息
                                - 用户信息查询
                                - 用户统计数据
                                
                                ### 🌐 API访问方式
                                
                                **推荐通过网关访问（端口8080）**：
                                - 所有API路径：`/api/v1/user/**`
                                - 示例：`GET /api/v1/user/me`
                                
                                **直接访问用户服务（端口8082）**：
                                - 仅用于开发调试
                                - API路径：`/user/**`
                                - 示例：`GET /user/me`
                                
                                ### 🔐 认证说明
                                - 部分接口需要JWT Token认证
                                - 测试接口无需认证
                                - 在右上角"Authorize"按钮中输入：`Bearer {your_token}`
                                
                                ### 📝 测试建议
                                1. 使用"🌉 网关访问"服务器进行测试
                                2. 先通过认证服务获取JWT Token
                                3. 配置Token后测试需要认证的接口
                                """)
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("开发团队")
                                .email("<EMAIL>")
                                .url("https://github.com/your-repo"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .components(new Components()
                        .addSecuritySchemes(SECURITY_SCHEME_NAME,
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请输入JWT令牌，格式：Bearer {token}")))
                .addSecurityItem(new SecurityRequirement().addList(SECURITY_SCHEME_NAME));
    }
}
