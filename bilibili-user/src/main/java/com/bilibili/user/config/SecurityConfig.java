package com.bilibili.user.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Spring Security配置类 - 用户服务
 * 简化配置，主要用于CORS和基础安全策略
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    /**
     * 密码编码器Bean
     */
    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 配置安全过滤器链
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF保护
            .csrf(AbstractHttpConfigurer::disable)
            // 禁用框架保护
            .headers(headers -> headers.frameOptions().disable())
            // 禁用CORS，交由网关统一处理
            .cors(AbstractHttpConfigurer::disable)
            // 配置会话管理为无状态
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            // 配置授权规则 - 由于认证在网关处理，这里允许所有请求
            .authorizeHttpRequests(auth -> auth
                .anyRequest().permitAll()  // 允许所有请求，认证由网关处理
            )
            // 禁用HTTP Basic认证
            .httpBasic(AbstractHttpConfigurer::disable)
            // 禁用默认登录页面
            .formLogin(AbstractHttpConfigurer::disable);

        return http.build();
    }

    // 移除CORS配置源，交由网关统一处理
    // @Bean
    // public CorsConfigurationSource corsConfigurationSource() {
    //     // 已禁用，交由网关处理
    // }
}
