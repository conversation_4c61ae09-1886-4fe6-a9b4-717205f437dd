package com.bilibili.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.user.entity.po.UserActions;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户行为记录Mapper
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface UserActionsMapper extends BaseMapper<UserActions> {

    /**
     * 根据用户ID和行为类型查询最近行为记录
     *
     * @param uid        用户ID
     * @param actionType 行为类型
     * @param limit      限制数量
     * @return 行为记录列表
     */
    @Select("SELECT * FROM user_actions WHERE uid = #{uid} AND action_type = #{actionType} ORDER BY created_at DESC LIMIT #{limit}")
    List<UserActions> findRecentByUidAndActionType(@Param("uid") Long uid, 
                                                   @Param("actionType") Integer actionType, 
                                                   @Param("limit") Integer limit);

    /**
     * 根据用户ID查询最近行为记录
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 行为记录列表
     */
    @Select("SELECT * FROM user_actions WHERE uid = #{uid} ORDER BY created_at DESC LIMIT #{limit}")
    List<UserActions> findRecentByUid(@Param("uid") Long uid, @Param("limit") Integer limit);

    /**
     * 查询用户最近点赞的视频
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 点赞记录列表
     */
    @Select("SELECT * FROM user_actions WHERE uid = #{uid} AND action_type = 1 AND target_type = 1 ORDER BY created_at DESC LIMIT #{limit}")
    List<UserActions> findRecentLikes(@Param("uid") Long uid, @Param("limit") Integer limit);

    /**
     * 查询用户最近投币的视频
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 投币记录列表
     */
    @Select("SELECT * FROM user_actions WHERE uid = #{uid} AND action_type = 2 AND target_type = 1 ORDER BY created_at DESC LIMIT #{limit}")
    List<UserActions> findRecentCoins(@Param("uid") Long uid, @Param("limit") Integer limit);

    /**
     * 查询用户最近收藏的视频
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 收藏记录列表
     */
    @Select("SELECT * FROM user_actions WHERE uid = #{uid} AND action_type = 3 AND target_type = 1 ORDER BY created_at DESC LIMIT #{limit}")
    List<UserActions> findRecentFavorites(@Param("uid") Long uid, @Param("limit") Integer limit);

    /**
     * 查询用户最近观看的视频
     *
     * @param uid   用户ID
     * @param limit 限制数量
     * @return 观看记录列表
     */
    @Select("SELECT * FROM user_actions WHERE uid = #{uid} AND action_type = 5 AND target_type = 1 ORDER BY created_at DESC LIMIT #{limit}")
    List<UserActions> findRecentViews(@Param("uid") Long uid, @Param("limit") Integer limit);

    /**
     * 检查用户是否对目标执行过指定行为
     *
     * @param uid        用户ID
     * @param actionType 行为类型
     * @param targetType 目标类型
     * @param targetId   目标ID
     * @return 是否存在记录
     */
    @Select("SELECT COUNT(*) FROM user_actions WHERE uid = #{uid} AND action_type = #{actionType} AND target_type = #{targetType} AND target_id = #{targetId}")
    Integer checkActionExists(@Param("uid") Long uid, 
                             @Param("actionType") Integer actionType,
                             @Param("targetType") Integer targetType, 
                             @Param("targetId") Long targetId);

    /**
     * 统计用户指定行为的总数
     *
     * @param uid        用户ID
     * @param actionType 行为类型
     * @return 行为总数
     */
    @Select("SELECT COUNT(*) FROM user_actions WHERE uid = #{uid} AND action_type = #{actionType}")
    Integer countByUidAndActionType(@Param("uid") Long uid, @Param("actionType") Integer actionType);

    /**
     * 删除用户对指定目标的行为记录
     *
     * @param uid        用户ID
     * @param actionType 行为类型
     * @param targetType 目标类型
     * @param targetId   目标ID
     * @return 删除的记录数
     */
    @Select("DELETE FROM user_actions WHERE uid = #{uid} AND action_type = #{actionType} AND target_type = #{targetType} AND target_id = #{targetId}")
    Integer deleteAction(@Param("uid") Long uid, 
                        @Param("actionType") Integer actionType,
                        @Param("targetType") Integer targetType, 
                        @Param("targetId") Long targetId);
}
