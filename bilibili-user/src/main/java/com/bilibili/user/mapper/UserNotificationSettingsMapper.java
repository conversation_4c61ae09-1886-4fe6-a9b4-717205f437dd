package com.bilibili.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.user.entity.po.UserNotificationSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户通知设置Mapper
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface UserNotificationSettingsMapper extends BaseMapper<UserNotificationSettings> {

    /**
     * 根据用户ID查询通知设置
     *
     * @param uid 用户ID
     * @return 通知设置
     */
    @Select("SELECT * FROM user_notification_settings WHERE uid = #{uid}")
    UserNotificationSettings findByUid(@Param("uid") Long uid);
}
