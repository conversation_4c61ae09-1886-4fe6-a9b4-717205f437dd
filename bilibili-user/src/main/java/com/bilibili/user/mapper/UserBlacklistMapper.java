package com.bilibili.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.user.entity.po.UserBlacklist;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户黑名单Mapper
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface UserBlacklistMapper extends BaseMapper<UserBlacklist> {

    /**
     * 根据用户ID查询黑名单列表
     *
     * @param uid 用户ID
     * @return 黑名单列表
     */
    @Select("SELECT * FROM user_blacklist WHERE uid = #{uid} ORDER BY created_at DESC")
    List<UserBlacklist> findByUid(@Param("uid") Long uid);

    /**
     * 检查用户是否在黑名单中
     *
     * @param uid       用户ID
     * @param targetUid 目标用户ID
     * @return 是否在黑名单中
     */
    @Select("SELECT COUNT(*) FROM user_blacklist WHERE uid = #{uid} AND target_uid = #{targetUid}")
    Integer checkBlacklisted(@Param("uid") Long uid, @Param("targetUid") Long targetUid);

    /**
     * 根据用户ID和目标用户ID删除黑名单记录
     *
     * @param uid       用户ID
     * @param targetUid 目标用户ID
     * @return 删除的记录数
     */
    @Delete("DELETE FROM user_blacklist WHERE uid = #{uid} AND target_uid = #{targetUid}")
    Integer deleteByUidAndTargetUid(@Param("uid") Long uid, @Param("targetUid") Long targetUid);

    /**
     * 获取用户黑名单总数
     *
     * @param uid 用户ID
     * @return 黑名单总数
     */
    @Select("SELECT COUNT(*) FROM user_blacklist WHERE uid = #{uid}")
    Integer countByUid(@Param("uid") Long uid);

    /**
     * 检查是否互相拉黑
     *
     * @param uid1 用户1ID
     * @param uid2 用户2ID
     * @return 是否互相拉黑
     */
    @Select("SELECT COUNT(*) FROM user_blacklist WHERE (uid = #{uid1} AND target_uid = #{uid2}) OR (uid = #{uid2} AND target_uid = #{uid1})")
    Integer checkMutualBlacklist(@Param("uid1") Long uid1, @Param("uid2") Long uid2);
}
