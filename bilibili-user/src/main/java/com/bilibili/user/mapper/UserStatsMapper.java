package com.bilibili.user.mapper;

import com.bilibili.user.entity.po.UserStats;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 用户统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface UserStatsMapper extends BaseMapper<UserStats> {

    /**
     * 根据用户ID获取用户统计信息
     *
     * @param uid 用户ID
     * @return 用户统计信息
     */
    @Select("SELECT * FROM user_stats WHERE uid = #{uid}")
    UserStats selectByUid(@Param("uid") Long uid);

    /**
     * 增加关注数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET following_count = following_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseFollowingCount(@Param("uid") Long uid, @Param("count") Integer count);

    /**
     * 增加粉丝数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET follower_count = follower_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseFollowerCount(@Param("uid") Long uid, @Param("count") Integer count);

    /**
     * 增加视频数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET video_count = video_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseVideoCount(@Param("uid") Long uid, @Param("count") Integer count);

    /**
     * 增加播放数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET view_count = view_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseViewCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加点赞数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET like_count = like_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseLikeCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加投币数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET coin_count = coin_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseCoinCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加收藏数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET favorite_count = favorite_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseFavoriteCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加分享数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET share_count = share_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseShareCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加评论数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET comment_count = comment_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseCommentCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加弹幕数
     *
     * @param uid 用户ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET danmu_count = danmu_count + #{count}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseDanmuCount(@Param("uid") Long uid, @Param("count") Long count);

    /**
     * 增加直播时长
     *
     * @param uid 用户ID
     * @param seconds 增加秒数
     * @return 影响行数
     */
    @Update("UPDATE user_stats SET live_time = live_time + #{seconds}, updated_at = NOW() WHERE uid = #{uid}")
    int increaseLiveTime(@Param("uid") Long uid, @Param("seconds") Long seconds);
}
