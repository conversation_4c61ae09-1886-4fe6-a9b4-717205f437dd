package com.bilibili.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.user.entity.po.UserFavorites;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户收藏夹Mapper
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface UserFavoritesMapper extends BaseMapper<UserFavorites> {

    /**
     * 根据用户ID查询收藏夹列表
     *
     * @param uid 用户ID
     * @return 收藏夹列表
     */
    @Select("SELECT * FROM user_favorites WHERE uid = #{uid} ORDER BY sort_order ASC, created_at DESC")
    List<UserFavorites> findByUid(@Param("uid") Long uid);

    /**
     * 根据用户ID和类型查询收藏夹列表
     *
     * @param uid  用户ID
     * @param type 收藏夹类型
     * @return 收藏夹列表
     */
    @Select("SELECT * FROM user_favorites WHERE uid = #{uid} AND type = #{type} ORDER BY sort_order ASC, created_at DESC")
    List<UserFavorites> findByUidAndType(@Param("uid") Long uid, @Param("type") Integer type);

    /**
     * 根据用户ID查询公开的收藏夹列表
     *
     * @param uid 用户ID
     * @return 公开收藏夹列表
     */
    @Select("SELECT * FROM user_favorites WHERE uid = #{uid} AND is_public = 1 ORDER BY sort_order ASC, created_at DESC")
    List<UserFavorites> findPublicByUid(@Param("uid") Long uid);

    /**
     * 检查收藏夹是否属于指定用户
     *
     * @param favoriteId 收藏夹ID
     * @param uid        用户ID
     * @return 是否属于该用户
     */
    @Select("SELECT COUNT(*) FROM user_favorites WHERE id = #{favoriteId} AND uid = #{uid}")
    Integer checkOwnership(@Param("favoriteId") Long favoriteId, @Param("uid") Long uid);

    /**
     * 更新收藏夹媒体数量
     *
     * @param favoriteId 收藏夹ID
     * @param count      数量变化
     */
    @Update("UPDATE user_favorites SET media_count = media_count + #{count}, updated_at = NOW() WHERE id = #{favoriteId}")
    void updateMediaCount(@Param("favoriteId") Long favoriteId, @Param("count") Integer count);

    /**
     * 增加收藏夹浏览次数
     *
     * @param favoriteId 收藏夹ID
     */
    @Update("UPDATE user_favorites SET view_count = view_count + 1, updated_at = NOW() WHERE id = #{favoriteId}")
    void incrementViewCount(@Param("favoriteId") Long favoriteId);

    /**
     * 获取用户收藏夹总数
     *
     * @param uid 用户ID
     * @return 收藏夹总数
     */
    @Select("SELECT COUNT(*) FROM user_favorites WHERE uid = #{uid}")
    Integer countByUid(@Param("uid") Long uid);

    /**
     * 获取用户指定类型收藏夹总数
     *
     * @param uid  用户ID
     * @param type 收藏夹类型
     * @return 收藏夹总数
     */
    @Select("SELECT COUNT(*) FROM user_favorites WHERE uid = #{uid} AND type = #{type}")
    Integer countByUidAndType(@Param("uid") Long uid, @Param("type") Integer type);
}
