package com.bilibili.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bilibili.user.entity.po.UserPrivacySettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户隐私设置Mapper
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface UserPrivacySettingsMapper extends BaseMapper<UserPrivacySettings> {

    /**
     * 根据用户ID查询隐私设置
     *
     * @param uid 用户ID
     * @return 隐私设置
     */
    @Select("SELECT * FROM user_privacy_settings WHERE uid = #{uid}")
    UserPrivacySettings findByUid(@Param("uid") Long uid);

    /**
     * 检查用户是否公开收藏夹
     *
     * @param uid 用户ID
     * @return 是否公开
     */
    @Select("SELECT show_favorites FROM user_privacy_settings WHERE uid = #{uid}")
    Integer checkShowFavorites(@Param("uid") Long uid);

    /**
     * 检查用户是否公开追番
     *
     * @param uid 用户ID
     * @return 是否公开
     */
    @Select("SELECT show_bangumi FROM user_privacy_settings WHERE uid = #{uid}")
    Integer checkShowBangumi(@Param("uid") Long uid);

    /**
     * 检查用户是否公开投币记录
     *
     * @param uid 用户ID
     * @return 是否公开
     */
    @Select("SELECT show_coins FROM user_privacy_settings WHERE uid = #{uid}")
    Integer checkShowCoins(@Param("uid") Long uid);

    /**
     * 检查用户是否公开点赞记录
     *
     * @param uid 用户ID
     * @return 是否公开
     */
    @Select("SELECT show_likes FROM user_privacy_settings WHERE uid = #{uid}")
    Integer checkShowLikes(@Param("uid") Long uid);

    /**
     * 检查用户是否公开关注列表
     *
     * @param uid 用户ID
     * @return 是否公开
     */
    @Select("SELECT show_following FROM user_privacy_settings WHERE uid = #{uid}")
    Integer checkShowFollowing(@Param("uid") Long uid);

    /**
     * 检查用户是否公开粉丝列表
     *
     * @param uid 用户ID
     * @return 是否公开
     */
    @Select("SELECT show_followers FROM user_privacy_settings WHERE uid = #{uid}")
    Integer checkShowFollowers(@Param("uid") Long uid);
}
