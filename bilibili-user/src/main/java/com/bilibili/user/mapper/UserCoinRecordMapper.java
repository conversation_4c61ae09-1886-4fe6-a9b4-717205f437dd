package com.bilibili.user.mapper;

import com.bilibili.user.entity.po.UserCoinRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户硬币记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Mapper
public interface UserCoinRecordMapper extends BaseMapper<UserCoinRecord> {

    /**
     * 分页查询用户硬币记录
     */
    @Select("<script>" +
            "SELECT * FROM user_coin_records WHERE uid = #{uid} " +
            "<if test='changeType != null'> AND change_type = #{changeType} </if>" +
            "<if test='sourceType != null'> AND source_type = #{sourceType} </if>" +
            "<if test='startTime != null'> AND created_at >= #{startTime} </if>" +
            "<if test='endTime != null'> AND created_at &lt;= #{endTime} </if>" +
            "ORDER BY created_at DESC" +
            "</script>")
    IPage<UserCoinRecord> selectPageByCondition(
            Page<UserCoinRecord> page,
            @Param("uid") Long uid,
            @Param("changeType") Integer changeType,
            @Param("sourceType") Integer sourceType,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间段内的收入
     */
    @Select("SELECT COALESCE(SUM(change_amount), 0) FROM user_coin_records " +
            "WHERE uid = #{uid} AND change_type = 1 " +
            "AND created_at >= #{startTime} AND created_at <= #{endTime}")
    BigDecimal sumIncomeByPeriod(@Param("uid") Long uid,
                                 @Param("startTime") LocalDateTime startTime,
                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间段内的支出
     */
    @Select("SELECT COALESCE(SUM(change_amount), 0) FROM user_coin_records " +
            "WHERE uid = #{uid} AND change_type = 2 " +
            "AND created_at >= #{startTime} AND created_at <= #{endTime}")
    BigDecimal sumExpenseByPeriod(@Param("uid") Long uid,
                                  @Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 检查今日是否已签到
     */
    @Select("SELECT COUNT(*) > 0 FROM user_coin_records " +
            "WHERE uid = #{uid} AND source_type = 1 " +
            "AND DATE(created_at) = CURDATE()")
    boolean hasCheckedInToday(@Param("uid") Long uid);

    /**
     * 获取连续签到天数
     */
    @Select("SELECT COUNT(*) FROM (" +
            "SELECT DATE(created_at) as check_date FROM user_coin_records " +
            "WHERE uid = #{uid} AND source_type = 1 " +
            "AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) " +
            "GROUP BY DATE(created_at) " +
            "ORDER BY check_date DESC" +
            ") t")
    int getConsecutiveCheckinDays(@Param("uid") Long uid);

    /**
     * 检查是否已对视频投币
     */
    @Select("SELECT COUNT(*) > 0 FROM user_coin_records " +
            "WHERE uid = #{uid} AND source_type = 4 AND source_id = #{videoId} " +
            "AND DATE(created_at) = CURDATE()")
    boolean hasThrownCoinToVideo(@Param("uid") Long uid, @Param("videoId") Long videoId);
}
