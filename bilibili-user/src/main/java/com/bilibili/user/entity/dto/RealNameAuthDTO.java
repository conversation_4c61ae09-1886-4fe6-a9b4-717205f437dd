package com.bilibili.user.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 实名认证DTO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(description = "实名认证请求")
public class RealNameAuthDTO {

    @Schema(description = "真实姓名", example = "张三")
    @NotBlank(message = "真实姓名不能为空")
    @Size(min = 2, max = 20, message = "姓名长度必须在2-20个字符之间")
    private String realName;

    @Schema(description = "身份证号", example = "110101199001011234")
    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCard;

    @Schema(description = "身份证正面照片URL", example = "https://example.com/id_front.jpg")
    @NotBlank(message = "身份证正面照片不能为空")
    private String idCardFront;

    @Schema(description = "身份证反面照片URL", example = "https://example.com/id_back.jpg")
    @NotBlank(message = "身份证反面照片不能为空")
    private String idCardBack;

    @Schema(description = "手持身份证照片URL", example = "https://example.com/id_hand.jpg")
    @NotBlank(message = "手持身份证照片不能为空")
    private String idCardHand;
}
