package com.bilibili.user.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 硬币余额VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "硬币余额信息")
public class CoinBalanceVO {

    @Schema(description = "硬币余额")
    private BigDecimal balance;

    @Schema(description = "B币余额")
    private Integer bCoin;

    @Schema(description = "冻结金额")
    private BigDecimal frozenAmount;

    @Schema(description = "可用余额")
    private BigDecimal availableBalance;

    @Schema(description = "今日获得")
    private BigDecimal todayEarned;

    @Schema(description = "今日消费")
    private BigDecimal todaySpent;

    @Schema(description = "本月获得")
    private BigDecimal monthEarned;

    @Schema(description = "本月消费")
    private BigDecimal monthSpent;

    @Schema(description = "总收入")
    private BigDecimal totalIncome;

    @Schema(description = "总支出")
    private BigDecimal totalExpense;

    @Schema(description = "是否已签到")
    private Boolean checkedIn;

    @Schema(description = "连续签到天数")
    private Integer consecutiveDays;

    @Schema(description = "下次签到奖励")
    private BigDecimal nextCheckinReward;

    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;
}
