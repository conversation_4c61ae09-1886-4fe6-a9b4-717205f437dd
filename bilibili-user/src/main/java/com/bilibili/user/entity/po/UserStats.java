package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_stats")
public class UserStats implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 关注数
     */
    private Integer followingCount;

    /**
     * 粉丝数
     */
    private Integer followerCount;

    /**
     * 视频数
     */
    private Integer videoCount;

    /**
     * 总播放数
     */
    private Long viewCount;

    /**
     * 总点赞数
     */
    private Long likeCount;

    /**
     * 总投币数
     */
    private Long coinCount;

    /**
     * 总收藏数
     */
    private Long favoriteCount;

    /**
     * 总分享数
     */
    private Long shareCount;

    /**
     * 总评论数
     */
    private Long commentCount;

    /**
     * 总弹幕数
     */
    private Long danmuCount;

    /**
     * 直播时长(秒)
     */
    private Long liveTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
