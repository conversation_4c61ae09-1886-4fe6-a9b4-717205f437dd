package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户通知设置实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_notification_settings")
@Schema(name = "UserNotificationSettings", description = "用户通知设置表")
public class UserNotificationSettings implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    private Long uid;

    @Schema(description = "回复通知 0关闭 1开启")
    private Integer replyNotify;

    @Schema(description = "@我的通知 0关闭 1开启")
    private Integer atNotify;

    @Schema(description = "点赞通知 0关闭 1开启")
    private Integer likeNotify;

    @Schema(description = "关注通知 0关闭 1开启")
    private Integer followNotify;

    @Schema(description = "系统通知 0关闭 1开启")
    private Integer systemNotify;

    @Schema(description = "直播通知 0关闭 1开启")
    private Integer liveNotify;

    @Schema(description = "动态通知 0关闭 1开启")
    private Integer dynamicNotify;

    @Schema(description = "邮件通知 0关闭 1开启")
    private Integer emailNotify;

    @Schema(description = "短信通知 0关闭 1开启")
    private Integer smsNotify;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
