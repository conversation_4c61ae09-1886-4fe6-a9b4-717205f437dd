package com.bilibili.user.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * VIP购买DTO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(description = "VIP购买请求")
public class VipPurchaseDTO {

    @Schema(description = "VIP类型", example = "2", allowableValues = {"1", "2"})
    @NotNull(message = "VIP类型不能为空")
    @Min(value = 1, message = "VIP类型必须为1或2")
    @Max(value = 2, message = "VIP类型必须为1或2")
    private Integer vipType; // 1月会员 2年会员

    @Schema(description = "购买时长(月)", example = "12")
    @NotNull(message = "购买时长不能为空")
    @Min(value = 1, message = "购买时长至少1个月")
    @Max(value = 36, message = "购买时长最多36个月")
    private Integer duration;

    @Schema(description = "支付方式", example = "alipay", allowableValues = {"alipay", "wechat", "balance"})
    @NotNull(message = "支付方式不能为空")
    private String paymentMethod;

    @Schema(description = "是否开启自动续费", example = "false")
    private Boolean autoRenew = false;

    @Schema(description = "优惠券ID", example = "123456")
    private Long couponId;
}
