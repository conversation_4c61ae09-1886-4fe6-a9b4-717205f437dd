package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户隐私设置实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_privacy_settings")
@Schema(name = "UserPrivacySettings", description = "用户隐私设置表")
public class UserPrivacySettings implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    private Long uid;

    @Schema(description = "公开我的收藏 0否 1是")
    private Integer showFavorites;

    @Schema(description = "公开我的追番追剧 0否 1是")
    private Integer showBangumi;

    @Schema(description = "公开最近投币的视频 0否 1是")
    private Integer showCoins;

    @Schema(description = "公开最近点赞的视频 0否 1是")
    private Integer showLikes;

    @Schema(description = "公开我的关注列表 0否 1是")
    private Integer showFollowing;

    @Schema(description = "公开我的粉丝列表 0否 1是")
    private Integer showFollowers;

    @Schema(description = "公开我的生日、个人标签 0否 1是")
    private Integer showBirthday;

    @Schema(description = "公开学校信息 0否 1是")
    private Integer showSchool;

    @Schema(description = "公开最近玩过的游戏 0否 1是")
    private Integer showGames;

    @Schema(description = "公开佩戴的粉丝勋章 0否 1是")
    private Integer showFanBadge;

    @Schema(description = "公开我的追漫 0否 1是")
    private Integer showManga;

    @Schema(description = "公开拥有的粉丝装扮 0否 1是")
    private Integer showFanDress;

    @Schema(description = "投稿视频列表中展现直播回放 0否 1是")
    private Integer showLiveReplay;

    @Schema(description = "投稿视频列表中展现课堂视频 0否 1是")
    private Integer showClassVideo;

    @Schema(description = "投稿视频列表中展现包月充电专属视频 0否 1是")
    private Integer showChargeVideo;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
