package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户收藏夹实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_favorites")
@Schema(name = "UserFavorites", description = "用户收藏夹表")
public class UserFavorites implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "收藏夹ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    private Long uid;

    @Schema(description = "收藏夹标题")
    private String title;

    @Schema(description = "收藏夹描述")
    private String description;

    @Schema(description = "收藏夹封面")
    private String cover;

    @Schema(description = "是否公开 0私密 1公开")
    private Integer isPublic;

    @Schema(description = "收藏数量")
    private Integer mediaCount;

    @Schema(description = "浏览次数")
    private Integer viewCount;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "类型 1视频 2音频 3专栏 4相簿")
    private Integer type;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 收藏夹类型枚举
     */
    public enum FavoriteType {
        VIDEO(1, "视频"),
        AUDIO(2, "音频"),
        ARTICLE(3, "专栏"),
        ALBUM(4, "相簿");

        private final int code;
        private final String desc;

        FavoriteType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
