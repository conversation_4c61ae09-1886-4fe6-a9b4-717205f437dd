package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户硬币记录实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_coin_records")
@Schema(description = "用户硬币记录")
public class UserCoinRecord {

    @Schema(description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    @TableField("uid")
    private Long uid;

    @Schema(description = "变动类型")
    @TableField("change_type")
    private Integer changeType; // 1获得 2消费

    @Schema(description = "变动数量")
    @TableField("change_amount")
    private BigDecimal changeAmount;

    @Schema(description = "变动后余额")
    @TableField("balance_after")
    private BigDecimal balanceAfter;

    @Schema(description = "来源类型")
    @TableField("source_type")
    private Integer sourceType; // 1签到 2投稿 3充值 4投币 5打赏

    @Schema(description = "来源ID")
    @TableField("source_id")
    private Long sourceId;

    @Schema(description = "描述")
    @TableField("description")
    private String description;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 变动类型枚举
     */
    public enum ChangeType {
        EARN(1, "获得"),
        SPEND(2, "消费");

        private final int code;
        private final String desc;

        ChangeType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() { return code; }
        public String getDesc() { return desc; }
    }

    /**
     * 来源类型枚举
     */
    public enum SourceType {
        CHECKIN(1, "签到"),
        UPLOAD(2, "投稿"),
        RECHARGE(3, "充值"),
        THROW_COIN(4, "投币"),
        REWARD(5, "打赏"),
        TASK(6, "任务"),
        REFUND(7, "退款"),
        SYSTEM(8, "系统");

        private final int code;
        private final String desc;

        SourceType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() { return code; }
        public String getDesc() { return desc; }

        public static SourceType fromCode(int code) {
            for (SourceType type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return SYSTEM;
        }
    }
}
