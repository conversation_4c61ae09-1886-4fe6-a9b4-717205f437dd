package com.bilibili.user.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 绑定微信DTO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(description = "绑定微信请求")
public class BindWechatDTO {

    @Schema(description = "微信授权码", example = "wx_auth_code_123")
    @NotBlank(message = "微信授权码不能为空")
    private String code;

    @Schema(description = "状态参数", example = "bind_wechat")
    private String state;
}
