package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户黑名单实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_blacklist")
@Schema(name = "UserBlacklist", description = "用户黑名单表")
public class UserBlacklist implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    private Long uid;

    @Schema(description = "被拉黑用户ID")
    private Long targetUid;

    @Schema(description = "被拉黑用户名")
    private String targetUsername;

    @Schema(description = "被拉黑用户昵称")
    private String targetNickname;

    @Schema(description = "被拉黑用户头像")
    private String targetAvatar;

    @Schema(description = "拉黑原因")
    private String reason;

    @Schema(description = "拉黑时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
