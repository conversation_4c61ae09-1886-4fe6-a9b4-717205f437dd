package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户安全设置实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_security")
@Schema(description = "用户安全设置")
public class UserSecurity {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    @TableField("uid")
    private Long uid;

    @Schema(description = "是否开启双因子认证")
    @TableField("two_factor_enabled")
    private Integer twoFactorEnabled;

    @Schema(description = "双因子认证密钥")
    @TableField("two_factor_secret")
    private String twoFactorSecret;

    @Schema(description = "登录保护")
    @TableField("login_protection")
    private Integer loginProtection;

    @Schema(description = "异地登录提醒")
    @TableField("remote_login_alert")
    private Integer remoteLoginAlert;

    @Schema(description = "密码强度")
    @TableField("password_strength")
    private Integer passwordStrength;

    @Schema(description = "最后修改密码时间")
    @TableField("last_password_change")
    private LocalDateTime lastPasswordChange;

    @Schema(description = "安全问题1")
    @TableField("security_question1")
    private String securityQuestion1;

    @Schema(description = "安全答案1")
    @TableField("security_answer1")
    private String securityAnswer1;

    @Schema(description = "安全问题2")
    @TableField("security_question2")
    private String securityQuestion2;

    @Schema(description = "安全答案2")
    @TableField("security_answer2")
    private String securityAnswer2;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @Schema(description = "逻辑删除标记")
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
}
