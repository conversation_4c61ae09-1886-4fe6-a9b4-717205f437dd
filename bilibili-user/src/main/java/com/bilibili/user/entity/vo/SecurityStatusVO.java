package com.bilibili.user.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 安全状态VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "账号安全状态")
public class SecurityStatusVO {

    @Schema(description = "密码安全")
    private PasswordSecurity passwordSecurity;

    @Schema(description = "手机绑定状态")
    private PhoneBinding phoneBinding;

    @Schema(description = "微信绑定状态")
    private WechatBinding wechatBinding;

    @Schema(description = "实名认证状态")
    private RealNameAuth realNameAuth;

    @Schema(description = "安全评分")
    private SecurityScore securityScore;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "密码安全")
    public static class PasswordSecurity {
        @Schema(description = "密码强度")
        private Integer strength; // 1弱 2中 3强

        @Schema(description = "最后修改时间")
        private LocalDateTime lastChangeTime;

        @Schema(description = "是否需要修改")
        private Boolean needChange;

        @Schema(description = "修改建议")
        private String suggestion;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "手机绑定状态")
    public static class PhoneBinding {
        @Schema(description = "是否已绑定")
        private Boolean bound;

        @Schema(description = "手机号（脱敏）")
        private String phoneNumber;

        @Schema(description = "绑定时间")
        private LocalDateTime bindTime;

        @Schema(description = "是否可以解绑")
        private Boolean canUnbind;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "微信绑定状态")
    public static class WechatBinding {
        @Schema(description = "是否已绑定")
        private Boolean bound;

        @Schema(description = "微信昵称")
        private String wechatNickname;

        @Schema(description = "微信头像")
        private String wechatAvatar;

        @Schema(description = "绑定时间")
        private LocalDateTime bindTime;

        @Schema(description = "是否可以解绑")
        private Boolean canUnbind;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "实名认证状态")
    public static class RealNameAuth {
        @Schema(description = "认证状态")
        private Integer status; // 0未认证 1审核中 2已认证 3认证失败

        @Schema(description = "真实姓名（脱敏）")
        private String realName;

        @Schema(description = "身份证号（脱敏）")
        private String idCard;

        @Schema(description = "认证时间")
        private LocalDateTime authTime;

        @Schema(description = "失败原因")
        private String failReason;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "安全评分")
    public static class SecurityScore {
        @Schema(description = "总分")
        private Integer totalScore;

        @Schema(description = "安全等级")
        private String level; // 低、中、高

        @Schema(description = "建议")
        private String[] suggestions;

        @Schema(description = "风险项")
        private String[] risks;
    }
}
