package com.bilibili.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户个人资料VO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Schema(description = "用户个人资料")
public class UserProfileVO {

    @Schema(description = "用户ID", example = "123456789")
    private Long uid;

    @Schema(description = "用户名", example = "user123")
    private String username;

    @Schema(description = "昵称", example = "小明同学")
    private String nickname;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "横幅URL", example = "https://example.com/banner.jpg")
    private String banner;

    @Schema(description = "性别 0未知 1男 2女", example = "1")
    private Integer gender;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "生日", example = "1995-01-01")
    private LocalDate birthday;

    @Schema(description = "个人签名", example = "这个人很懒，什么都没有留下")
    private String signature;

    @Schema(description = "位置", example = "北京市")
    private String location;

    @Schema(description = "学校信息", example = "清华大学")
    private String school;

    @Schema(description = "个人标签", example = "[\"程序员\", \"游戏爱好者\", \"二次元\"]")
    private List<String> personalTags;

    @Schema(description = "用户等级", example = "5")
    private Integer level;

    @Schema(description = "经验值", example = "12500")
    private Integer exp;

    @Schema(description = "硬币数量", example = "100.50")
    private Double coins;

    @Schema(description = "VIP类型 0无 1月度会员 2年度会员 3终身会员", example = "1")
    private Integer vipType;

    @Schema(description = "VIP状态 0非VIP 1VIP", example = "1")
    private Integer vipStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "VIP到期时间", example = "2024-12-31 23:59:59")
    private LocalDateTime vipDueDate;

    @Schema(description = "是否认证 0未认证 1已认证", example = "1")
    private Integer isVerified;

    @Schema(description = "认证类型 0无 1个人 2机构", example = "1")
    private Integer verifiedType;

    @Schema(description = "认证信息", example = "知名UP主")
    private String verifiedInfo;

    @Schema(description = "实名认证状态 0未认证 1已认证", example = "1")
    private Integer realNameVerified;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "注册时间", example = "2020-01-01 12:00:00")
    private LocalDateTime registerTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后登录时间", example = "2025-07-28 10:30:00")
    private LocalDateTime lastLoginTime;

    @Schema(description = "用户统计信息")
    private UserStatsVO stats;

    @Data
    @Schema(description = "用户统计信息")
    public static class UserStatsVO {
        @Schema(description = "关注数", example = "100")
        private Integer followingCount;

        @Schema(description = "粉丝数", example = "5000")
        private Integer followerCount;

        @Schema(description = "视频数", example = "50")
        private Integer videoCount;

        @Schema(description = "总播放数", example = "1000000")
        private Long viewCount;

        @Schema(description = "总点赞数", example = "50000")
        private Long likeCount;

        @Schema(description = "总投币数", example = "10000")
        private Long coinCount;

        @Schema(description = "总收藏数", example = "20000")
        private Long favoriteCount;

        @Schema(description = "总分享数", example = "5000")
        private Long shareCount;
    }
}
