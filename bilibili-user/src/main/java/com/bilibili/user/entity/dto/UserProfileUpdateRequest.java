package com.bilibili.user.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户个人信息更新请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Schema(description = "用户个人信息更新请求")
public class UserProfileUpdateRequest {

    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @Schema(description = "昵称", example = "小明同学")
    private String nickname;

    @Size(max = 255, message = "个人签名长度不能超过255个字符")
    @Schema(description = "个人签名", example = "这个人很懒，什么都没有留下")
    private String signature;

    @Schema(description = "性别 0未知 1男 2女", example = "1", allowableValues = {"0", "1", "2"})
    private Integer gender;

    @Schema(description = "生日", example = "1995-01-01")
    private LocalDate birthday;

    @Size(max = 100, message = "位置长度不能超过100个字符")
    @Schema(description = "位置", example = "北京市")
    private String location;

    @Size(max = 100, message = "学校信息长度不能超过100个字符")
    @Schema(description = "学校信息", example = "清华大学")
    private String school;

    @Schema(description = "个人标签", example = "[\"程序员\", \"游戏爱好者\", \"二次元\"]")
    private List<String> personalTags;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "横幅URL", example = "https://example.com/banner.jpg")
    private String banner;
}
