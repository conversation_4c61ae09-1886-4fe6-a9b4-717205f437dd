package com.bilibili.user.entity.vo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户信息响应VO")
public class UserInfoVO {

    @Schema(description = "用户唯一标识")
    private Long uid;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "横幅URL")
    private String banner;

    @Schema(description = "个人签名")
    private String signature;

    @Schema(description = "用户等级")
    private Integer level;

    @Schema(description = "经验值信息")
    private ExpInfo exp;

    @Schema(description = "硬币数量")
    private Double coins;

    @Schema(description = "关注数")
    private Integer followingCount;

    @Schema(description = "粉丝数")
    private Integer followerCount;

    @Schema(description = "VIP类型")
    private Integer vipType;

    @Schema(description = "VIP状态")
    private Integer vipStatus;

    @Schema(description = "统计信息")
    private Statistics statistics;

    @Data
    @Schema(description = "经验值信息")
    public static class ExpInfo {
        @Schema(description = "当前经验值")
        private Integer current;

        @Schema(description = "下一等级所需经验值")
        private Integer nextLevel;
    }

    @Data
    @Schema(description = "用户统计信息")
    public static class Statistics {
        @Schema(description = "视频数量")
        private Integer videoCount;

        @Schema(description = "总播放量")
        private Long viewCount;

        @Schema(description = "总点赞数")
        private Long likeCount;
    }
}
