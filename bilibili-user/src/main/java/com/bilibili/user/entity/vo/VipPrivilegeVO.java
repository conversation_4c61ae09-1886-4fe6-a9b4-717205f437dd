package com.bilibili.user.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VIP特权VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "VIP特权信息")
public class VipPrivilegeVO {

    @Schema(description = "特权ID")
    private String privilegeId;

    @Schema(description = "特权名称")
    private String name;

    @Schema(description = "特权描述")
    private String description;

    @Schema(description = "特权图标")
    private String icon;

    @Schema(description = "是否已解锁")
    private Boolean unlocked;

    @Schema(description = "所需VIP等级")
    private Integer requiredVipLevel;

    @Schema(description = "特权类型")
    private String type; // basic基础 premium高级 exclusive专享

    @Schema(description = "使用次数限制")
    private Integer usageLimit;

    @Schema(description = "已使用次数")
    private Integer usedCount;

    @Schema(description = "剩余使用次数")
    private Integer remainingCount;

    @Schema(description = "特权状态")
    private String status; // active激活 inactive未激活 expired已过期

    @Schema(description = "特权详情URL")
    private String detailUrl;
}
