package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户余额实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_balance")
@Schema(description = "用户余额信息")
public class UserBalance {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    @TableField("uid")
    private Long uid;

    @Schema(description = "硬币余额")
    @TableField("balance")
    private BigDecimal balance;

    @Schema(description = "B币余额")
    @TableField("b_coin")
    private Integer bCoin;

    @Schema(description = "冻结金额")
    @TableField("frozen_amount")
    private BigDecimal frozenAmount;

    @Schema(description = "总收入")
    @TableField("total_income")
    private BigDecimal totalIncome;

    @Schema(description = "总支出")
    @TableField("total_expense")
    private BigDecimal totalExpense;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @Schema(description = "逻辑删除标记")
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
}
