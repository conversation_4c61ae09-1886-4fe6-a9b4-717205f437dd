package com.bilibili.user.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 绑定手机号DTO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(description = "绑定手机号请求")
public class BindPhoneDTO {

    @Schema(description = "手机号", example = "13800138000")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "验证码", example = "123456")
    @NotBlank(message = "验证码不能为空")
    @Size(min = 6, max = 6, message = "验证码必须是6位")
    private String verificationCode;

    @Schema(description = "原手机号验证码（更换手机号时需要）", example = "654321")
    private String oldPhoneCode;
}
