package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户行为记录实体
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_actions")
@Schema(name = "UserActions", description = "用户行为记录表")
public class UserActions implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    private Long uid;

    @Schema(description = "行为类型 1点赞 2投币 3收藏 4分享 5观看")
    private Integer actionType;

    @Schema(description = "目标类型 1视频 2动态 3专栏 4音频")
    private Integer targetType;

    @Schema(description = "目标ID")
    private Long targetId;

    @Schema(description = "目标标题")
    private String targetTitle;

    @Schema(description = "目标作者")
    private String targetAuthor;

    @Schema(description = "目标封面")
    private String targetCover;

    @Schema(description = "额外数据")
    private String extraData;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 行为类型枚举
     */
    public enum ActionType {
        LIKE(1, "点赞"),
        COIN(2, "投币"),
        FAVORITE(3, "收藏"),
        SHARE(4, "分享"),
        VIEW(5, "观看");

        private final int code;
        private final String desc;

        ActionType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 目标类型枚举
     */
    public enum TargetType {
        VIDEO(1, "视频"),
        DYNAMIC(2, "动态"),
        ARTICLE(3, "专栏"),
        AUDIO(4, "音频");

        private final int code;
        private final String desc;

        TargetType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
