package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户等级表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_levels")
public class UserLevels implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer level;

    /**
     * 等级名称
     */
    private String name;

    /**
     * 最小经验值
     */
    private Integer minExp;

    /**
     * 最大经验值
     */
    private Integer maxExp;

    /**
     * 等级颜色
     */
    private String color;

    /**
     * 等级图标
     */
    private String icon;

    /**
     * 等级特权
     */
    private String privileges;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    private String description;

    private Integer exp;

    private String levelName;

    private Integer nextLevelExp;

    private Integer status;

    private Long uid;


}
