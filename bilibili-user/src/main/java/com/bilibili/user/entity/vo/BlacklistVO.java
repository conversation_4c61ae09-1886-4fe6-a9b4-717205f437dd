package com.bilibili.user.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 黑名单VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "黑名单信息")
public class BlacklistVO {

    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "目标用户ID")
    private Long targetUid;

    @Schema(description = "目标用户名")
    private String targetUsername;

    @Schema(description = "目标用户昵称")
    private String targetNickname;

    @Schema(description = "目标用户头像")
    private String targetAvatar;

    @Schema(description = "拉黑原因")
    private String reason;

    @Schema(description = "拉黑时间")
    private LocalDateTime createdAt;

    @Schema(description = "是否在线")
    private Boolean isOnline;

    @Schema(description = "最后活跃时间")
    private LocalDateTime lastActiveTime;
}
