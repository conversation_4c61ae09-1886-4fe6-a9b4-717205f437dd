package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户VIP信息实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_vip")
@Schema(description = "用户VIP信息")
public class UserVip {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    @TableField("uid")
    private Long uid;

    @Schema(description = "VIP类型")
    @TableField("vip_type")
    private Integer vipType;

    @Schema(description = "VIP状态")
    @TableField("vip_status")
    private Integer vipStatus;

    @Schema(description = "VIP开始时间")
    @TableField("vip_start_time")
    private LocalDateTime vipStartTime;

    @Schema(description = "VIP结束时间")
    @TableField("vip_end_time")
    private LocalDateTime vipEndTime;

    @Schema(description = "VIP购买金额")
    @TableField("vip_amount")
    private BigDecimal vipAmount;

    @Schema(description = "VIP购买时间")
    @TableField("vip_purchase_time")
    private LocalDateTime vipPurchaseTime;

    @Schema(description = "自动续费状态")
    @TableField("auto_renew")
    private Integer autoRenew;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @Schema(description = "逻辑删除标记")
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
}
