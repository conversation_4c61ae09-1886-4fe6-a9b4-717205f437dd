package com.bilibili.user.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * VIP套餐VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "VIP套餐信息")
public class VipPackageVO {

    @Schema(description = "套餐ID")
    private String packageId;

    @Schema(description = "套餐名称")
    private String name;

    @Schema(description = "套餐描述")
    private String description;

    @Schema(description = "VIP类型")
    private Integer vipType; // 1月会员 2年会员

    @Schema(description = "时长(月)")
    private Integer duration;

    @Schema(description = "原价")
    private BigDecimal originalPrice;

    @Schema(description = "现价")
    private BigDecimal currentPrice;

    @Schema(description = "折扣")
    private BigDecimal discount;

    @Schema(description = "是否推荐")
    private Boolean recommended;

    @Schema(description = "是否热门")
    private Boolean popular;

    @Schema(description = "标签")
    private String[] tags;

    @Schema(description = "特权列表")
    private String[] privileges;

    @Schema(description = "套餐图标")
    private String icon;

    @Schema(description = "套餐颜色")
    private String color;

    @Schema(description = "是否可购买")
    private Boolean available;

    @Schema(description = "限购数量")
    private Integer purchaseLimit;

    @Schema(description = "有效期至")
    private String validUntil;
}
