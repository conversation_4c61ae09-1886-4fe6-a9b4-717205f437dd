package com.bilibili.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("users")
@Schema(name = "Users", description = "用户基础信息表")
public class Users implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户唯一标识")
    private Long uid;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "密码哈希")
    private String passwordHash;

    @Schema(description = "盐值")
    private String salt;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "横幅URL")
    private String banner;

    @Schema(description = "性别 0未知 1男 2女")
    private Integer gender;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Schema(description = "个人签名")
    private String signature;

    @Schema(description = "位置")
    private String location;

    @Schema(description = "用户等级")
    private Integer level;

    @Schema(description = "经验值")
    private Integer exp;

    @Schema(description = "硬币数量")
    private Double coins;

    @Schema(description = "VIP类型 0无 1月度会员 2年度会员 3终身会员")
    private Integer vipType;

    @Schema(description = "VIP状态 0非VIP 1VIP")
    private Integer vipStatus;

    @Schema(description = "VIP到期时间")
    private LocalDateTime vipDueDate;

    @Schema(description = "用户状态 0正常 1封禁 2注销")
    private Integer status;

    @Schema(description = "是否认证 0未认证 1已认证")
    private Integer isVerified;

    @Schema(description = "认证类型 0无 1个人 2机构")
    private Integer verifiedType;

    @Schema(description = "认证信息")
    private String verifiedInfo;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    private LocalDateTime deletedAt;

    @Schema(description = "手机号是否已绑定")
    private Boolean phoneBound;

    @Schema(description = "微信OpenId")
    private String wechatOpenId;

    @Schema(description = "微信UnionId")
    private String wechatUnionId;

    @Schema(description = "学校信息")
    private String school;

    @Schema(description = "个人标签")
    private String personalTags;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "实名认证状态 0未认证 1已认证")
    private Integer realNameVerified;

}
