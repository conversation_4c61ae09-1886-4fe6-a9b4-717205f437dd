package com.bilibili.user.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 添加黑名单DTO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(description = "添加黑名单请求")
public class AddBlacklistDTO {

    @Schema(description = "目标用户ID", example = "123456")
    @NotNull(message = "目标用户ID不能为空")
    private Long targetUid;

    @Schema(description = "拉黑原因", example = "恶意评论")
    @Size(max = 200, message = "拉黑原因不能超过200个字符")
    private String reason;
}
