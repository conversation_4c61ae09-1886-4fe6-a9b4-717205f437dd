package com.bilibili.user.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * VIP信息VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "VIP信息")
public class VipInfoVO {

    @Schema(description = "是否为VIP")
    private Boolean isVip;

    @Schema(description = "VIP类型")
    private Integer vipType; // 0普通 1月会员 2年会员

    @Schema(description = "VIP类型描述")
    private String vipTypeDesc;

    @Schema(description = "VIP状态")
    private Integer vipStatus; // 0无效 1有效 2已过期

    @Schema(description = "VIP状态描述")
    private String vipStatusDesc;

    @Schema(description = "VIP开始时间")
    private LocalDateTime vipStartTime;

    @Schema(description = "VIP结束时间")
    private LocalDateTime vipEndTime;

    @Schema(description = "剩余天数")
    private Long remainingDays;

    @Schema(description = "是否即将到期")
    private Boolean nearExpiry;

    @Schema(description = "是否开启自动续费")
    private Boolean autoRenew;

    @Schema(description = "累计购买金额")
    private BigDecimal totalAmount;

    @Schema(description = "累计购买次数")
    private Integer purchaseCount;

    @Schema(description = "VIP等级")
    private Integer vipLevel;

    @Schema(description = "VIP积分")
    private Integer vipPoints;

    @Schema(description = "下次续费金额")
    private BigDecimal nextRenewAmount;

    @Schema(description = "下次续费时间")
    private LocalDateTime nextRenewTime;

    @Schema(description = "VIP徽章")
    private String vipBadge;

    @Schema(description = "专属客服")
    private Boolean exclusiveService;

    @Schema(description = "免广告")
    private Boolean adFree;

    @Schema(description = "高清画质")
    private Boolean hdQuality;

    @Schema(description = "离线下载")
    private Boolean offlineDownload;

    @Schema(description = "会员专享内容")
    private Boolean exclusiveContent;
}
