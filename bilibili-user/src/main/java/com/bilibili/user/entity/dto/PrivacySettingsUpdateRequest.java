package com.bilibili.user.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 隐私设置更新请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Schema(description = "隐私设置更新请求")
public class PrivacySettingsUpdateRequest {

    @Schema(description = "公开我的收藏 0否 1是", example = "1")
    private Integer showFavorites;

    @Schema(description = "公开我的追番追剧 0否 1是", example = "1")
    private Integer showBangumi;

    @Schema(description = "公开最近投币的视频 0否 1是", example = "1")
    private Integer showCoins;

    @Schema(description = "公开最近点赞的视频 0否 1是", example = "1")
    private Integer showLikes;

    @Schema(description = "公开我的关注列表 0否 1是", example = "1")
    private Integer showFollowing;

    @Schema(description = "公开我的粉丝列表 0否 1是", example = "1")
    private Integer showFollowers;

    @Schema(description = "公开我的生日、个人标签 0否 1是", example = "1")
    private Integer showBirthday;

    @Schema(description = "公开学校信息 0否 1是", example = "1")
    private Integer showSchool;

    @Schema(description = "公开最近玩过的游戏 0否 1是", example = "1")
    private Integer showGames;

    @Schema(description = "公开佩戴的粉丝勋章 0否 1是", example = "1")
    private Integer showFanBadge;

    @Schema(description = "公开我的追漫 0否 1是", example = "1")
    private Integer showManga;

    @Schema(description = "公开拥有的粉丝装扮 0否 1是", example = "1")
    private Integer showFanDress;

    @Schema(description = "投稿视频列表中展现直播回放 0否 1是", example = "1")
    private Integer showLiveReplay;

    @Schema(description = "投稿视频列表中展现课堂视频 0否 1是", example = "1")
    private Integer showClassVideo;

    @Schema(description = "投稿视频列表中展现包月充电专属视频 0否 1是", example = "1")
    private Integer showChargeVideo;
}
