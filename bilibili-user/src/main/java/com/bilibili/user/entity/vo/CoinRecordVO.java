package com.bilibili.user.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 硬币记录VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "硬币记录")
public class CoinRecordVO {

    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "交易类型")
    private String type;

    @Schema(description = "交易类型描述")
    private String typeDesc;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "余额")
    private BigDecimal balance;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "关联对象ID")
    private Long relatedId;

    @Schema(description = "关联对象类型")
    private String relatedType;

    @Schema(description = "关联对象标题")
    private String relatedTitle;

    @Schema(description = "交易状态")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "是否为收入")
    private Boolean isIncome;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "颜色")
    private String color;
}
