package com.bilibili.user.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 个人中心首页VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "个人中心首页数据")
public class PersonalCenterVO {

    @Schema(description = "用户基本信息")
    private UserBasicInfo userInfo;

    @Schema(description = "会员信息")
    private VipInfo vipInfo;

    @Schema(description = "硬币信息")
    private CoinInfo coinInfo;

    @Schema(description = "统计信息")
    private StatsInfo statsInfo;

    @Schema(description = "安全信息")
    private SecurityInfo securityInfo;

    @Schema(description = "快捷功能")
    private QuickActions quickActions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户基本信息")
    public static class UserBasicInfo {
        @Schema(description = "用户ID")
        private Long uid;
        
        @Schema(description = "用户名")
        private String username;
        
        @Schema(description = "昵称")
        private String nickname;
        
        @Schema(description = "头像")
        private String avatar;
        
        @Schema(description = "等级")
        private Integer level;
        
        @Schema(description = "经验值")
        private Integer exp;
        
        @Schema(description = "是否实名认证")
        private Boolean realNameVerified;
        
        @Schema(description = "认证信息")
        private String verifiedInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "会员信息")
    public static class VipInfo {
        @Schema(description = "VIP类型")
        private Integer vipType;
        
        @Schema(description = "VIP状态")
        private Integer vipStatus;
        
        @Schema(description = "VIP到期时间")
        private LocalDateTime vipDueDate;
        
        @Schema(description = "VIP天数")
        private Long vipDays;
        
        @Schema(description = "是否即将到期")
        private Boolean nearExpiry;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "硬币信息")
    public static class CoinInfo {
        @Schema(description = "硬币余额")
        private Double coins;
        
        @Schema(description = "B币余额")
        private Integer bCoin;
        
        @Schema(description = "今日获得硬币")
        private Double todayEarned;
        
        @Schema(description = "今日消费硬币")
        private Double todaySpent;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "统计信息")
    public static class StatsInfo {
        @Schema(description = "关注数")
        private Integer followingCount;
        
        @Schema(description = "粉丝数")
        private Integer followerCount;
        
        @Schema(description = "获赞数")
        private Long likeCount;
        
        @Schema(description = "播放数")
        private Long viewCount;
        
        @Schema(description = "视频数")
        private Integer videoCount;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "安全信息")
    public static class SecurityInfo {
        @Schema(description = "是否绑定手机")
        private Boolean phoneBound;
        
        @Schema(description = "是否绑定邮箱")
        private Boolean emailBound;
        
        @Schema(description = "是否开启双因子认证")
        private Boolean twoFactorEnabled;
        
        @Schema(description = "最后登录时间")
        private LocalDateTime lastLoginTime;
        
        @Schema(description = "最后登录IP")
        private String lastLoginIp;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "快捷功能")
    public static class QuickActions {
        @Schema(description = "未读消息数")
        private Integer unreadMessages;
        
        @Schema(description = "待处理事项数")
        private Integer pendingTasks;
        
        @Schema(description = "新粉丝数")
        private Integer newFollowers;
        
        @Schema(description = "是否有系统通知")
        private Boolean hasSystemNotice;
    }
}
