# Bilibili用户服务主配置文件

# 应用配置
spring:
  application:
    name: user-service
  profiles:
    active: prod
  # Spring Boot 3.x Nacos配置导入方式
  config:
    import:
      - optional:nacos:bilibili-user-service.yml
      - optional:nacos:shared-spring.yaml
      - optional:nacos:shared-redis.yaml
      - optional:nacos:shared-mybatis.yaml
      - optional:nacos:shared-feign.yaml
      - optional:nacos:shared-mq.yaml
      - optional:nacos:shared-elasticsearch.yaml
      - optional:nacos:shared-security.yaml
      - optional:nacos:shared-monitoring.yaml
      # 暂时注释掉Swagger配置，使用本地配置
      # - optional:nacos:shared-swagger.yaml

  cloud:
    nacos:
      # Nacos Server地址
      server-addr: 8.156.72.38:8848
      # Nacos配置
      config:
        namespace: public
        group: DEFAULT_GROUP
        file-extension: yml
      # Nacos服务发现
      discovery:
        namespace: public
        group: DEFAULT_GROUP
        enabled: true

# 基础配置
server:
  port: 8091

# 本地Knife4j配置 - 最简版本
knife4j:
  enable: true
  setting:
    language: zh-CN

# SpringDoc配置 - 最简版本
springdoc:
  api-docs:
    enabled: true

# 日志配置 - 屏蔽LoadBalancer警告
logging:
  level:
    # 屏蔽Spring Cloud LoadBalancer的BeanPostProcessor警告
    org.springframework.beans.factory.support.DefaultListableBeanFactory$BeanPostProcessorChecker: ERROR
    org.springframework.context.annotation.ConfigurationClassPostProcessor$ImportAwareBeanPostProcessor: ERROR
