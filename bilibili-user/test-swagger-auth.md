# 🔧 Swagger JWT 认证测试指南

## 问题修复说明

已为用户服务的需要认证的接口添加了 `@SecurityRequirement(name = "BearerAuth")` 注解，现在 Knife4j 应该会显示认证输入框。

## 🚀 测试步骤

### 1. 重启用户服务
```bash
cd bilibili-user
mvn spring-boot:run
```

### 2. 访问 Knife4j 文档
- 直接访问用户服务：http://localhost:8091/doc.html
- 通过网关访问：http://localhost:8080/doc.html

### 3. 配置 JWT 认证

#### 方法一：使用右上角 Authorize 按钮
1. 点击页面右上角的 🔒 **Authorize** 按钮
2. 在弹出的对话框中输入：`Bearer 你的JWT令牌`
3. 点击 **Authorize** 确认

#### 方法二：在接口中直接输入
1. 找到 `/user/me` 接口
2. 点击 **Try it out**
3. 现在应该能看到 **Authorization** 输入框
4. 输入：`Bearer 你的JWT令牌`

### 4. 获取 JWT 令牌

如果还没有 JWT 令牌，先通过认证服务登录：

```bash
# 访问认证服务文档
http://localhost:8090/doc.html

# 使用密码登录接口
POST /auth/login-by-password
{
  "account": "你的手机号或用户名",
  "password": "你的密码",
  "remember": false
}

# 复制响应中的 accessToken
```

### 5. 测试认证接口

现在测试以下需要认证的接口：

#### ✅ 需要认证的接口
- `GET /user/me` - 获取当前用户信息
- `GET /user/center/profile` - 获取个人资料
- `PUT /user/center/profile` - 更新个人资料
- `GET /user/vip/info` - 获取VIP信息
- `GET /user/coin/balance` - 获取硬币余额
- `GET /user/security/status` - 获取安全状态

#### ⭕ 无需认证的接口
- `GET /user/{uid}` - 根据UID获取用户信息
- `GET /user/test/info` - 测试接口

## 🔍 验证要点

### 1. Swagger UI 显示
- ✅ 接口应该显示 🔒 锁图标
- ✅ 点击接口后应该有 Authorization 输入框
- ✅ 右上角应该有 Authorize 按钮

### 2. 接口调用
- ✅ 不带令牌调用应该返回 401 错误
- ✅ 带正确令牌调用应该返回正常数据
- ✅ 带过期令牌调用应该返回 401 错误

### 3. 响应格式
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "uid": 123456,
    "username": "user123",
    "nickname": "小明",
    // ... 其他用户信息
  },
  "timestamp": "2025-07-30T12:00:00"
}
```

## 🐛 如果仍然没有认证框

### 检查清单

1. **确认服务重启**：修改代码后必须重启服务
2. **清除浏览器缓存**：Ctrl+F5 强制刷新页面
3. **检查 Swagger 配置**：确认 SwaggerConfig 中的安全配置正确
4. **查看控制台错误**：检查浏览器开发者工具的控制台

### 备用方案

如果 Swagger UI 仍然有问题，可以使用以下方式测试：

#### 使用 curl 命令
```bash
# 获取当前用户信息
curl -X GET "http://localhost:8091/user/me" \
  -H "Authorization: Bearer 你的JWT令牌" \
  -H "Content-Type: application/json"

# 通过网关访问
curl -X GET "http://localhost:8080/api/v1/user/me" \
  -H "Authorization: Bearer 你的JWT令牌" \
  -H "Content-Type: application/json"
```

#### 使用 Postman
1. 创建新请求：`GET http://localhost:8091/user/me`
2. 在 Headers 中添加：
   - Key: `Authorization`
   - Value: `Bearer 你的JWT令牌`
3. 发送请求

## 📋 测试记录

请记录测试结果：

- [ ] Swagger UI 显示认证框
- [ ] 右上角 Authorize 按钮可用
- [ ] 不带令牌返回 401
- [ ] 带令牌返回正常数据
- [ ] 通过网关访问正常
- [ ] 直接访问服务正常

## 🎯 预期结果

修复后，您应该能够：
1. 在 Knife4j 中看到需要认证的接口有 🔒 图标
2. 点击接口后看到 Authorization 输入框
3. 输入 JWT 令牌后成功调用接口
4. 获取到正确的用户信息

如果还有问题，请告诉我具体的错误信息！
